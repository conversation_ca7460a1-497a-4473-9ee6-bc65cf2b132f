#!/usr/bin/env python3
"""
🔍 MEMORY AIMBOT DEBUG SYSTEM
============================
Comprehensive debugging and verification for memory injection aimbot
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
import time
import json
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from collections import deque
import logging

class MemoryAimbotDebugger:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 Memory Aimbot Debugger")
        self.root.geometry("1200x800")
        self.root.configure(bg='#0a0a0a')
        
        # Debug data storage
        self.debug_data = {
            'memory_reads': deque(maxlen=1000),
            'target_detections': deque(maxlen=100),
            'aim_calculations': deque(maxlen=100),
            'memory_writes': deque(maxlen=100),
            'performance_metrics': deque(maxlen=200)
        }
        
        # Status tracking
        self.is_monitoring = False
        self.last_update = time.time()
        
        # Setup logging
        self.setup_logging()
        
        # Create GUI
        self.create_widgets()
        self.start_monitoring()
        
    def setup_logging(self):
        """Setup debug logging"""
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('memory_aimbot_debug.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('MemoryAimbotDebug')
        
    def create_widgets(self):
        """Create debug GUI widgets"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Tab 1: Real-time Status
        self.create_status_tab(notebook)
        
        # Tab 2: Memory Operations Log
        self.create_memory_log_tab(notebook)
        
        # Tab 3: Target Detection Visualization
        self.create_target_viz_tab(notebook)
        
        # Tab 4: Performance Metrics
        self.create_performance_tab(notebook)
        
        # Tab 5: Raw Data Inspector
        self.create_data_inspector_tab(notebook)
        
    def create_status_tab(self, notebook):
        """Create real-time status tab"""
        status_frame = ttk.Frame(notebook, padding="10")
        notebook.add(status_frame, text="📊 Real-time Status")
        
        # Status indicators
        indicators_frame = tk.LabelFrame(status_frame, text="🔍 Aimbot Status", 
                                       bg='#1a1a1a', fg='white', font=('Arial', 12, 'bold'))
        indicators_frame.pack(fill='x', pady=10)
        
        # Memory reading status
        self.memory_status = tk.Label(indicators_frame, text="💾 Memory Reading: INACTIVE", 
                                    bg='#1a1a1a', fg='red', font=('Arial', 11, 'bold'))
        self.memory_status.pack(anchor='w', padx=10, pady=5)
        
        # Target detection status
        self.target_status = tk.Label(indicators_frame, text="🎯 Target Detection: 0 targets", 
                                    bg='#1a1a1a', fg='orange', font=('Arial', 11, 'bold'))
        self.target_status.pack(anchor='w', padx=10, pady=5)
        
        # Aim calculation status
        self.aim_status = tk.Label(indicators_frame, text="🧮 Aim Calculation: IDLE", 
                                 bg='#1a1a1a', fg='gray', font=('Arial', 11, 'bold'))
        self.aim_status.pack(anchor='w', padx=10, pady=5)
        
        # Memory injection status
        self.injection_status = tk.Label(indicators_frame, text="💉 Memory Injection: INACTIVE", 
                                       bg='#1a1a1a', fg='red', font=('Arial', 11, 'bold'))
        self.injection_status.pack(anchor='w', padx=10, pady=5)
        
        # Performance metrics
        metrics_frame = tk.LabelFrame(status_frame, text="📈 Performance Metrics", 
                                    bg='#1a1a1a', fg='white', font=('Arial', 12, 'bold'))
        metrics_frame.pack(fill='x', pady=10)
        
        self.fps_label = tk.Label(metrics_frame, text="🚀 FPS: 0", 
                                bg='#1a1a1a', fg='cyan', font=('Arial', 11, 'bold'))
        self.fps_label.pack(anchor='w', padx=10, pady=5)
        
        self.latency_label = tk.Label(metrics_frame, text="⏱️ Latency: 0ms", 
                                    bg='#1a1a1a', fg='cyan', font=('Arial', 11, 'bold'))
        self.latency_label.pack(anchor='w', padx=10, pady=5)
        
        self.accuracy_label = tk.Label(metrics_frame, text="🎯 Accuracy: 0%", 
                                     bg='#1a1a1a', fg='cyan', font=('Arial', 11, 'bold'))
        self.accuracy_label.pack(anchor='w', padx=10, pady=5)
        
        # Current target info
        target_frame = tk.LabelFrame(status_frame, text="🎯 Current Target", 
                                   bg='#1a1a1a', fg='white', font=('Arial', 12, 'bold'))
        target_frame.pack(fill='both', expand=True, pady=10)
        
        self.target_info = scrolledtext.ScrolledText(target_frame, height=10, 
                                                   bg='#0a0a0a', fg='lime', 
                                                   font=('Consolas', 10))
        self.target_info.pack(fill='both', expand=True, padx=10, pady=10)
        
    def create_memory_log_tab(self, notebook):
        """Create memory operations log tab"""
        log_frame = ttk.Frame(notebook, padding="10")
        notebook.add(log_frame, text="💾 Memory Log")
        
        # Controls
        controls_frame = tk.Frame(log_frame, bg='#1a1a1a')
        controls_frame.pack(fill='x', pady=5)
        
        tk.Button(controls_frame, text="🔄 Clear Log", bg='#4a4a4a', fg='white',
                 command=self.clear_memory_log).pack(side='left', padx=5)
        
        tk.Button(controls_frame, text="💾 Save Log", bg='#4a4a4a', fg='white',
                 command=self.save_memory_log).pack(side='left', padx=5)
        
        # Log display
        self.memory_log = scrolledtext.ScrolledText(log_frame, height=30, 
                                                  bg='#0a0a0a', fg='white', 
                                                  font=('Consolas', 9))
        self.memory_log.pack(fill='both', expand=True, pady=10)
        
    def create_target_viz_tab(self, notebook):
        """Create target detection visualization tab"""
        viz_frame = ttk.Frame(notebook, padding="10")
        notebook.add(viz_frame, text="🎯 Target Visualization")
        
        # Create matplotlib figure
        self.fig, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(12, 6))
        self.fig.patch.set_facecolor('#1a1a1a')
        
        # Setup 2D map view
        self.ax1.set_title('2D Map View', color='white')
        self.ax1.set_facecolor('#0a0a0a')
        self.ax1.tick_params(colors='white')
        
        # Setup FOV view
        self.ax2.set_title('FOV View', color='white')
        self.ax2.set_facecolor('#0a0a0a')
        self.ax2.tick_params(colors='white')
        
        # Embed in tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, viz_frame)
        self.canvas.get_tk_widget().pack(fill='both', expand=True)
        
    def create_performance_tab(self, notebook):
        """Create performance metrics tab"""
        perf_frame = ttk.Frame(notebook, padding="10")
        notebook.add(perf_frame, text="📈 Performance")
        
        # Performance graphs
        self.perf_fig, ((self.fps_ax, self.latency_ax), 
                       (self.memory_ax, self.accuracy_ax)) = plt.subplots(2, 2, figsize=(12, 8))
        self.perf_fig.patch.set_facecolor('#1a1a1a')
        
        # Setup each graph
        for ax, title in zip([self.fps_ax, self.latency_ax, self.memory_ax, self.accuracy_ax],
                           ['FPS Over Time', 'Latency (ms)', 'Memory Usage', 'Accuracy %']):
            ax.set_title(title, color='white')
            ax.set_facecolor('#0a0a0a')
            ax.tick_params(colors='white')
        
        self.perf_canvas = FigureCanvasTkAgg(self.perf_fig, perf_frame)
        self.perf_canvas.get_tk_widget().pack(fill='both', expand=True)
        
    def create_data_inspector_tab(self, notebook):
        """Create raw data inspector tab"""
        data_frame = ttk.Frame(notebook, padding="10")
        notebook.add(data_frame, text="🔍 Data Inspector")
        
        # Data type selector
        selector_frame = tk.Frame(data_frame, bg='#1a1a1a')
        selector_frame.pack(fill='x', pady=5)
        
        tk.Label(selector_frame, text="Data Type:", bg='#1a1a1a', fg='white').pack(side='left', padx=5)
        
        self.data_type_var = tk.StringVar(value="memory_reads")
        data_combo = ttk.Combobox(selector_frame, textvariable=self.data_type_var,
                                 values=['memory_reads', 'target_detections', 'aim_calculations', 
                                        'memory_writes', 'performance_metrics'],
                                 state='readonly')
        data_combo.pack(side='left', padx=5)
        data_combo.bind('<<ComboboxSelected>>', self.update_data_inspector)
        
        # Raw data display
        self.data_inspector = scrolledtext.ScrolledText(data_frame, height=25, 
                                                      bg='#0a0a0a', fg='cyan', 
                                                      font=('Consolas', 9))
        self.data_inspector.pack(fill='both', expand=True, pady=10)
        
    def log_memory_operation(self, operation_type, address, data, success=True):
        """Log memory operation for debugging"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        status = "✅" if success else "❌"
        
        log_entry = {
            'timestamp': timestamp,
            'type': operation_type,
            'address': f"0x{address:X}" if address else "N/A",
            'data': str(data)[:100],  # Truncate long data
            'success': success
        }
        
        self.debug_data['memory_reads'].append(log_entry)
        
        # Update memory log display
        log_text = f"[{timestamp}] {status} {operation_type} @ {log_entry['address']}: {log_entry['data']}\n"
        self.memory_log.insert(tk.END, log_text)
        self.memory_log.see(tk.END)
        
        # Update status
        if success:
            self.memory_status.config(text="💾 Memory Reading: ACTIVE", fg='lime')
        else:
            self.memory_status.config(text="💾 Memory Reading: ERROR", fg='red')
    
    def log_target_detection(self, targets, local_player_pos):
        """Log target detection results"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        detection_data = {
            'timestamp': timestamp,
            'target_count': len(targets),
            'targets': targets,
            'local_pos': local_player_pos
        }
        
        self.debug_data['target_detections'].append(detection_data)
        
        # Update target status
        self.target_status.config(text=f"🎯 Target Detection: {len(targets)} targets", 
                                fg='lime' if targets else 'orange')
        
        # Update target info display
        target_text = f"[{timestamp}] Detected {len(targets)} targets:\n"
        for i, target in enumerate(targets):
            distance = np.sqrt((target['x'] - local_player_pos[0])**2 + 
                             (target['y'] - local_player_pos[1])**2 + 
                             (target['z'] - local_player_pos[2])**2)
            target_text += f"  Target {i+1}: Pos({target['x']:.1f}, {target['y']:.1f}, {target['z']:.1f}) "
            target_text += f"Health: {target['health']:.0f} Team: {target['team']} Dist: {distance:.1f}\n"
        
        self.target_info.delete(1.0, tk.END)
        self.target_info.insert(tk.END, target_text)
        
    def log_aim_calculation(self, current_angles, target_angles, target_info):
        """Log aim calculation results"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        aim_data = {
            'timestamp': timestamp,
            'current_angles': current_angles,
            'target_angles': target_angles,
            'angle_diff': (target_angles[0] - current_angles[0], target_angles[1] - current_angles[1]),
            'target_info': target_info
        }
        
        self.debug_data['aim_calculations'].append(aim_data)
        
        # Update aim status
        self.aim_status.config(text="🧮 Aim Calculation: ACTIVE", fg='lime')
        
    def log_memory_injection(self, address, angles, success=True):
        """Log memory injection attempt"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        injection_data = {
            'timestamp': timestamp,
            'address': f"0x{address:X}",
            'angles': angles,
            'success': success
        }
        
        self.debug_data['memory_writes'].append(injection_data)
        
        # Update injection status
        if success:
            self.injection_status.config(text="💉 Memory Injection: ACTIVE", fg='lime')
        else:
            self.injection_status.config(text="💉 Memory Injection: FAILED", fg='red')
    
    def update_performance_metrics(self, fps, latency, accuracy):
        """Update performance metrics"""
        timestamp = time.time()
        
        perf_data = {
            'timestamp': timestamp,
            'fps': fps,
            'latency': latency,
            'accuracy': accuracy
        }
        
        self.debug_data['performance_metrics'].append(perf_data)
        
        # Update labels
        self.fps_label.config(text=f"🚀 FPS: {fps:.1f}")
        self.latency_label.config(text=f"⏱️ Latency: {latency:.1f}ms")
        self.accuracy_label.config(text=f"🎯 Accuracy: {accuracy:.1f}%")
        
    def update_visualizations(self):
        """Update all visualizations"""
        try:
            # Update target visualization
            self.update_target_visualization()
            
            # Update performance graphs
            self.update_performance_graphs()
            
        except Exception as e:
            self.logger.error(f"Visualization update error: {e}")
    
    def update_target_visualization(self):
        """Update target detection visualization"""
        if not self.debug_data['target_detections']:
            return
            
        latest_detection = self.debug_data['target_detections'][-1]
        targets = latest_detection['targets']
        local_pos = latest_detection['local_pos']
        
        # Clear previous plots
        self.ax1.clear()
        self.ax2.clear()
        
        # 2D Map View
        self.ax1.set_title('2D Map View (Top-Down)', color='white')
        self.ax1.set_facecolor('#0a0a0a')
        
        # Plot local player
        self.ax1.scatter(local_pos[0], local_pos[1], c='blue', s=100, marker='o', label='Local Player')
        
        # Plot targets
        for target in targets:
            color = 'red' if target['team'] != 1 else 'green'  # Assume team 1 is friendly
            self.ax1.scatter(target['x'], target['y'], c=color, s=80, marker='^', alpha=0.7)
        
        self.ax1.legend()
        self.ax1.grid(True, alpha=0.3)
        
        # FOV View (relative to local player)
        self.ax2.set_title('FOV View (Relative Positions)', color='white')
        self.ax2.set_facecolor('#0a0a0a')
        
        # Draw FOV circle (example 90 degrees)
        fov_circle = plt.Circle((0, 0), 100, fill=False, color='yellow', alpha=0.5)
        self.ax2.add_patch(fov_circle)
        
        # Plot targets relative to local player
        for target in targets:
            rel_x = target['x'] - local_pos[0]
            rel_y = target['y'] - local_pos[1]
            distance = np.sqrt(rel_x**2 + rel_y**2)
            
            if distance <= 100:  # Within FOV range
                color = 'red' if target['team'] != 1 else 'green'
                self.ax2.scatter(rel_x, rel_y, c=color, s=80, marker='^', alpha=0.7)
        
        self.ax2.set_xlim(-150, 150)
        self.ax2.set_ylim(-150, 150)
        self.ax2.grid(True, alpha=0.3)
        
        self.canvas.draw()
    
    def update_performance_graphs(self):
        """Update performance metric graphs"""
        if len(self.debug_data['performance_metrics']) < 2:
            return
            
        # Extract data
        metrics = list(self.debug_data['performance_metrics'])
        timestamps = [m['timestamp'] for m in metrics]
        fps_data = [m['fps'] for m in metrics]
        latency_data = [m['latency'] for m in metrics]
        accuracy_data = [m['accuracy'] for m in metrics]
        
        # Normalize timestamps
        base_time = timestamps[0]
        time_points = [(t - base_time) for t in timestamps]
        
        # Clear and update graphs
        for ax in [self.fps_ax, self.latency_ax, self.memory_ax, self.accuracy_ax]:
            ax.clear()
            ax.set_facecolor('#0a0a0a')
            ax.tick_params(colors='white')
        
        # FPS graph
        self.fps_ax.plot(time_points, fps_data, 'lime', linewidth=2)
        self.fps_ax.set_title('FPS Over Time', color='white')
        self.fps_ax.set_ylabel('FPS', color='white')
        
        # Latency graph
        self.latency_ax.plot(time_points, latency_data, 'orange', linewidth=2)
        self.latency_ax.set_title('Latency (ms)', color='white')
        self.latency_ax.set_ylabel('Milliseconds', color='white')
        
        # Memory usage (simulated)
        memory_data = [50 + 10 * np.sin(t/10) for t in time_points]
        self.memory_ax.plot(time_points, memory_data, 'cyan', linewidth=2)
        self.memory_ax.set_title('Memory Usage', color='white')
        self.memory_ax.set_ylabel('MB', color='white')
        
        # Accuracy graph
        self.accuracy_ax.plot(time_points, accuracy_data, 'yellow', linewidth=2)
        self.accuracy_ax.set_title('Accuracy %', color='white')
        self.accuracy_ax.set_ylabel('Percentage', color='white')
        
        self.perf_canvas.draw()
    
    def update_data_inspector(self, event=None):
        """Update raw data inspector"""
        data_type = self.data_type_var.get()
        data = list(self.debug_data[data_type])
        
        self.data_inspector.delete(1.0, tk.END)
        
        if data:
            # Show latest 50 entries
            recent_data = data[-50:]
            for entry in recent_data:
                formatted_entry = json.dumps(entry, indent=2, default=str)
                self.data_inspector.insert(tk.END, formatted_entry + "\n" + "="*50 + "\n")
        else:
            self.data_inspector.insert(tk.END, f"No {data_type} data available yet.")
        
        self.data_inspector.see(tk.END)
    
    def clear_memory_log(self):
        """Clear memory operation log"""
        self.memory_log.delete(1.0, tk.END)
        self.debug_data['memory_reads'].clear()
    
    def save_memory_log(self):
        """Save memory log to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"memory_debug_log_{timestamp}.json"
        
        # Convert deques to lists for JSON serialization
        save_data = {key: list(value) for key, value in self.debug_data.items()}
        
        with open(filename, 'w') as f:
            json.dump(save_data, f, indent=2, default=str)
        
        self.logger.info(f"Debug log saved to {filename}")
    
    def start_monitoring(self):
        """Start monitoring thread"""
        self.is_monitoring = True
        monitor_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        monitor_thread.start()
    
    def monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # Simulate debug data (replace with actual aimbot integration)
                self.simulate_debug_data()
                
                # Update visualizations
                self.update_visualizations()
                
                # Update data inspector if needed
                if hasattr(self, 'data_inspector'):
                    self.root.after(0, self.update_data_inspector)
                
                time.sleep(0.1)  # 10 FPS update rate for GUI
                
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                time.sleep(1)
    
    def simulate_debug_data(self):
        """Simulate debug data for testing (replace with actual aimbot integration)"""
        current_time = time.time()
        
        # Simulate memory operations
        if current_time - self.last_update > 0.1:
            self.log_memory_operation("READ", 0x12345678, "Player data", True)
            
            # Simulate target detection
            fake_targets = [
                {'x': 100 + 50*np.sin(current_time), 'y': 200 + 30*np.cos(current_time), 
                 'z': 50, 'health': 100, 'team': 2},
                {'x': -80 + 20*np.cos(current_time*1.5), 'y': 150 + 40*np.sin(current_time*1.5), 
                 'z': 60, 'health': 75, 'team': 2}
            ]
            local_pos = (0, 0, 50)
            
            self.log_target_detection(fake_targets, local_pos)
            
            # Simulate aim calculation
            current_angles = (0, 90)
            target_angles = (5, 95)
            self.log_aim_calculation(current_angles, target_angles, fake_targets[0])
            
            # Simulate memory injection
            self.log_memory_injection(0x87654321, target_angles, True)
            
            # Update performance metrics
            fps = 800 + 200*np.sin(current_time/5)
            latency = 1 + 0.5*np.sin(current_time/3)
            accuracy = 85 + 10*np.cos(current_time/7)
            self.update_performance_metrics(fps, latency, accuracy)
            
            self.last_update = current_time
    
    def run(self):
        """Run the debugger"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🔍 Launching Memory Aimbot Debugger...")
    debugger = MemoryAimbotDebugger()
    debugger.run()
