#!/usr/bin/env python3
"""
🔍 COMPUTER VISION DETECTOR
===========================
Advanced computer vision-based target detection using YOLO and custom algorithms.
"""

import cv2
import numpy as np
import time
import logging
from typing import List, Optional, Dict, Any, Tuple
import torch
from ultralytics import <PERSON>OL<PERSON>

from .base_detector import BaseDetector, Target
from ..utils.screen_capture import ScreenCapture
from ..utils.math_utils import calculate_distance, calculate_iou

class ComputerVisionDetector(BaseDetector):
    """
    Computer vision-based target detector using YOLO and custom algorithms.
    
    Features:
    - YOLO-based person detection
    - Custom enemy detection algorithms
    - Multi-scale detection
    - Confidence filtering
    - Non-maximum suppression
    - Target validation and tracking
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the computer vision detector"""
        super().__init__(config)
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.detection_confidence = self.config.get('detection_confidence', 0.5)
        self.nms_threshold = self.config.get('nms_threshold', 0.4)
        self.target_classes = self.config.get('target_classes', ['person'])
        self.detection_region = self.config.get('detection_region', None)  # (x, y, w, h)
        self.multi_scale = self.config.get('multi_scale', True)
        self.max_targets = self.config.get('max_targets', 10)
        
        # Screen capture
        self.screen_capture = ScreenCapture(self.config.get('screen_capture', {}))
        
        # AI Models
        self.yolo_model = None
        self.custom_model = None
        self.model_initialized = False
        
        # Detection state
        self.last_frame = None
        self.last_detections = []
        self.detection_history = []
        self.frame_count = 0
        
        # Performance tracking
        self.detection_times = []
        self.fps = 0.0
        
        # Initialize models
        self._initialize_models()
        
        self.logger.info("🔍 Computer Vision Detector initialized")
    
    def _initialize_models(self):
        """Initialize AI detection models"""
        try:
            self.logger.info("🤖 Loading AI detection models...")
            
            # Load YOLO model
            model_path = self.config.get('yolo_model_path', 'yolov8n.pt')
            self.yolo_model = YOLO(model_path)
            
            # Configure YOLO
            if torch.cuda.is_available():
                self.yolo_model.to('cuda')
                self.logger.info("🚀 YOLO model loaded on GPU")
            else:
                self.logger.info("💻 YOLO model loaded on CPU")
            
            # Load custom model if specified
            custom_model_path = self.config.get('custom_model_path')
            if custom_model_path:
                self.custom_model = self._load_custom_model(custom_model_path)
            
            self.model_initialized = True
            self.logger.info("✅ AI models initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Model initialization failed: {e}")
            self.model_initialized = False
    
    def _load_custom_model(self, model_path: str):
        """Load custom detection model"""
        # Placeholder for custom model loading
        # This would load game-specific detection models
        self.logger.info(f"📦 Loading custom model: {model_path}")
        return None
    
    def detect_targets(self) -> List[Target]:
        """Detect targets using computer vision"""
        if not self.model_initialized:
            return []
        
        detection_start = time.time()
        
        try:
            # Capture screen
            frame = self.screen_capture.capture()
            if frame is None:
                return []
            
            self.last_frame = frame
            self.frame_count += 1
            
            # Apply detection region if specified
            if self.detection_region:
                x, y, w, h = self.detection_region
                frame = frame[y:y+h, x:x+w]
            
            # Detect targets using multiple methods
            targets = []
            
            # YOLO detection
            yolo_targets = self._detect_with_yolo(frame)
            targets.extend(yolo_targets)
            
            # Custom detection algorithms
            custom_targets = self._detect_with_custom_algorithms(frame)
            targets.extend(custom_targets)
            
            # Apply non-maximum suppression
            targets = self._apply_nms(targets)
            
            # Validate and filter targets
            targets = self._validate_targets(targets, frame)
            
            # Limit number of targets
            targets = targets[:self.max_targets]
            
            # Update detection history
            self.last_detections = targets
            self.detection_history.append({
                'timestamp': time.time(),
                'targets': targets,
                'frame_count': self.frame_count
            })
            
            # Keep history manageable
            if len(self.detection_history) > 100:
                self.detection_history = self.detection_history[-100:]
            
            # Update performance metrics
            detection_time = time.time() - detection_start
            self.detection_times.append(detection_time)
            if len(self.detection_times) > 30:
                self.detection_times = self.detection_times[-30:]
            
            # Calculate FPS
            if len(self.detection_times) >= 10:
                avg_time = sum(self.detection_times[-10:]) / 10
                self.fps = 1.0 / avg_time if avg_time > 0 else 0
            
            return targets
            
        except Exception as e:
            self.logger.error(f"❌ Detection error: {e}")
            return []
    
    def _detect_with_yolo(self, frame: np.ndarray) -> List[Target]:
        """Detect targets using YOLO model"""
        if not self.yolo_model:
            return []
        
        try:
            # Run YOLO inference
            results = self.yolo_model(frame, conf=self.detection_confidence, verbose=False)
            
            targets = []
            for result in results:
                boxes = result.boxes
                if boxes is None:
                    continue
                
                for i, box in enumerate(boxes):
                    # Get box coordinates
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()
                    class_id = int(box.cls[0].cpu().numpy())
                    
                    # Check if it's a target class
                    class_name = self.yolo_model.names[class_id]
                    if class_name not in self.target_classes:
                        continue
                    
                    # Calculate center and dimensions
                    center_x = (x1 + x2) / 2
                    center_y = (y1 + y2) / 2
                    width = x2 - x1
                    height = y2 - y1
                    
                    # Adjust coordinates if detection region was used
                    if self.detection_region:
                        center_x += self.detection_region[0]
                        center_y += self.detection_region[1]
                    
                    # Create target
                    target = Target(
                        id=f"yolo_{self.frame_count}_{i}",
                        x=center_x,
                        y=center_y,
                        z=0,  # 2D detection
                        width=width,
                        height=height,
                        confidence=float(confidence),
                        detection_method='yolo',
                        class_name=class_name,
                        timestamp=time.time()
                    )
                    
                    targets.append(target)
            
            return targets
            
        except Exception as e:
            self.logger.error(f"❌ YOLO detection error: {e}")
            return []
    
    def _detect_with_custom_algorithms(self, frame: np.ndarray) -> List[Target]:
        """Detect targets using custom algorithms"""
        targets = []
        
        try:
            # Color-based detection
            color_targets = self._detect_by_color(frame)
            targets.extend(color_targets)
            
            # Template matching
            template_targets = self._detect_by_template(frame)
            targets.extend(template_targets)
            
            # Edge detection
            edge_targets = self._detect_by_edges(frame)
            targets.extend(edge_targets)
            
            return targets
            
        except Exception as e:
            self.logger.error(f"❌ Custom detection error: {e}")
            return []
    
    def _detect_by_color(self, frame: np.ndarray) -> List[Target]:
        """Detect targets by color characteristics"""
        targets = []
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Define color ranges for enemy detection (example: red team)
        color_ranges = self.config.get('color_ranges', {
            'red_team': {
                'lower': np.array([0, 50, 50]),
                'upper': np.array([10, 255, 255])
            }
        })
        
        for color_name, color_range in color_ranges.items():
            # Create mask
            mask = cv2.inRange(hsv, color_range['lower'], color_range['upper'])
            
            # Find contours
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                
                # Filter by area
                if area < 100 or area > 10000:
                    continue
                
                # Get bounding box
                x, y, w, h = cv2.boundingRect(contour)
                
                # Calculate center
                center_x = x + w / 2
                center_y = y + h / 2
                
                # Adjust coordinates if detection region was used
                if self.detection_region:
                    center_x += self.detection_region[0]
                    center_y += self.detection_region[1]
                
                # Create target
                target = Target(
                    id=f"color_{color_name}_{self.frame_count}_{i}",
                    x=center_x,
                    y=center_y,
                    z=0,
                    width=w,
                    height=h,
                    confidence=0.7,  # Medium confidence for color detection
                    detection_method='color',
                    class_name=color_name,
                    timestamp=time.time()
                )
                
                targets.append(target)
        
        return targets
    
    def _detect_by_template(self, frame: np.ndarray) -> List[Target]:
        """Detect targets using template matching"""
        targets = []
        
        # Load templates
        templates = self.config.get('templates', [])
        
        for template_config in templates:
            template_path = template_config.get('path')
            if not template_path:
                continue
            
            try:
                template = cv2.imread(template_path, cv2.IMREAD_COLOR)
                if template is None:
                    continue
                
                # Perform template matching
                result = cv2.matchTemplate(frame, template, cv2.TM_CCOEFF_NORMED)
                
                # Find matches above threshold
                threshold = template_config.get('threshold', 0.8)
                locations = np.where(result >= threshold)
                
                for i, (y, x) in enumerate(zip(*locations)):
                    # Calculate center
                    center_x = x + template.shape[1] / 2
                    center_y = y + template.shape[0] / 2
                    
                    # Adjust coordinates if detection region was used
                    if self.detection_region:
                        center_x += self.detection_region[0]
                        center_y += self.detection_region[1]
                    
                    # Create target
                    target = Target(
                        id=f"template_{template_config.get('name', 'unknown')}_{self.frame_count}_{i}",
                        x=center_x,
                        y=center_y,
                        z=0,
                        width=template.shape[1],
                        height=template.shape[0],
                        confidence=float(result[y, x]),
                        detection_method='template',
                        class_name=template_config.get('name', 'template'),
                        timestamp=time.time()
                    )
                    
                    targets.append(target)
                    
            except Exception as e:
                self.logger.error(f"❌ Template matching error: {e}")
        
        return targets
    
    def _detect_by_edges(self, frame: np.ndarray) -> List[Target]:
        """Detect targets using edge detection"""
        targets = []
        
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Edge detection
            edges = cv2.Canny(blurred, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                
                # Filter by area and shape
                if area < 500 or area > 20000:
                    continue
                
                # Check if contour is roughly rectangular (person-like)
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = h / w if w > 0 else 0
                
                if aspect_ratio < 1.2 or aspect_ratio > 3.0:  # Not person-like
                    continue
                
                # Calculate center
                center_x = x + w / 2
                center_y = y + h / 2
                
                # Adjust coordinates if detection region was used
                if self.detection_region:
                    center_x += self.detection_region[0]
                    center_y += self.detection_region[1]
                
                # Create target
                target = Target(
                    id=f"edge_{self.frame_count}_{i}",
                    x=center_x,
                    y=center_y,
                    z=0,
                    width=w,
                    height=h,
                    confidence=0.6,  # Lower confidence for edge detection
                    detection_method='edge',
                    class_name='edge_target',
                    timestamp=time.time()
                )
                
                targets.append(target)
        
        except Exception as e:
            self.logger.error(f"❌ Edge detection error: {e}")
        
        return targets
    
    def _apply_nms(self, targets: List[Target]) -> List[Target]:
        """Apply non-maximum suppression to remove duplicate detections"""
        if len(targets) <= 1:
            return targets
        
        # Convert to format for NMS
        boxes = []
        scores = []
        
        for target in targets:
            x1 = target.x - target.width / 2
            y1 = target.y - target.height / 2
            x2 = target.x + target.width / 2
            y2 = target.y + target.height / 2
            
            boxes.append([x1, y1, x2, y2])
            scores.append(target.confidence)
        
        boxes = np.array(boxes)
        scores = np.array(scores)
        
        # Apply NMS
        indices = cv2.dnn.NMSBoxes(
            boxes.tolist(), scores.tolist(), 
            self.detection_confidence, self.nms_threshold
        )
        
        if len(indices) == 0:
            return []
        
        # Return filtered targets
        filtered_targets = []
        for i in indices.flatten():
            filtered_targets.append(targets[i])
        
        return filtered_targets
    
    def _validate_targets(self, targets: List[Target], frame: np.ndarray) -> List[Target]:
        """Validate and filter targets"""
        valid_targets = []
        
        for target in targets:
            # Check bounds
            if (target.x < 0 or target.y < 0 or 
                target.x >= frame.shape[1] or target.y >= frame.shape[0]):
                continue
            
            # Check minimum confidence
            if target.confidence < self.detection_confidence:
                continue
            
            # Check size constraints
            min_size = self.config.get('min_target_size', 20)
            max_size = self.config.get('max_target_size', 500)
            
            if (target.width < min_size or target.height < min_size or
                target.width > max_size or target.height > max_size):
                continue
            
            # Additional validation can be added here
            valid_targets.append(target)
        
        return valid_targets
    
    def get_last_frame(self) -> Optional[np.ndarray]:
        """Get the last captured frame"""
        return self.last_frame
    
    def get_detection_overlay(self) -> Optional[np.ndarray]:
        """Get frame with detection overlays"""
        if self.last_frame is None or not self.last_detections:
            return self.last_frame
        
        overlay = self.last_frame.copy()
        
        for target in self.last_detections:
            # Draw bounding box
            x1 = int(target.x - target.width / 2)
            y1 = int(target.y - target.height / 2)
            x2 = int(target.x + target.width / 2)
            y2 = int(target.y + target.height / 2)
            
            # Color based on detection method
            color_map = {
                'yolo': (0, 255, 0),      # Green
                'color': (255, 0, 0),     # Blue
                'template': (0, 0, 255),  # Red
                'edge': (255, 255, 0)     # Cyan
            }
            color = color_map.get(target.detection_method, (255, 255, 255))
            
            cv2.rectangle(overlay, (x1, y1), (x2, y2), color, 2)
            
            # Draw center point
            cv2.circle(overlay, (int(target.x), int(target.y)), 3, color, -1)
            
            # Draw label
            label = f"{target.class_name}: {target.confidence:.2f}"
            cv2.putText(overlay, label, (x1, y1 - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return overlay
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            'fps': self.fps,
            'avg_detection_time': sum(self.detection_times) / len(self.detection_times) if self.detection_times else 0,
            'frame_count': self.frame_count,
            'last_detection_count': len(self.last_detections),
            'model_initialized': self.model_initialized
        }
    
    def cleanup(self):
        """Cleanup detector resources"""
        if self.screen_capture:
            self.screen_capture.cleanup()
        
        # Clear GPU memory if using CUDA
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        self.logger.info("🔍 Computer Vision Detector cleaned up")
