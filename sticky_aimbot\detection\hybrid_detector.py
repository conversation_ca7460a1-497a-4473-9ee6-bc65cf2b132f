#!/usr/bin/env python3
"""
🔄 HYBRID DETECTOR
==================
Hybrid detection combining computer vision and memory reading.
"""

import time
import logging
from typing import List, Dict, Any

from .base_detector import BaseDetector, Target
from .cv_detector import ComputerVisionDetector
from .memory_detector import MemoryDetector

class HybridDetector(BaseDetector):
    """Hybrid detector combining CV and memory detection"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize hybrid detector"""
        super().__init__(config)
        self.logger = logging.getLogger(__name__)
        
        # Initialize sub-detectors
        self.cv_detector = ComputerVisionDetector(config.get('cv', {}))
        self.memory_detector = MemoryDetector(config.get('memory', {}))
        
        self.logger.info("🔄 Hybrid Detector initialized")
    
    def detect_targets(self) -> List[Target]:
        """Detect targets using hybrid approach"""
        self.detection_count += 1
        self.last_detection_time = time.time()
        
        # Get targets from both detectors
        cv_targets = self.cv_detector.detect_targets() if self.cv_detector.is_enabled() else []
        memory_targets = self.memory_detector.detect_targets() if self.memory_detector.is_enabled() else []
        
        # Combine and validate targets
        all_targets = cv_targets + memory_targets
        
        # Remove duplicates and validate
        validated_targets = self._validate_and_merge_targets(all_targets)
        
        return validated_targets
    
    def _validate_and_merge_targets(self, targets: List[Target]) -> List[Target]:
        """Validate and merge targets from different sources"""
        # Simple implementation - just return CV targets for now
        cv_targets = [t for t in targets if t.detection_method == 'yolo']
        return cv_targets[:10]  # Limit to 10 targets
    
    def cleanup(self):
        """Cleanup hybrid detector"""
        if self.cv_detector:
            self.cv_detector.cleanup()
        if self.memory_detector:
            self.memory_detector.cleanup()
        super().cleanup()
