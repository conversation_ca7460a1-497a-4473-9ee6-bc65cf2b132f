#!/usr/bin/env python3
"""
📸 SCREEN CAPTURE
=================
Screen capture utilities for computer vision detection.
"""

import cv2
import numpy as np
try:
    import win32gui
    import win32ui
    import win32con
    import win32api
except ImportError:
    print("⚠️ Win32 modules not available - screen capture may be limited")
from typing import Optional, Tuple, Dict, Any

class ScreenCapture:
    """Screen capture utility class"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize screen capture"""
        self.config = config or {}
        self.window_name = self.config.get('window_name', None)
        self.region = self.config.get('region', None)  # (x, y, w, h)
        self.hwnd = None
        
        if self.window_name:
            self.hwnd = win32gui.FindWindow(None, self.window_name)
    
    def capture(self) -> Optional[np.ndarray]:
        """Capture screen or window"""
        try:
            if self.hwnd:
                return self._capture_window()
            else:
                return self._capture_screen()
        except Exception as e:
            print(f"❌ Screen capture error: {e}")
            return None
    
    def _capture_window(self) -> Optional[np.ndarray]:
        """Capture specific window"""
        try:
            # Get window dimensions
            rect = win32gui.GetWindowRect(self.hwnd)
            width = rect[2] - rect[0]
            height = rect[3] - rect[1]
            
            # Get window device context
            hwndDC = win32gui.GetWindowDC(self.hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # Create bitmap
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            
            # Copy window content
            saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            
            # Convert to numpy array
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            
            img = np.frombuffer(bmpstr, dtype='uint8')
            img.shape = (height, width, 4)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
            # Cleanup
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(self.hwnd, hwndDC)
            
            return img
            
        except Exception as e:
            print(f"❌ Window capture error: {e}")
            return None
    
    def _capture_screen(self) -> Optional[np.ndarray]:
        """Capture full screen or region"""
        try:
            if self.region:
                x, y, w, h = self.region
                # Use mss for region capture (placeholder)
                # For now, capture full screen and crop
                import mss
                with mss.mss() as sct:
                    monitor = {"top": y, "left": x, "width": w, "height": h}
                    screenshot = sct.grab(monitor)
                    img = np.array(screenshot)
                    return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            else:
                # Full screen capture
                import mss
                with mss.mss() as sct:
                    screenshot = sct.grab(sct.monitors[1])  # Primary monitor
                    img = np.array(screenshot)
                    return cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                    
        except ImportError:
            # Fallback to win32 API
            return self._capture_screen_win32()
        except Exception as e:
            print(f"❌ Screen capture error: {e}")
            return None
    
    def _capture_screen_win32(self) -> Optional[np.ndarray]:
        """Fallback screen capture using win32 API"""
        try:
            # Get screen dimensions
            width = win32api.GetSystemMetrics(win32con.SM_CXSCREEN)
            height = win32api.GetSystemMetrics(win32con.SM_CYSCREEN)
            
            # Create device contexts
            hdesktop = win32gui.GetDesktopWindow()
            hwndDC = win32gui.GetWindowDC(hdesktop)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # Create bitmap
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            
            # Copy screen
            saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            
            # Convert to numpy array
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            
            img = np.frombuffer(bmpstr, dtype='uint8')
            img.shape = (height, width, 4)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
            # Cleanup
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(hdesktop, hwndDC)
            
            # Apply region if specified
            if self.region:
                x, y, w, h = self.region
                img = img[y:y+h, x:x+w]
            
            return img
            
        except Exception as e:
            print(f"❌ Win32 screen capture error: {e}")
            return None
    
    def cleanup(self):
        """Cleanup resources"""
        pass
