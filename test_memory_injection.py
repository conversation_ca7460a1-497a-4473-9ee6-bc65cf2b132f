#!/usr/bin/env python3
"""
🧪 TEST MEMORY INJECTION
=======================
Test memory reading/writing capabilities
"""

import ctypes
import ctypes.wintypes
import psutil
import struct

def test_memory_access():
    """Test basic memory access functionality"""
    print("🧪 MEMORY INJECTION TEST")
    print("========================")
    
    # Test 1: List running processes
    print("\n1. 📋 Listing running processes:")
    processes = []
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            name = proc.info['name']
            if name.endswith('.exe') and 'python' not in name.lower():
                processes.append(f"{name} (PID: {proc.info['pid']})")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    processes.sort()
    for i, proc in enumerate(processes[:10]):  # Show first 10
        print(f"   {i+1}. {proc}")
    
    if len(processes) > 10:
        print(f"   ... and {len(processes) - 10} more processes")
    
    # Test 2: Test Windows API access
    print("\n2. 🔧 Testing Windows API access:")
    try:
        kernel32 = ctypes.windll.kernel32
        print("   ✅ kernel32.dll loaded successfully")
        
        # Test GetCurrentProcessId
        current_pid = kernel32.GetCurrentProcessId()
        print(f"   ✅ Current process ID: {current_pid}")
        
        # Test OpenProcess
        handle = kernel32.OpenProcess(0x1F0FFF, False, current_pid)
        if handle:
            print("   ✅ OpenProcess successful")
            kernel32.CloseHandle(handle)
        else:
            print("   ❌ OpenProcess failed")
            
    except Exception as e:
        print(f"   ❌ Windows API test failed: {e}")
    
    # Test 3: Test memory operations on self
    print("\n3. 🧠 Testing memory operations:")
    try:
        # Create test data
        test_value = 12345.67
        test_bytes = struct.pack('<f', test_value)
        
        print(f"   📝 Test value: {test_value}")
        print(f"   📝 Packed bytes: {test_bytes.hex()}")
        
        # Unpack to verify
        unpacked = struct.unpack('<f', test_bytes)[0]
        print(f"   📝 Unpacked value: {unpacked}")
        
        if abs(unpacked - test_value) < 0.001:
            print("   ✅ Memory packing/unpacking works correctly")
        else:
            print("   ❌ Memory packing/unpacking failed")
            
    except Exception as e:
        print(f"   ❌ Memory operations test failed: {e}")
    
    # Test 4: Test process memory access (safe test)
    print("\n4. 🔍 Testing process memory access:")
    try:
        # Try to read our own process memory (safe)
        current_pid = ctypes.windll.kernel32.GetCurrentProcessId()
        handle = ctypes.windll.kernel32.OpenProcess(0x1F0FFF, False, current_pid)
        
        if handle:
            print("   ✅ Successfully opened own process handle")
            
            # Try to read a small amount of memory from a safe location
            buffer = ctypes.create_string_buffer(4)
            bytes_read = ctypes.c_size_t()
            
            # This will likely fail but tests the API
            result = ctypes.windll.kernel32.ReadProcessMemory(
                handle, ctypes.c_void_p(0x1000), buffer, 4, ctypes.byref(bytes_read)
            )
            
            if result:
                print(f"   ✅ ReadProcessMemory successful, read {bytes_read.value} bytes")
            else:
                print("   ⚠️  ReadProcessMemory failed (expected for this test)")
            
            ctypes.windll.kernel32.CloseHandle(handle)
        else:
            print("   ❌ Failed to open own process handle")
            
    except Exception as e:
        print(f"   ❌ Process memory access test failed: {e}")
    
    # Test 5: Game offset simulation
    print("\n5. 🎮 Testing game offset simulation:")
    try:
        from game_offsets import get_game_offsets, list_supported_games
        
        print("   📋 Supported games:")
        for game in list_supported_games():
            offsets = get_game_offsets(game)
            print(f"      • {game} - Module: {offsets.module_name}")
        
        # Test getting offsets for a specific game
        csgo_offsets = get_game_offsets("csgo.exe")
        print(f"\n   🎯 CS:GO offsets example:")
        print(f"      Player base: 0x{csgo_offsets.player_base:X}")
        print(f"      Local player: 0x{csgo_offsets.local_player:X}")
        print(f"      View angles: 0x{csgo_offsets.view_angles:X}")
        print("   ✅ Game offset system working")
        
    except Exception as e:
        print(f"   ❌ Game offset test failed: {e}")
    
    print("\n🎯 MEMORY INJECTION CAPABILITIES:")
    print("✅ Process enumeration")
    print("✅ Windows API access")
    print("✅ Memory packing/unpacking")
    print("✅ Process handle management")
    print("✅ Game offset configuration")
    print("\n⚠️  IMPORTANT NOTES:")
    print("• Memory injection requires administrator privileges")
    print("• Game offsets need to be updated regularly")
    print("• Anti-cheat systems may detect memory access")
    print("• Use only for educational purposes")
    print("• Test in offline/practice modes only")

if __name__ == "__main__":
    test_memory_access()
    input("\nPress Enter to exit...")
