#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

bugreport:
{
	btn_continue = "在线检查解决方案并关闭游戏。";
	btn_exit = "关闭游戏。";
	btn_hidedetails = "隐藏详情";
	btn_showdetails = "显示详情";
	chk_sendreport = "发送错误报告";
	error_code = "错误代码：";
	lbl_body1 = "很抱歉，在启动你的游戏时遇到了问题。";
	lbl_body2 = "请通过报告问题帮助我们解决。";
	lbl_body3 = "Easy Anti-Cheat 可以在线检查有此问题的解决方案，并试着协助解决。";
	lbl_header = "无法启动游戏";
	title = "启动错误";
};
game_error:
{
	error_catalogue_corrupted = "Easy Anti-Cheat Hash 列表已损坏";
	error_catalogue_not_found = "未找到 EAC 索引";
	error_certificate_revoked = "EAC 索引证书已撤销";
	error_corrupted_memory = "内存损坏";
	error_corrupted_network = "数据包流损坏";
	error_file_forbidden = "未知的游戏文件";
	error_file_not_found = "缺少必需的文件";
	error_file_version = "未知的文件版本";
	error_module_forbidden = "禁止的模块";
	error_system_configuration = "禁止的系统配置";
	error_system_version = "不受信任的系统文件";
	error_tool_forbidden = "禁止的工具";
	error_violation = "内部反作弊错误";
	error_virtual = "无法在虚拟机下运行。";
	peer_client_banned = "反作弊对等程序被禁止。";
	peer_heartbeat_rejected = "反作弊对等程序被拒绝。";
	peer_validated = "反作弊对等验证完成。";
	peer_validation_failed = "反作弊对等验证失败。";
	executable_not_hashed = "无法在目录中找到游戏可执行条目。";
};
launcher:
{
	btn_cancel = "取消";
	btn_exit = "退出";
	error_cancel = "启动取消";
	error_filenotfound = "文件未找到";
	error_init = "初始化错误";
	error_install = "安装错误";
	error_launch = "启动错误";
	error_nolib = "无法加载 Easy Anti-Cheat 库";
	loading = "载入中";
	wait = "请稍候";
	initializing = "正在初始化";
	success_waiting_for_game = "等待游戏窗口";
	success_closing = "等待游戏窗口";
	network_error = "网络";
	error_no_settings_file = "{0} 未找到";
	error_invalid_settings_format = "{0} 没有有效的 JSON 格式";
	error_missing_required_field = "{0} 缺失必要字段（{1}）";
	error_invalid_eos_identifier = "{0} 中包含无效的 EOS 标识符：（{1}）";
	download_progress = "下载进程： {0}";
};
launcher_error:
{
	error_already_running = "使用 Easy Anti-Cheat 的应用程序已在运行中！ {0}";
	error_application = "游戏客户端遇到应用程序错误。错误代码： {0}";
	error_bad_exe_format = "需要 64 位操作系统";
	error_bitset_32 = "请使用游戏的32位版本";
	error_bitset_64 = "请使用游戏的64位版本";
	error_cancelled = "操作已由用户取消";
	error_certificate_validation = "验证 Easy Anti-Cheat 代码签名证书时出错";
	error_connection = "连接到内容分发网络失败！";
	error_debugger = "检测到调试程序。请取消载入，然后重试";
	error_disk_space = "磁盘空间不足。";
	error_dns = "DNS 解析到内容分发网络失败！";
	error_dotlocal = "检测到 DotLocal DLL 重定向。";
	error_dotlocal_instructions = "请删除以下文件";
	error_file_not_found = "文件未找到：";
	error_forbidden_tool = "请在开始游戏前关闭{0}";
	error_forbidden_driver = "请在启动游戏前卸载 {0}";
	error_generic = "意外错误。";
	error_kernel_debug = "如果启用了内核调试，Easy Anti-Cheat 将无法运行";
	error_kernel_dse = "如果禁用了驱动程序强制签名，Easy Anti-Cheat 将无法运行";
	error_kernel_modified = "检测到禁止的 Windows 内核修改";
	error_library_load = "无法加载 Easy Anti-Cheat 库";
	error_memory = "内存不足，无法启动游戏";
	error_module_load = "无法加载反作弊模块";
	error_patched = "检测到经过修补的 Windows 启动加载程序";
	error_process = "无法创建进程";
	error_process_crash = "进程突然终止";
	error_safe_mode = "Easy Anti-Cheat 无法在 Windows 安全模式下运行";
	error_socket = "有什么东西阻止应用程序访问互联网！";
	error_ssl = "与 CDN 服务建立 SSL 连接时出错！";
	error_start = "无法启动游戏";
	error_uncpath_forbidden = "无法通过网络共享运行游戏。（UNC 路径）";
	error_connection_failed = "连接失败: ";
	error_missing_game_id = "缺失游戏账号";
	error_dns_resolve_failed = "DNS 代理解析失败";
	error_dns_connection_failed = "内容分发网络连接失败！错误代码：{0}!";
	error_http_response = "HTTP 响应代码：{0} 错误代码：{1}";
	error_driver_handle = "意外错误。（无法打开驱动程序句柄）";
	error_incompatible_service = "已有不兼容的 Easy Anti-Cheat 服务在运行中。请退出其他正在运行的游戏，或重新启动";
	error_incompatible_driver_version = "已有不兼容的 Easy Anti-Cheat 驱动程序版本在运行中。请退出其他正在运行的游戏，或重新启动";
	error_another_launcher = "意外错误。（另一个启动器已经在运行）";
	error_game_running = "意外错误。（游戏已经在运行）";
	error_patched_boot_loader = "检测到修复的 Windows 引导加载程序。 （内核补丁保护已禁用）";
	error_unknown_process = "无法识别的游戏客户端。无法继续。";
	error_unknown_game = "游戏未配置。无法继续。";
	error_win7_required = "需要 Windows 7 或更高版本。";
	success_initialized = "Easy Anti-Cheat 成功初始化";
	success_loaded = "Easy Anti-Cheat 在游戏中成功加载";
	error_create_process = "创建游戏进程失败：{0}";
	error_create_thread = "创建后台线程失败！";
	error_disallowed_cdn_path = "意外错误。（不正确的 CDN 网址）";
	error_empty_executable_field = "未提供游戏二进制文件的路径。";
	error_failed_path_query = "无法获取进程路径";
	error_failed_to_execute = "无法执行游戏进程。";
	error_game_binary_is_directory = "目标可执行文件为目录！";
	error_game_binary_is_dot_app = "目标可执行文件为目录，请在 .app 文件中定位二进制文件！";
	error_game_binary_not_found = "找不到游戏二进制文件：'{0}'";
	error_game_binary_not_found_wine = "找不到游戏二进制文件（Wine 兼容层）";
	error_game_security_violation = "游戏安全违规 {0}";
	error_generic_ex = "意外错误。{0}";
	error_instance_count_limit = "已达到最大同时游戏实例数！";
	error_internal = "内部错误！";
	error_invalid_executable_path = "游戏可执行文件路径无效！";
	error_memory_ex = "启动游戏《{0}》所需的内存不足";
	error_missing_binary_path = "缺失游戏可执行文件路径。";
	error_missing_directory_path = "缺失工作目录路径。";
	error_module_initialize = "模块初始化失败，原因为 {0}";
	error_module_loading = "调取反作弊模块失败。";
	error_set_environment_variables = "为游戏进程设置环境变量失败。";
	error_unrecognized_blacklisted_driver = "检测到 N/A。请将其卸载并重试。";
	error_unsupported_machine_arch = "不支持的主机架构。（{0}）";
	error_working_directory_not_found = "工作目录不存在。";
	error_x8664_required = "不支持的操作系统。需要 64 位（x86-64）Windows。";
	warn_module_download_size = "HTTP响应大小：{0}。以空客户端模式开始。";
	warn_vista_deprecation = "Easy Anti-Cheat 必须在 2020 年 10 月结束对 Windows Vista 的支持，因为无法再创建兼容的代码签名。请参阅 https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "无法验证反作弊配置。";
	warn_win7_update_required = "请运行 Windows 更新，您的系统缺少 2020 年 10 月所需的关键 SHA-2 代码签名支持。请参阅 https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "Easy Anti-Cheat 服务更新已超时。"
	error_launch_ex = "启动错误: {0}";
};
setup:
{
	btn_finish = "完成";
	btn_install = "立即安装";
	btn_repair = "修复服务";
	btn_uninstall = "卸载";
	epic_link = "© Epic Games, Inc";
	install_progress = "正在安装……";
	install_success = "成功安装";
	licenses_link = "许可证";
	privacy_link = "隐私";
	repair_progress = "正在修复……";
	title = "Easy Anti-Cheat服务安装";
	uninstall_progress = "正在卸载……";
	uninstall_success = "成功卸载";
};
setup_error:
{
	error_cancelled = "操作已由用户取消";
	error_encrypted = "Easy Anti-Cheat (防作弊）安装文件夹已加密";
	error_intro = "Easy Anti-Cheat 设置失败";
	error_not_installed = "未安装 Easy Anti-Cheat。";
	error_registry = "复制服务可执行文件失败（32）";
	error_rights = "权限不足";
	error_service = "无法创建服务";
	error_service_ex = "无法创建服务 {0}";
	error_system = "访问 System32 被拒绝";
};