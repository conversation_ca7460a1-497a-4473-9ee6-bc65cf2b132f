#!/usr/bin/env python3
"""
🎯 STICKY AIM SYSTEM
===================
Advanced sticky aim (aim lock) implementation for memory injection aimbot
Continuously tracks and locks onto targets with smooth, humanized movement
"""

import math
import time
import numpy as np
from dataclasses import dataclass
from typing import Optional, List, Tuple
from collections import deque
import random

@dataclass
class Target:
    """Target data structure"""
    id: int
    x: float
    y: float
    z: float
    health: float
    team: int
    last_seen: float
    velocity_x: float = 0.0
    velocity_y: float = 0.0
    velocity_z: float = 0.0
    prediction_confidence: float = 1.0
    lock_priority: float = 1.0

@dataclass
class StickyAimConfig:
    """Sticky aim configuration"""
    # Target selection
    max_lock_distance: float = 500.0
    max_lock_fov: float = 45.0
    target_switch_delay: float = 0.2
    
    # Tracking behavior
    lock_strength: float = 0.8  # 0.0 = no lock, 1.0 = perfect lock
    smoothing_factor: float = 3.0  # Higher = smoother movement
    prediction_enabled: bool = True
    prediction_time: float = 0.1  # Seconds to predict ahead
    
    # Humanization
    micro_adjustments: bool = True
    random_jitter: float = 0.02  # Small random movements
    fatigue_simulation: bool = True
    reaction_delay: float = 0.05  # Human reaction time
    
    # Persistence
    target_lost_timeout: float = 1.0  # Time before switching targets
    reacquisition_time: float = 0.3  # Time to reacquire lost target
    
    # Aim zones
    preferred_bone: str = "head"  # head, chest, center
    bone_switch_chance: float = 0.1  # Chance to switch aim point
    
    # Anti-detection
    detection_avoidance: bool = True
    max_snap_speed: float = 180.0  # Max degrees per second
    natural_drift: bool = True
    drift_amount: float = 0.5

class StickyAimSystem:
    def __init__(self, config: StickyAimConfig = None):
        self.config = config or StickyAimConfig()
        
        # Current state
        self.current_target: Optional[Target] = None
        self.target_locked: bool = False
        self.lock_start_time: float = 0.0
        self.last_target_switch: float = 0.0
        
        # Target tracking
        self.target_history: deque = deque(maxlen=100)
        self.available_targets: List[Target] = []
        self.target_velocities: dict = {}
        
        # Smoothing and humanization
        self.aim_accumulator: Tuple[float, float] = (0.0, 0.0)
        self.last_aim_time: float = 0.0
        self.fatigue_factor: float = 1.0
        self.micro_adjustment_timer: float = 0.0
        
        # Performance tracking
        self.lock_accuracy: float = 0.0
        self.target_switches: int = 0
        self.total_lock_time: float = 0.0
        
        print("🎯 Sticky Aim System initialized")
        
    def update_targets(self, targets: List[Target], local_player_pos: Tuple[float, float, float], 
                      local_player_angles: Tuple[float, float]) -> None:
        """Update available targets and their tracking data"""
        current_time = time.time()
        
        # Update target velocities
        for target in targets:
            if target.id in self.target_velocities:
                prev_pos, prev_time = self.target_velocities[target.id]
                dt = current_time - prev_time
                
                if dt > 0:
                    target.velocity_x = (target.x - prev_pos[0]) / dt
                    target.velocity_y = (target.y - prev_pos[1]) / dt
                    target.velocity_z = (target.z - prev_pos[2]) / dt
            
            # Store current position for next velocity calculation
            self.target_velocities[target.id] = ((target.x, target.y, target.z), current_time)
            target.last_seen = current_time
        
        # Calculate lock priorities
        for target in targets:
            target.lock_priority = self.calculate_lock_priority(
                target, local_player_pos, local_player_angles
            )
        
        self.available_targets = targets
        
    def calculate_lock_priority(self, target: Target, local_pos: Tuple[float, float, float], 
                               local_angles: Tuple[float, float]) -> float:
        """Calculate target lock priority based on multiple factors"""
        # Distance factor (closer = higher priority)
        distance = math.sqrt(
            (target.x - local_pos[0])**2 + 
            (target.y - local_pos[1])**2 + 
            (target.z - local_pos[2])**2
        )
        distance_factor = max(0, 1.0 - (distance / self.config.max_lock_distance))
        
        # FOV factor (closer to crosshair = higher priority)
        target_angles = self.calculate_aim_angles(local_pos, (target.x, target.y, target.z))
        angle_diff = self.calculate_angle_difference(local_angles, target_angles)
        fov_factor = max(0, 1.0 - (angle_diff / self.config.max_lock_fov))
        
        # Health factor (lower health = higher priority for finishing)
        health_factor = max(0.1, 1.0 - (target.health / 100.0))
        
        # Velocity factor (slower targets easier to track)
        velocity = math.sqrt(target.velocity_x**2 + target.velocity_y**2 + target.velocity_z**2)
        velocity_factor = max(0.1, 1.0 - min(velocity / 500.0, 1.0))
        
        # Current target bonus (sticky behavior)
        current_target_bonus = 1.5 if (self.current_target and target.id == self.current_target.id) else 1.0
        
        # Combine factors
        priority = (distance_factor * 0.3 + 
                   fov_factor * 0.4 + 
                   health_factor * 0.1 + 
                   velocity_factor * 0.2) * current_target_bonus
        
        return priority
        
    def select_target(self) -> Optional[Target]:
        """Select the best target for sticky aim"""
        current_time = time.time()
        
        # Check if we should switch targets
        if (self.current_target and 
            current_time - self.last_target_switch < self.config.target_switch_delay):
            return self.current_target
        
        # Filter valid targets
        valid_targets = [
            t for t in self.available_targets 
            if (t.health > 0 and 
                t.lock_priority > 0.1 and
                current_time - t.last_seen < self.config.target_lost_timeout)
        ]
        
        if not valid_targets:
            return None
        
        # Sort by priority
        valid_targets.sort(key=lambda t: t.lock_priority, reverse=True)
        best_target = valid_targets[0]
        
        # Check if we should switch to new target
        if (not self.current_target or 
            best_target.id != self.current_target.id):
            
            # Only switch if new target is significantly better
            if (not self.current_target or 
                best_target.lock_priority > self.current_target.lock_priority * 1.2):
                
                self.switch_target(best_target)
                return best_target
        
        return self.current_target
        
    def switch_target(self, new_target: Target) -> None:
        """Switch to a new target"""
        current_time = time.time()
        
        if self.current_target:
            print(f"🎯 Switching target: {self.current_target.id} → {new_target.id}")
        else:
            print(f"🎯 Acquiring target: {new_target.id}")
        
        self.current_target = new_target
        self.target_locked = True
        self.lock_start_time = current_time
        self.last_target_switch = current_time
        self.target_switches += 1
        
        # Reset smoothing accumulator for new target
        self.aim_accumulator = (0.0, 0.0)
        
    def calculate_aim_angles(self, local_pos: Tuple[float, float, float], 
                           target_pos: Tuple[float, float, float]) -> Tuple[float, float]:
        """Calculate required aim angles to target"""
        dx = target_pos[0] - local_pos[0]
        dy = target_pos[1] - local_pos[1]
        dz = target_pos[2] - local_pos[2]
        
        distance = math.sqrt(dx*dx + dy*dy + dz*dz)
        
        if distance == 0:
            return (0.0, 0.0)
        
        # Calculate angles
        yaw = math.atan2(dy, dx) * 180.0 / math.pi
        pitch = -math.asin(dz / distance) * 180.0 / math.pi
        
        return (pitch, yaw)
        
    def calculate_angle_difference(self, angles1: Tuple[float, float], 
                                 angles2: Tuple[float, float]) -> float:
        """Calculate angular difference between two angle pairs"""
        pitch_diff = abs(angles1[0] - angles2[0])
        yaw_diff = abs(angles1[1] - angles2[1])
        
        # Handle yaw wraparound
        if yaw_diff > 180:
            yaw_diff = 360 - yaw_diff
        
        return math.sqrt(pitch_diff*pitch_diff + yaw_diff*yaw_diff)
        
    def predict_target_position(self, target: Target, prediction_time: float) -> Tuple[float, float, float]:
        """Predict target position based on velocity"""
        if not self.config.prediction_enabled:
            return (target.x, target.y, target.z)
        
        # Simple linear prediction
        predicted_x = target.x + (target.velocity_x * prediction_time)
        predicted_y = target.y + (target.velocity_y * prediction_time)
        predicted_z = target.z + (target.velocity_z * prediction_time)
        
        return (predicted_x, predicted_y, predicted_z)
        
    def apply_humanization(self, aim_delta: Tuple[float, float]) -> Tuple[float, float]:
        """Apply humanization effects to aim movement"""
        current_time = time.time()
        dt = current_time - self.last_aim_time
        
        pitch_delta, yaw_delta = aim_delta
        
        # Apply reaction delay
        if dt < self.config.reaction_delay:
            return (0.0, 0.0)
        
        # Apply fatigue simulation
        if self.config.fatigue_simulation:
            lock_duration = current_time - self.lock_start_time
            self.fatigue_factor = max(0.7, 1.0 - (lock_duration / 30.0))  # Fatigue over 30 seconds
            pitch_delta *= self.fatigue_factor
            yaw_delta *= self.fatigue_factor
        
        # Apply micro adjustments
        if self.config.micro_adjustments:
            if current_time - self.micro_adjustment_timer > random.uniform(0.1, 0.3):
                micro_pitch = random.uniform(-self.config.random_jitter, self.config.random_jitter)
                micro_yaw = random.uniform(-self.config.random_jitter, self.config.random_jitter)
                pitch_delta += micro_pitch
                yaw_delta += micro_yaw
                self.micro_adjustment_timer = current_time
        
        # Apply natural drift
        if self.config.natural_drift:
            drift_pitch = math.sin(current_time * 0.5) * self.config.drift_amount * 0.1
            drift_yaw = math.cos(current_time * 0.7) * self.config.drift_amount * 0.1
            pitch_delta += drift_pitch
            yaw_delta += drift_yaw
        
        # Limit maximum movement speed for detection avoidance
        if self.config.detection_avoidance:
            max_delta = self.config.max_snap_speed * dt
            total_delta = math.sqrt(pitch_delta*pitch_delta + yaw_delta*yaw_delta)
            
            if total_delta > max_delta:
                scale = max_delta / total_delta
                pitch_delta *= scale
                yaw_delta *= scale
        
        self.last_aim_time = current_time
        return (pitch_delta, yaw_delta)
        
    def calculate_sticky_aim(self, local_pos: Tuple[float, float, float], 
                           current_angles: Tuple[float, float]) -> Optional[Tuple[float, float]]:
        """Calculate sticky aim adjustment"""
        if not self.target_locked or not self.current_target:
            return None
        
        current_time = time.time()
        
        # Check if target is still valid
        if (self.current_target.health <= 0 or 
            current_time - self.current_target.last_seen > self.config.target_lost_timeout):
            self.release_target()
            return None
        
        # Get predicted target position
        predicted_pos = self.predict_target_position(
            self.current_target, self.config.prediction_time
        )
        
        # Calculate required angles
        target_angles = self.calculate_aim_angles(local_pos, predicted_pos)
        
        # Calculate angle difference
        pitch_diff = target_angles[0] - current_angles[0]
        yaw_diff = target_angles[1] - current_angles[1]
        
        # Handle yaw wraparound
        if yaw_diff > 180:
            yaw_diff -= 360
        elif yaw_diff < -180:
            yaw_diff += 360
        
        # Apply lock strength
        pitch_diff *= self.config.lock_strength
        yaw_diff *= self.config.lock_strength
        
        # Apply smoothing
        self.aim_accumulator = (
            self.aim_accumulator[0] + pitch_diff / self.config.smoothing_factor,
            self.aim_accumulator[1] + yaw_diff / self.config.smoothing_factor
        )
        
        # Get smoothed movement
        smooth_pitch = self.aim_accumulator[0] / self.config.smoothing_factor
        smooth_yaw = self.aim_accumulator[1] / self.config.smoothing_factor
        
        # Apply decay to accumulator
        self.aim_accumulator = (
            self.aim_accumulator[0] * 0.9,
            self.aim_accumulator[1] * 0.9
        )
        
        # Apply humanization
        humanized_delta = self.apply_humanization((smooth_pitch, smooth_yaw))
        
        # Update performance metrics
        self.update_performance_metrics(target_angles, current_angles)
        
        return humanized_delta
        
    def release_target(self) -> None:
        """Release current target"""
        if self.current_target:
            lock_duration = time.time() - self.lock_start_time
            self.total_lock_time += lock_duration
            print(f"🎯 Released target {self.current_target.id} (locked for {lock_duration:.2f}s)")
        
        self.current_target = None
        self.target_locked = False
        self.aim_accumulator = (0.0, 0.0)
        
    def update_performance_metrics(self, target_angles: Tuple[float, float], 
                                 current_angles: Tuple[float, float]) -> None:
        """Update performance tracking metrics"""
        angle_error = self.calculate_angle_difference(target_angles, current_angles)
        
        # Update accuracy (lower error = higher accuracy)
        max_error = 10.0  # Maximum expected error in degrees
        accuracy = max(0, 1.0 - (angle_error / max_error))
        
        # Exponential moving average
        alpha = 0.1
        self.lock_accuracy = (alpha * accuracy) + ((1 - alpha) * self.lock_accuracy)
        
    def get_status(self) -> dict:
        """Get current sticky aim status"""
        return {
            'target_locked': self.target_locked,
            'current_target_id': self.current_target.id if self.current_target else None,
            'lock_accuracy': self.lock_accuracy,
            'target_switches': self.target_switches,
            'total_lock_time': self.total_lock_time,
            'available_targets': len(self.available_targets),
            'fatigue_factor': self.fatigue_factor
        }
        
    def update(self, targets: List[Target], local_pos: Tuple[float, float, float], 
              local_angles: Tuple[float, float]) -> Optional[Tuple[float, float]]:
        """Main update function - call this every frame"""
        # Update target tracking data
        self.update_targets(targets, local_pos, local_angles)
        
        # Select best target
        selected_target = self.select_target()
        
        if not selected_target:
            if self.target_locked:
                self.release_target()
            return None
        
        # Calculate sticky aim adjustment
        return self.calculate_sticky_aim(local_pos, local_angles)

if __name__ == "__main__":
    print("🎯 STICKY AIM SYSTEM")
    print("====================")
    print("Advanced aim lock with target tracking and humanization")
    print("Features:")
    print("• Continuous target tracking")
    print("• Smooth aim transitions")
    print("• Target prediction")
    print("• Humanization effects")
    print("• Anti-detection measures")
    print("• Performance metrics")
