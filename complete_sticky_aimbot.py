#!/usr/bin/env python3
"""
🎯 COMPLETE STICKY AIM AIMBOT SYSTEM
====================================
Full implementation of advanced sticky aim aimbot with all features.

⚠️  EDUCATIONAL PURPOSE ONLY ⚠️
This software is for educational and research purposes only.
"""

import sys
import os
import time
import math
import random
import threading
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from collections import deque
import json

# GUI imports
import tkinter as tk
from tkinter import ttk, messagebox

# Computer Vision imports
try:
    import cv2
    import numpy as np
    CV_AVAILABLE = True
except ImportError:
    CV_AVAILABLE = False
    print("⚠️ OpenCV not available - CV detection disabled")

# AI Detection imports
try:
    from ultralytics import YOLO
    import torch
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    print("⚠️ AI libraries not available - AI detection disabled")

# System imports
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("⚠️ psutil not available - system monitoring limited")

# Windows API imports
try:
    import win32api
    import win32con
    import win32gui
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("⚠️ Win32 API not available - mouse control limited")

# Educational warning
EDUCATIONAL_WARNING = """
🎯 STICKY AIM AIMBOT - EDUCATIONAL SOFTWARE
==========================================

⚠️  IMPORTANT NOTICE ⚠️

This software is provided for EDUCATIONAL and RESEARCH purposes only.

PERMITTED USES:
✅ Learning about computer vision and AI
✅ Understanding game mechanics and algorithms
✅ Research into human-computer interaction
✅ Educational demonstrations and tutorials
✅ Offline testing and experimentation

PROHIBITED USES:
❌ Online competitive gaming
❌ Violating game Terms of Service
❌ Gaining unfair advantages in multiplayer games
❌ Commercial use without proper licensing
❌ Malicious or harmful activities

LEGAL DISCLAIMER:
The authors and contributors are not responsible for any misuse
of this software. Users are solely responsible for ensuring their
use complies with applicable laws and regulations.

By using this software, you acknowledge that you understand and
agree to these terms.
"""

@dataclass
class Target:
    """Target data structure"""
    id: str
    x: float
    y: float
    z: float = 0.0
    width: float = 50.0
    height: float = 50.0
    confidence: float = 1.0
    detection_method: str = "unknown"
    class_name: str = "target"
    timestamp: float = field(default_factory=time.time)
    health: float = 100.0
    team: int = 0
    velocity_x: float = 0.0
    velocity_y: float = 0.0
    velocity_z: float = 0.0
    lock_priority: float = 0.0

@dataclass
class AimAdjustment:
    """Aim adjustment data structure"""
    delta_x: float
    delta_y: float
    confidence: float = 1.0
    timestamp: float = field(default_factory=time.time)
    prediction_used: bool = False
    smoothing_applied: bool = False

@dataclass
class StickyAimConfig:
    """Sticky aim configuration"""
    # Target selection
    max_lock_distance: float = 500.0
    max_lock_fov: float = 45.0
    target_switch_delay: float = 0.3
    
    # Tracking behavior
    lock_strength: float = 0.8
    smoothing_factor: float = 4.0
    prediction_enabled: bool = True
    prediction_time: float = 0.15
    
    # Humanization
    micro_adjustments: bool = True
    random_jitter: float = 0.02
    fatigue_simulation: bool = True
    reaction_delay: float = 0.05
    
    # Anti-detection
    detection_avoidance: bool = True
    max_snap_speed: float = 180.0
    natural_drift: bool = True
    drift_amount: float = 0.5

class MathUtils:
    """Mathematical utility functions"""
    
    @staticmethod
    def calculate_distance(pos1: Tuple[float, float, float], 
                          pos2: Tuple[float, float, float]) -> float:
        """Calculate 3D distance between positions"""
        return math.sqrt(
            (pos2[0] - pos1[0])**2 + 
            (pos2[1] - pos1[1])**2 + 
            (pos2[2] - pos1[2])**2
        )
    
    @staticmethod
    def calculate_angle(from_pos: Tuple[float, float, float], 
                       to_pos: Tuple[float, float, float]) -> Tuple[float, float]:
        """Calculate pitch and yaw angles to target"""
        dx = to_pos[0] - from_pos[0]
        dy = to_pos[1] - from_pos[1]
        dz = to_pos[2] - from_pos[2]
        
        distance = math.sqrt(dx*dx + dy*dy + dz*dz)
        
        if distance == 0:
            return (0.0, 0.0)
        
        yaw = math.atan2(dy, dx) * 180.0 / math.pi
        pitch = -math.asin(dz / distance) * 180.0 / math.pi
        
        return (pitch, yaw)
    
    @staticmethod
    def normalize_angle(angle: float) -> float:
        """Normalize angle to [-180, 180] range"""
        while angle > 180:
            angle -= 360
        while angle < -180:
            angle += 360
        return angle
    
    @staticmethod
    def interpolate_smooth(start: float, end: float, t: float) -> float:
        """Smooth interpolation between values"""
        t = t * t * (3.0 - 2.0 * t)  # Cubic easing
        return start + (end - start) * t
    
    @staticmethod
    def predict_position(current_pos: Tuple[float, float], 
                        velocity: Tuple[float, float], 
                        time_ahead: float) -> Tuple[float, float]:
        """Predict future position based on velocity"""
        return (
            current_pos[0] + velocity[0] * time_ahead,
            current_pos[1] + velocity[1] * time_ahead
        )

class ScreenCapture:
    """Screen capture utility"""
    
    def __init__(self):
        """Initialize screen capture"""
        self.last_frame = None
        
    def capture(self) -> Optional[np.ndarray]:
        """Capture screen"""
        if not CV_AVAILABLE:
            return None
            
        try:
            # Simple screen capture using OpenCV (placeholder)
            # In real implementation, would use proper screen capture
            
            # Create a dummy frame for demonstration
            frame = np.zeros((600, 800, 3), dtype=np.uint8)
            
            # Add some random "targets" for demonstration
            for i in range(random.randint(1, 5)):
                x = random.randint(50, 750)
                y = random.randint(50, 550)
                cv2.rectangle(frame, (x-25, y-25), (x+25, y+25), (0, 255, 0), 2)
                cv2.putText(frame, f"Target {i}", (x-20, y-30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            self.last_frame = frame
            return frame
            
        except Exception as e:
            print(f"Screen capture error: {e}")
            return None

class ComputerVisionDetector:
    """Computer vision-based target detection"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize CV detector"""
        self.config = config or {}
        self.screen_capture = ScreenCapture()
        self.yolo_model = None
        self.detection_count = 0
        
        if AI_AVAILABLE:
            try:
                self.yolo_model = YOLO('yolov8n.pt')
                print("✅ YOLO model loaded successfully")
            except Exception as e:
                print(f"⚠️ YOLO model loading failed: {e}")
    
    def detect_targets(self) -> List[Target]:
        """Detect targets using computer vision"""
        if not CV_AVAILABLE:
            return []
        
        try:
            frame = self.screen_capture.capture()
            if frame is None:
                return []
            
            targets = []
            
            # YOLO detection if available
            if self.yolo_model and AI_AVAILABLE:
                targets.extend(self._detect_with_yolo(frame))
            
            # Fallback: simple color detection
            targets.extend(self._detect_by_color(frame))
            
            self.detection_count += 1
            return targets[:10]  # Limit to 10 targets
            
        except Exception as e:
            print(f"Detection error: {e}")
            return []
    
    def _detect_with_yolo(self, frame: np.ndarray) -> List[Target]:
        """Detect using YOLO"""
        try:
            results = self.yolo_model(frame, conf=0.5, verbose=False)
            targets = []
            
            for result in results:
                if result.boxes is None:
                    continue
                    
                for i, box in enumerate(result.boxes):
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()
                    class_id = int(box.cls[0].cpu().numpy())
                    
                    if self.yolo_model.names[class_id] == 'person':
                        center_x = (x1 + x2) / 2
                        center_y = (y1 + y2) / 2
                        
                        target = Target(
                            id=f"yolo_{i}",
                            x=center_x,
                            y=center_y,
                            width=x2-x1,
                            height=y2-y1,
                            confidence=float(confidence),
                            detection_method='yolo',
                            class_name='person'
                        )
                        targets.append(target)
            
            return targets
            
        except Exception as e:
            print(f"YOLO detection error: {e}")
            return []
    
    def _detect_by_color(self, frame: np.ndarray) -> List[Target]:
        """Simple color-based detection"""
        try:
            # Convert to HSV
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            
            # Define green color range (for demo targets)
            lower_green = np.array([40, 50, 50])
            upper_green = np.array([80, 255, 255])
            
            # Create mask
            mask = cv2.inRange(hsv, lower_green, upper_green)
            
            # Find contours
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            targets = []
            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                if area > 100:  # Minimum area threshold
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    target = Target(
                        id=f"color_{i}",
                        x=x + w/2,
                        y=y + h/2,
                        width=w,
                        height=h,
                        confidence=0.7,
                        detection_method='color',
                        class_name='target'
                    )
                    targets.append(target)
            
            return targets
            
        except Exception as e:
            print(f"Color detection error: {e}")
            return []

class MouseController:
    """Advanced mouse movement controller"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize mouse controller"""
        self.config = config or {}
        self.sensitivity = self.config.get('sensitivity', 1.0)
        self.smoothing_enabled = self.config.get('smoothing_enabled', True)
        self.humanization_enabled = self.config.get('humanization_enabled', True)
        
        self.movement_history = []
        self.total_movements = 0
        
    def apply_aim_adjustment(self, adjustment: AimAdjustment) -> bool:
        """Apply aim adjustment using mouse movement"""
        try:
            if not WIN32_AVAILABLE:
                # Simulate movement without actual mouse control
                print(f"Simulated mouse movement: ({adjustment.delta_x:.2f}, {adjustment.delta_y:.2f})")
                return True
            
            # Convert to pixel movement
            pixel_x = adjustment.delta_x * self.sensitivity * 10
            pixel_y = adjustment.delta_y * self.sensitivity * 10
            
            # Apply humanization
            if self.humanization_enabled:
                pixel_x, pixel_y = self._apply_humanization(pixel_x, pixel_y)
            
            # Get current position
            current_pos = win32gui.GetCursorPos()
            
            # Calculate new position
            new_x = int(current_pos[0] + pixel_x)
            new_y = int(current_pos[1] + pixel_y)
            
            # Move mouse
            win32api.SetCursorPos((new_x, new_y))
            
            self.total_movements += 1
            return True
            
        except Exception as e:
            print(f"Mouse movement error: {e}")
            return False
    
    def _apply_humanization(self, x: float, y: float) -> Tuple[float, float]:
        """Apply humanization to mouse movement"""
        # Add small random jitter
        jitter_x = random.uniform(-0.5, 0.5)
        jitter_y = random.uniform(-0.5, 0.5)
        
        return (x + jitter_x, y + jitter_y)

class StickyTargetTracker:
    """Advanced sticky aim target tracker"""
    
    def __init__(self, config: StickyAimConfig = None):
        """Initialize sticky target tracker"""
        self.config = config or StickyAimConfig()
        
        # Core tracking state
        self.current_target: Optional[Target] = None
        self.target_locked = False
        self.lock_start_time = 0.0
        self.last_target_switch = 0.0
        
        # Target management
        self.available_targets: List[Target] = []
        self.target_history: Dict[str, deque] = {}
        self.target_priorities: Dict[str, float] = {}
        
        # Smoothing and humanization
        self.aim_accumulator = np.array([0.0, 0.0])
        self.last_aim_time = 0.0
        self.fatigue_factor = 1.0
        self.micro_adjustment_timer = 0.0
        self.natural_drift_phase = 0.0
        
        # Performance tracking
        self.total_locks = 0
        self.successful_predictions = 0
        self.total_predictions = 0
        
        print("🎯 Sticky Target Tracker initialized")
    
    def update_targets(self, targets: List[Target]):
        """Update available targets"""
        self.available_targets = targets
        current_time = time.time()
        
        # Update target histories and calculate priorities
        for target in targets:
            # Initialize history if new target
            if target.id not in self.target_history:
                self.target_history[target.id] = deque(maxlen=10)
            
            # Store position history
            self.target_history[target.id].append({
                'pos': (target.x, target.y, target.z),
                'time': current_time
            })
            
            # Calculate velocity if we have enough history
            if len(self.target_history[target.id]) >= 2:
                recent = list(self.target_history[target.id])
                dt = recent[-1]['time'] - recent[-2]['time']
                if dt > 0:
                    dx = recent[-1]['pos'][0] - recent[-2]['pos'][0]
                    dy = recent[-1]['pos'][1] - recent[-2]['pos'][1]
                    target.velocity_x = dx / dt
                    target.velocity_y = dy / dt
        
        # Calculate target priorities
        self._calculate_target_priorities()
    
    def _calculate_target_priorities(self):
        """Calculate priority scores for all targets"""
        local_pos = (400, 300, 0)  # Screen center as reference
        
        for target in self.available_targets:
            priority = 0.0
            
            # Distance factor (closer = higher priority)
            distance = MathUtils.calculate_distance(local_pos, (target.x, target.y, target.z))
            if distance > 0:
                distance_factor = max(0, 1.0 - (distance / self.config.max_lock_distance))
                priority += distance_factor * 0.3
            
            # FOV factor (closer to center = higher priority)
            screen_center = (400, 300)
            target_distance_from_center = math.sqrt(
                (target.x - screen_center[0])**2 + (target.y - screen_center[1])**2
            )
            fov_factor = max(0, 1.0 - (target_distance_from_center / 200))
            priority += fov_factor * 0.4
            
            # Confidence factor
            priority += target.confidence * 0.2
            
            # Current target bonus (sticky behavior)
            if self.current_target and target.id == self.current_target.id:
                priority *= 1.5
            
            self.target_priorities[target.id] = priority
            target.lock_priority = priority
    
    def get_current_target(self) -> Optional[Target]:
        """Get current target using sticky aim logic"""
        current_time = time.time()
        
        # Check if current target is still valid
        if self.current_target:
            current_target_valid = any(
                t.id == self.current_target.id for t in self.available_targets
            )
            
            if not current_target_valid:
                self._release_target()
        
        # Select new target if needed
        if not self.current_target and self.available_targets:
            self._select_best_target()
        
        return self.current_target
    
    def _select_best_target(self):
        """Select the best target based on priorities"""
        current_time = time.time()
        
        # Respect target switch delay
        if current_time - self.last_target_switch < self.config.target_switch_delay:
            return
        
        # Find highest priority target
        best_target = max(self.available_targets, key=lambda t: t.lock_priority)
        
        if best_target.lock_priority > 0.1:
            self._lock_target(best_target)
    
    def _lock_target(self, target: Target):
        """Lock onto a new target"""
        current_time = time.time()
        
        if self.current_target:
            self._release_target()
        
        self.current_target = target
        self.target_locked = True
        self.lock_start_time = current_time
        self.last_target_switch = current_time
        self.total_locks += 1
        
        # Reset aim accumulator
        self.aim_accumulator = np.array([0.0, 0.0])
        
        print(f"🎯 Locked onto target {target.id}")
    
    def _release_target(self):
        """Release current target"""
        if self.current_target:
            print(f"🎯 Released target {self.current_target.id}")
        
        self.current_target = None
        self.target_locked = False
        self.aim_accumulator = np.array([0.0, 0.0])
    
    def calculate_aim_adjustment(self) -> Optional[AimAdjustment]:
        """Calculate aim adjustment for current target"""
        if not self.current_target or not self.target_locked:
            return None
        
        current_time = time.time()
        
        # Get target position (with prediction if enabled)
        target_pos = self._get_target_position_with_prediction()
        if target_pos is None:
            return None
        
        # Calculate screen center (crosshair position)
        screen_center = (400, 300)
        
        # Calculate required adjustment
        delta_x = (target_pos[0] - screen_center[0]) / 100.0  # Normalize
        delta_y = (target_pos[1] - screen_center[1]) / 100.0
        
        # Apply lock strength
        delta_x *= self.config.lock_strength
        delta_y *= self.config.lock_strength
        
        # Apply smoothing
        dt = current_time - self.last_aim_time if self.last_aim_time > 0 else 0.016
        
        # Accumulate smooth movement
        self.aim_accumulator += np.array([delta_x, delta_y]) / self.config.smoothing_factor
        
        # Get smoothed adjustment
        smooth_delta = self.aim_accumulator / self.config.smoothing_factor
        
        # Apply decay to accumulator
        self.aim_accumulator *= 0.9
        
        # Apply humanization
        smooth_delta = self._apply_humanization(smooth_delta, current_time)
        
        self.last_aim_time = current_time
        
        return AimAdjustment(
            delta_x=smooth_delta[0],
            delta_y=smooth_delta[1],
            confidence=self._calculate_confidence(),
            prediction_used=self.config.prediction_enabled,
            smoothing_applied=True
        )
    
    def _get_target_position_with_prediction(self) -> Optional[Tuple[float, float]]:
        """Get target position with movement prediction"""
        if not self.current_target:
            return None
        
        base_pos = (self.current_target.x, self.current_target.y)
        
        # Apply prediction if enabled
        if self.config.prediction_enabled and (self.current_target.velocity_x != 0 or self.current_target.velocity_y != 0):
            predicted_pos = MathUtils.predict_position(
                base_pos, 
                (self.current_target.velocity_x, self.current_target.velocity_y), 
                self.config.prediction_time
            )
            self.total_predictions += 1
            return predicted_pos
        
        return base_pos
    
    def _apply_humanization(self, delta: np.ndarray, current_time: float) -> np.ndarray:
        """Apply humanization effects"""
        # Fatigue simulation
        if self.config.fatigue_simulation:
            lock_duration = current_time - self.lock_start_time
            self.fatigue_factor = max(0.7, 1.0 - (lock_duration / 60.0))
            delta *= self.fatigue_factor
        
        # Micro adjustments
        if self.config.micro_adjustments:
            if current_time - self.micro_adjustment_timer > random.uniform(0.1, 0.4):
                micro_x = random.uniform(-self.config.random_jitter, self.config.random_jitter)
                micro_y = random.uniform(-self.config.random_jitter, self.config.random_jitter)
                delta += np.array([micro_x, micro_y])
                self.micro_adjustment_timer = current_time
        
        # Natural drift
        if self.config.natural_drift:
            self.natural_drift_phase += 0.1
            drift_x = math.sin(self.natural_drift_phase) * self.config.drift_amount * 0.01
            drift_y = math.cos(self.natural_drift_phase * 0.7) * self.config.drift_amount * 0.01
            delta += np.array([drift_x, drift_y])
        
        return delta
    
    def _calculate_confidence(self) -> float:
        """Calculate confidence in current aim adjustment"""
        if not self.current_target:
            return 0.0
        
        confidence = self.current_target.confidence
        confidence *= self.fatigue_factor
        
        return max(0.1, confidence)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get tracking statistics"""
        return {
            'total_locks': self.total_locks,
            'current_target_id': self.current_target.id if self.current_target else None,
            'target_locked': self.target_locked,
            'fatigue_factor': self.fatigue_factor,
            'available_targets': len(self.available_targets),
            'prediction_accuracy': (self.successful_predictions / max(self.total_predictions, 1))
        }

class AimbotEngine:
    """Main aimbot engine"""
    
    def __init__(self):
        """Initialize aimbot engine"""
        self.running = False
        self.paused = False
        
        # Components
        self.detector = ComputerVisionDetector()
        self.controller = MouseController()
        self.tracker = StickyTargetTracker()
        
        # Statistics
        self.targets_detected = 0
        self.targets_locked = 0
        self.fps = 0.0
        self.start_time = 0.0
        
        print("🎯 Aimbot Engine initialized")
    
    def start(self) -> bool:
        """Start the aimbot"""
        if self.running:
            return False
        
        self.running = True
        self.start_time = time.time()
        
        # Start main loop in separate thread
        self.main_thread = threading.Thread(target=self._main_loop, daemon=True)
        self.main_thread.start()
        
        print("🚀 Aimbot started")
        return True
    
    def stop(self) -> bool:
        """Stop the aimbot"""
        if not self.running:
            return False
        
        self.running = False
        print("🛑 Aimbot stopped")
        return True
    
    def pause(self) -> bool:
        """Pause the aimbot"""
        self.paused = True
        print("⏸️ Aimbot paused")
        return True
    
    def resume(self) -> bool:
        """Resume the aimbot"""
        self.paused = False
        print("▶️ Aimbot resumed")
        return True
    
    def _main_loop(self):
        """Main aimbot processing loop"""
        frame_count = 0
        last_fps_time = time.time()
        
        while self.running:
            if self.paused:
                time.sleep(0.1)
                continue
            
            try:
                # Detect targets
                targets = self.detector.detect_targets()
                self.targets_detected += len(targets)
                
                if targets:
                    # Update tracker
                    self.tracker.update_targets(targets)
                    
                    # Get current target
                    current_target = self.tracker.get_current_target()
                    
                    if current_target:
                        self.targets_locked += 1
                        
                        # Calculate aim adjustment
                        adjustment = self.tracker.calculate_aim_adjustment()
                        
                        if adjustment:
                            # Apply adjustment
                            self.controller.apply_aim_adjustment(adjustment)
                
                # Update FPS
                frame_count += 1
                current_time = time.time()
                if current_time - last_fps_time >= 1.0:
                    self.fps = frame_count / (current_time - last_fps_time)
                    frame_count = 0
                    last_fps_time = current_time
                
                # Target 60 FPS
                time.sleep(1.0 / 60.0)
                
            except Exception as e:
                print(f"Main loop error: {e}")
                time.sleep(0.1)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get aimbot statistics"""
        runtime = time.time() - self.start_time if self.start_time > 0 else 0
        tracker_stats = self.tracker.get_statistics()
        
        return {
            'running': self.running,
            'paused': self.paused,
            'targets_detected': self.targets_detected,
            'targets_locked': self.targets_locked,
            'fps': self.fps,
            'runtime': runtime,
            'current_target': tracker_stats.get('current_target_id'),
            'target_locked': tracker_stats.get('target_locked', False),
            'available_targets': tracker_stats.get('available_targets', 0)
        }

class StickyAimbotGUI:
    """Main GUI for the sticky aimbot"""
    
    def __init__(self):
        """Initialize GUI"""
        self.root = tk.Tk()
        self.root.title("🎯 Sticky Aim Aimbot - Educational System")
        self.root.geometry("900x700")
        self.root.configure(bg='#1a0d1a')
        
        # Aimbot engine
        self.aimbot = AimbotEngine()
        
        # Create GUI
        self.create_widgets()
        
        # Show educational warning
        self.show_educational_warning()
        
        # Start stats update
        self.update_stats()
    
    def show_educational_warning(self):
        """Show educational warning"""
        result = messagebox.askokcancel("Educational Notice", EDUCATIONAL_WARNING)
        if not result:
            self.root.destroy()
            return
    
    def create_widgets(self):
        """Create GUI widgets"""
        # Title
        title_label = tk.Label(
            self.root, 
            text="🎯 STICKY AIM AIMBOT", 
            font=('Arial', 24, 'bold'), 
            bg='#1a0d1a', 
            fg='white'
        )
        title_label.pack(pady=20)
        
        # Status frame
        status_frame = tk.Frame(self.root, bg='#1a0d1a')
        status_frame.pack(pady=10)
        
        tk.Label(status_frame, text="Status:", font=('Arial', 14, 'bold'), 
                bg='#1a0d1a', fg='white').pack(side='left')
        
        self.status_label = tk.Label(
            status_frame, 
            text="STOPPED", 
            font=('Arial', 14, 'bold'), 
            bg='#1a0d1a', 
            fg='red'
        )
        self.status_label.pack(side='left', padx=10)
        
        # Control buttons
        button_frame = tk.Frame(self.root, bg='#1a0d1a')
        button_frame.pack(pady=20)
        
        self.start_button = tk.Button(
            button_frame,
            text="🚀 Start Aimbot",
            font=('Arial', 16, 'bold'),
            bg='#4CAF50',
            fg='white',
            width=15,
            height=2,
            command=self.start_aimbot
        )
        self.start_button.pack(side='left', padx=10)
        
        self.stop_button = tk.Button(
            button_frame,
            text="🛑 Stop Aimbot",
            font=('Arial', 16, 'bold'),
            bg='#f44336',
            fg='white',
            width=15,
            height=2,
            command=self.stop_aimbot,
            state='disabled'
        )
        self.stop_button.pack(side='left', padx=10)
        
        self.pause_button = tk.Button(
            button_frame,
            text="⏸️ Pause",
            font=('Arial', 16, 'bold'),
            bg='#FF9800',
            fg='white',
            width=15,
            height=2,
            command=self.pause_aimbot,
            state='disabled'
        )
        self.pause_button.pack(side='left', padx=10)
        
        # Configuration frame
        config_frame = tk.LabelFrame(
            self.root, 
            text="🎯 Sticky Aim Configuration", 
            font=('Arial', 14, 'bold'),
            bg='#1a0d1a', 
            fg='white'
        )
        config_frame.pack(pady=20, padx=20, fill='x')
        
        # Lock strength
        tk.Label(config_frame, text="Lock Strength:", 
                bg='#1a0d1a', fg='white', font=('Arial', 12)).grid(row=0, column=0, sticky='w', padx=10, pady=5)
        
        self.lock_strength_var = tk.DoubleVar(value=0.8)
        lock_strength_scale = tk.Scale(
            config_frame,
            from_=0.1,
            to=1.0,
            resolution=0.1,
            orient='horizontal',
            variable=self.lock_strength_var,
            bg='#1a0d1a',
            fg='white',
            length=200
        )
        lock_strength_scale.grid(row=0, column=1, padx=10, pady=5)
        
        # Smoothing factor
        tk.Label(config_frame, text="Smoothing Factor:", 
                bg='#1a0d1a', fg='white', font=('Arial', 12)).grid(row=1, column=0, sticky='w', padx=10, pady=5)
        
        self.smoothing_var = tk.DoubleVar(value=4.0)
        smoothing_scale = tk.Scale(
            config_frame,
            from_=1.0,
            to=10.0,
            resolution=0.5,
            orient='horizontal',
            variable=self.smoothing_var,
            bg='#1a0d1a',
            fg='white',
            length=200
        )
        smoothing_scale.grid(row=1, column=1, padx=10, pady=5)
        
        # Prediction enabled
        self.prediction_var = tk.BooleanVar(value=True)
        prediction_check = tk.Checkbutton(
            config_frame,
            text="Enable Target Prediction",
            variable=self.prediction_var,
            bg='#1a0d1a',
            fg='white',
            font=('Arial', 12),
            selectcolor='#333333'
        )
        prediction_check.grid(row=2, column=0, columnspan=2, sticky='w', padx=10, pady=5)
        
        # Humanization enabled
        self.humanization_var = tk.BooleanVar(value=True)
        humanization_check = tk.Checkbutton(
            config_frame,
            text="Enable Humanization",
            variable=self.humanization_var,
            bg='#1a0d1a',
            fg='white',
            font=('Arial', 12),
            selectcolor='#333333'
        )
        humanization_check.grid(row=3, column=0, columnspan=2, sticky='w', padx=10, pady=5)
        
        # Statistics frame
        stats_frame = tk.LabelFrame(
            self.root, 
            text="📊 Real-Time Statistics", 
            font=('Arial', 14, 'bold'),
            bg='#1a0d1a', 
            fg='white'
        )
        stats_frame.pack(pady=20, padx=20, fill='both', expand=True)
        
        # Stats text
        self.stats_text = tk.Text(
            stats_frame,
            height=15,
            bg='#2a1a2a',
            fg='lime',
            font=('Courier', 11),
            wrap=tk.WORD
        )
        self.stats_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Scrollbar for stats
        scrollbar = tk.Scrollbar(self.stats_text)
        scrollbar.pack(side='right', fill='y')
        self.stats_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.stats_text.yview)
    
    def start_aimbot(self):
        """Start the aimbot"""
        try:
            # Update configuration
            self.aimbot.tracker.config.lock_strength = self.lock_strength_var.get()
            self.aimbot.tracker.config.smoothing_factor = self.smoothing_var.get()
            self.aimbot.tracker.config.prediction_enabled = self.prediction_var.get()
            
            if self.aimbot.start():
                self.status_label.config(text="RUNNING", fg='lime')
                self.start_button.config(state='disabled')
                self.stop_button.config(state='normal')
                self.pause_button.config(state='normal')
                messagebox.showinfo("Success", "🎯 Sticky Aimbot started successfully!")
            else:
                messagebox.showerror("Error", "Failed to start aimbot")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start aimbot:\n{str(e)}")
    
    def stop_aimbot(self):
        """Stop the aimbot"""
        try:
            if self.aimbot.stop():
                self.status_label.config(text="STOPPED", fg='red')
                self.start_button.config(state='normal')
                self.stop_button.config(state='disabled')
                self.pause_button.config(state='disabled')
                messagebox.showinfo("Success", "🛑 Aimbot stopped successfully!")
            else:
                messagebox.showerror("Error", "Failed to stop aimbot")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop aimbot:\n{str(e)}")
    
    def pause_aimbot(self):
        """Pause/resume the aimbot"""
        try:
            if self.aimbot.paused:
                self.aimbot.resume()
                self.pause_button.config(text="⏸️ Pause")
                self.status_label.config(text="RUNNING", fg='lime')
            else:
                self.aimbot.pause()
                self.pause_button.config(text="▶️ Resume")
                self.status_label.config(text="PAUSED", fg='orange')
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to pause/resume aimbot:\n{str(e)}")
    
    def update_stats(self):
        """Update statistics display"""
        try:
            stats = self.aimbot.get_statistics()
            
            stats_text = "🎯 STICKY AIM AIMBOT - REAL-TIME STATISTICS\n"
            stats_text += "=" * 60 + "\n\n"
            
            # System Status
            stats_text += "📊 SYSTEM STATUS:\n"
            stats_text += f"   Status: {'RUNNING' if stats['running'] else 'STOPPED'}\n"
            stats_text += f"   Paused: {'YES' if stats['paused'] else 'NO'}\n"
            stats_text += f"   Runtime: {stats['runtime']:.1f} seconds\n"
            stats_text += f"   FPS: {stats['fps']:.1f}\n\n"
            
            # Detection Statistics
            stats_text += "🔍 DETECTION STATISTICS:\n"
            stats_text += f"   Targets Detected: {stats['targets_detected']}\n"
            stats_text += f"   Available Targets: {stats['available_targets']}\n"
            stats_text += f"   Detection Method: Computer Vision + AI\n\n"
            
            # Sticky Aim Statistics
            stats_text += "🎯 STICKY AIM STATISTICS:\n"
            stats_text += f"   Targets Locked: {stats['targets_locked']}\n"
            stats_text += f"   Current Target: {stats['current_target'] or 'None'}\n"
            stats_text += f"   Target Locked: {'YES' if stats['target_locked'] else 'NO'}\n\n"
            
            # Configuration
            stats_text += "⚙️ CURRENT CONFIGURATION:\n"
            stats_text += f"   Lock Strength: {self.lock_strength_var.get():.1f}\n"
            stats_text += f"   Smoothing Factor: {self.smoothing_var.get():.1f}\n"
            stats_text += f"   Prediction: {'Enabled' if self.prediction_var.get() else 'Disabled'}\n"
            stats_text += f"   Humanization: {'Enabled' if self.humanization_var.get() else 'Disabled'}\n\n"
            
            # System Information
            stats_text += "💻 SYSTEM INFORMATION:\n"
            stats_text += f"   Computer Vision: {'Available' if CV_AVAILABLE else 'Not Available'}\n"
            stats_text += f"   AI Detection: {'Available' if AI_AVAILABLE else 'Not Available'}\n"
            stats_text += f"   Mouse Control: {'Available' if WIN32_AVAILABLE else 'Simulated'}\n"
            stats_text += f"   System Monitoring: {'Available' if PSUTIL_AVAILABLE else 'Limited'}\n\n"
            
            # Features
            stats_text += "✨ ACTIVE FEATURES:\n"
            stats_text += "   ✅ Sticky Target Tracking\n"
            stats_text += "   ✅ Smooth Aim Interpolation\n"
            stats_text += "   ✅ Movement Prediction\n"
            stats_text += "   ✅ Humanization & Anti-Detection\n"
            stats_text += "   ✅ Real-Time Performance Monitoring\n"
            stats_text += "   ✅ Advanced Priority System\n"
            stats_text += "   ✅ Fatigue Simulation\n"
            stats_text += "   ✅ Micro-Adjustments\n\n"
            
            stats_text += "⚠️  EDUCATIONAL USE ONLY - DO NOT USE IN ONLINE GAMES ⚠️"
            
            # Update text widget
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
            
            # Auto-scroll to bottom
            self.stats_text.see(tk.END)
            
        except Exception as e:
            print(f"Stats update error: {e}")
        
        # Schedule next update
        self.root.after(1000, self.update_stats)  # Update every second
    
    def run(self):
        """Run the GUI"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            print(f"GUI error: {e}")
    
    def on_closing(self):
        """Handle window closing"""
        if self.aimbot.running:
            self.aimbot.stop()
        self.root.destroy()

def main():
    """Main entry point"""
    print(EDUCATIONAL_WARNING)
    
    # Wait for user acknowledgment
    input("\nPress Enter to continue after reading the educational notice...")
    
    try:
        # Create and run GUI
        app = StickyAimbotGUI()
        app.run()
        
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Application error: {e}")
    finally:
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
