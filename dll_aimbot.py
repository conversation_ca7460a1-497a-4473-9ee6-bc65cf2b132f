#!/usr/bin/env python3
"""
🎯 DLL-BASED AIMBOT
==================
Fast, efficient aimbot using DLL injection for mouse input
No visual overlay needed - direct game integration
"""

import ctypes
import time
import threading
import keyboard
import numpy as np
import cv2
import mss
from pathlib import Path
import torch
from ultralytics import YOLO
import win32gui
import win32con
import os

class DLLAimbot:
    def __init__(self):
        # Core settings
        self.running = False
        self.aimbot_active = False
        self.fov_size = 400
        self.sensitivity = 0.8
        self.confidence_threshold = 0.6
        self.smoothing = 0.8
        
        # DLL mouse controller
        self.mouse_dll = None
        self.dll_initialized = False
        
        # YOLO model
        self.model = None
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # Screen capture
        self.sct = mss.mss()
        self.screen_width = 1920
        self.screen_height = 1080
        
        # Performance tracking
        self.fps = 0
        self.detections = 0
        
        print("🎯 DLL Aimbot initializing...")
        self.initialize_dll()
        self.load_model()
        
    def initialize_dll(self):
        """Initialize the DDXoft DLL for mouse input"""
        try:
            dll_path = Path("lib/mouse/dd40605x64.dll").resolve()
            if not dll_path.exists():
                print(f"❌ DLL not found at {dll_path}")
                return False
                
            print(f"📁 Loading DLL from: {dll_path}")
            self.mouse_dll = ctypes.WinDLL(str(dll_path))
            time.sleep(0.1)
            
            # Set up DLL function signatures
            self.mouse_dll.DD_btn.argtypes = [ctypes.c_int]
            self.mouse_dll.DD_btn.restype = ctypes.c_int
            self.mouse_dll.DD_movR.argtypes = [ctypes.c_int, ctypes.c_int]
            self.mouse_dll.DD_movR.restype = ctypes.c_int
            
            # Test DLL initialization
            if self.mouse_dll.DD_btn(0) != 1:
                print("❌ Failed to initialize DDXoft DLL")
                return False
                
            self.dll_initialized = True
            print("✅ DDXoft DLL initialized successfully!")
            return True
            
        except Exception as e:
            print(f"❌ DLL initialization failed: {e}")
            return False
    
    def load_model(self):
        """Load YOLO model"""
        try:
            model_path = "AI-Aimbot-main/lib/best.pt"
            if not os.path.exists(model_path):
                model_path = "lib/best.pt"
            if not os.path.exists(model_path):
                print("❌ YOLO model not found!")
                return False
                
            print(f"🧠 Loading YOLO model from: {model_path}")
            self.model = YOLO(model_path)
            self.model.to(self.device)
            print(f"✅ YOLO model loaded on {self.device}")
            return True
            
        except Exception as e:
            print(f"❌ Model loading failed: {e}")
            return False
    
    def move_mouse_dll(self, dx, dy):
        """Move mouse using DLL (much faster than Win32)"""
        if not self.dll_initialized or not self.mouse_dll:
            return False
            
        try:
            # Apply smoothing
            dx = int(dx * self.smoothing)
            dy = int(dy * self.smoothing)
            
            # Move mouse using DLL
            result = self.mouse_dll.DD_movR(dx, dy)
            return result == 1
        except Exception as e:
            print(f"❌ DLL mouse move failed: {e}")
            return False
    
    def click_dll(self):
        """Click using DLL"""
        if not self.dll_initialized or not self.mouse_dll:
            return False
            
        try:
            self.mouse_dll.DD_btn(1)  # Mouse down
            time.sleep(0.001)
            self.mouse_dll.DD_btn(2)  # Mouse up
            return True
        except Exception as e:
            print(f"❌ DLL click failed: {e}")
            return False
    
    def get_screen_region(self):
        """Capture screen region around crosshair"""
        center_x = self.screen_width // 2
        center_y = self.screen_height // 2
        
        # Define FOV region
        left = center_x - self.fov_size // 2
        top = center_y - self.fov_size // 2
        width = self.fov_size
        height = self.fov_size
        
        # Capture screen region
        region = {
            'left': left,
            'top': top, 
            'width': width,
            'height': height
        }
        
        screenshot = self.sct.grab(region)
        frame = np.array(screenshot)
        frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2RGB)
        
        return frame, left, top
    
    def detect_targets(self, frame):
        """Detect targets using YOLO"""
        if not self.model:
            return []
            
        try:
            results = self.model(frame, verbose=False)
            targets = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        confidence = box.conf[0].item()
                        if confidence >= self.confidence_threshold:
                            x1, y1, x2, y2 = box.xyxy[0].tolist()
                            center_x = (x1 + x2) / 2
                            center_y = (y1 + y2) / 2
                            targets.append({
                                'x': center_x,
                                'y': center_y,
                                'confidence': confidence,
                                'bbox': [x1, y1, x2, y2]
                            })
            
            self.detections = len(targets)
            return targets
            
        except Exception as e:
            print(f"❌ Detection failed: {e}")
            return []
    
    def find_best_target(self, targets):
        """Find the best target to aim at"""
        if not targets:
            return None
            
        # Find closest target to center
        center_x = self.fov_size / 2
        center_y = self.fov_size / 2
        
        best_target = None
        min_distance = float('inf')
        
        for target in targets:
            dx = target['x'] - center_x
            dy = target['y'] - center_y
            distance = np.sqrt(dx*dx + dy*dy)
            
            if distance < min_distance:
                min_distance = distance
                best_target = target
                
        return best_target
    
    def aim_at_target(self, target, region_left, region_top):
        """Aim at the target using DLL mouse movement"""
        if not target or not self.aimbot_active:
            return
            
        # Calculate aim offset
        target_x = target['x'] + region_left
        target_y = target['y'] + region_top
        
        screen_center_x = self.screen_width / 2
        screen_center_y = self.screen_height / 2
        
        dx = target_x - screen_center_x
        dy = target_y - screen_center_y
        
        # Apply sensitivity
        dx *= self.sensitivity
        dy *= self.sensitivity
        
        # Move mouse using DLL
        if abs(dx) > 2 or abs(dy) > 2:  # Only move if significant
            self.move_mouse_dll(int(dx), int(dy))
    
    def aimbot_loop(self):
        """Main aimbot loop"""
        print("🎯 DLL Aimbot loop started")
        frame_count = 0
        start_time = time.time()
        
        while self.running:
            try:
                # Capture screen
                frame, region_left, region_top = self.get_screen_region()
                
                # Detect targets
                targets = self.detect_targets(frame)
                
                # Aim at best target
                if targets and self.aimbot_active:
                    best_target = self.find_best_target(targets)
                    if best_target:
                        self.aim_at_target(best_target, region_left, region_top)
                
                # Calculate FPS
                frame_count += 1
                if frame_count % 30 == 0:
                    elapsed = time.time() - start_time
                    self.fps = 30 / elapsed if elapsed > 0 else 0
                    start_time = time.time()
                    print(f"🎯 FPS: {self.fps:.1f} | Targets: {self.detections} | Active: {self.aimbot_active}")
                
                time.sleep(0.001)  # Small delay to prevent 100% CPU usage
                
            except Exception as e:
                print(f"❌ Aimbot loop error: {e}")
                time.sleep(0.1)
    
    def toggle_aimbot(self):
        """Toggle aimbot activation"""
        self.aimbot_active = not self.aimbot_active
        status = "🔥 ACTIVE" if self.aimbot_active else "💤 INACTIVE"
        print(f"🎯 AIMBOT {status}")
        
        if self.aimbot_active:
            print("🎮 Aimbot will now move your mouse to targets!")
        else:
            print("🖱️  You have full mouse control back!")
    
    def start(self):
        """Start the aimbot"""
        if not self.dll_initialized:
            print("❌ Cannot start - DLL not initialized")
            return
            
        if not self.model:
            print("❌ Cannot start - YOLO model not loaded")
            return
            
        print("🚀 Starting DLL Aimbot...")
        print("🎮 Controls:")
        print("   F1: Toggle aimbot ON/OFF")
        print("   F2: Quit")
        print("⚠️  AIMBOT STARTS INACTIVE - Press F1 to activate!")
        
        self.running = True
        
        # Start aimbot thread
        aimbot_thread = threading.Thread(target=self.aimbot_loop, daemon=True)
        aimbot_thread.start()
        
        # Hotkey handlers
        keyboard.add_hotkey('f1', self.toggle_aimbot)
        keyboard.add_hotkey('f2', self.stop)
        
        try:
            keyboard.wait('f2')  # Wait for F2 to quit
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """Stop the aimbot"""
        print("🛑 Stopping DLL Aimbot...")
        self.running = False
        self.aimbot_active = False

if __name__ == "__main__":
    print("🎯 DLL-BASED AIMBOT")
    print("==================")
    print("Fast, efficient aimbot using DLL injection")
    print("No visual overlay - direct mouse control")
    print()
    
    aimbot = DLLAimbot()
    aimbot.start()
