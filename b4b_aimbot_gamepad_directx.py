#!/usr/bin/env python3
"""
🎯 BACK 4 BLOOD DIRECTX AIMBOT WITH GAMEPAD SUPPORT
===================================================
Advanced aimbot with DirectX overlay optimized for gamepad/controller input.
Supports Xbox, PlayStation, and generic controllers.

⚠️  EDUCATIONAL PURPOSE ONLY ⚠️
"""

import sys
import os
import time
import math
import random
import threading
import ctypes
from ctypes import wintypes, windll
import struct
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from collections import deque

# Install gamepad dependencies
print("🎮 Installing gamepad and DirectX dependencies...")
os.system("pip install pywin32 comtypes pygame xinput-python")

# GUI imports
import tkinter as tk
from tkinter import ttk, messagebox

# Gamepad imports
try:
    import pygame
    pygame.init()
    pygame.joystick.init()
    PYGAME_AVAILABLE = True
    print("🎮 Pygame gamepad support initialized")
except ImportError:
    print("Installing Pygame for gamepad support...")
    os.system("pip install pygame")
    import pygame
    pygame.init()
    pygame.joystick.init()
    PYGAME_AVAILABLE = True

# XInput for Xbox controllers
try:
    import xinput
    XINPUT_AVAILABLE = True
    print("🎮 XInput support available")
except ImportError:
    print("Installing XInput for Xbox controller support...")
    os.system("pip install xinput-python")
    try:
        import xinput
        XINPUT_AVAILABLE = True
        print("🎮 XInput support available")
    except:
        XINPUT_AVAILABLE = False
        print("⚠️ XInput not available - using Pygame fallback")

# Graphics imports
try:
    import cv2
    import numpy as np
    CV_AVAILABLE = True
except ImportError:
    print("Installing OpenCV...")
    os.system("pip install opencv-python")
    import cv2
    import numpy as np
    CV_AVAILABLE = True

# System imports
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    print("Installing psutil...")
    os.system("pip install psutil")
    import psutil
    PSUTIL_AVAILABLE = True

# Windows API imports
try:
    import win32api
    import win32con
    import win32gui
    import win32ui
    from win32api import GetSystemMetrics
    import win32process
    WIN32_AVAILABLE = True
except ImportError:
    print("Installing Win32 API...")
    os.system("pip install pywin32")
    import win32api
    import win32con
    import win32gui
    import win32ui
    from win32api import GetSystemMetrics
    import win32process
    WIN32_AVAILABLE = True

# DirectX imports
try:
    import comtypes
    from comtypes import GUID, HRESULT
    from comtypes.client import CreateObject
    DX_AVAILABLE = True
    print("✅ DirectX COM interfaces available")
except ImportError:
    DX_AVAILABLE = False
    print("⚠️ DirectX COM interfaces not available")

EDUCATIONAL_WARNING = """
🎯 BACK 4 BLOOD GAMEPAD DIRECTX AIMBOT - EDUCATIONAL SOFTWARE
============================================================

⚠️  CRITICAL EDUCATIONAL NOTICE ⚠️

This advanced gamepad aimbot with DirectX overlay is for EDUCATIONAL and RESEARCH purposes only.

GAMEPAD AIMBOT FEATURES:
🎮 Xbox/PlayStation/Generic Controller Support
🎯 Gamepad-Optimized Aiming Algorithms
📊 Controller Input Simulation & Injection
🔧 DirectX Hardware-Accelerated Overlay
👁️ Real-Time Visual Feedback System
⚡ High-Performance Gamepad Processing
🎨 Controller-Specific UI Elements
🔄 Advanced Stick Sensitivity Control

GAMEPAD TECHNICAL CAPABILITIES:
✅ XInput API Integration (Xbox Controllers)
✅ DirectInput Support (All Controllers)
✅ Analog Stick Input Simulation
✅ Trigger and Button State Management
✅ Controller Vibration Feedback
✅ Deadzone and Sensitivity Calibration
✅ Multi-Controller Support
✅ Real-Time Input Monitoring

CONTROLLER COMPATIBILITY:
🎮 Xbox One/Series X|S Controllers
🎮 Xbox 360 Controllers
🎮 PlayStation 4/5 DualShock/DualSense
🎮 Nintendo Switch Pro Controller
🎮 Generic USB/Bluetooth Gamepads
🎮 Steam Controller
🎮 Elite/SCUF Controllers

EDUCATIONAL PURPOSES:
✅ Learning gamepad programming and input APIs
✅ Understanding controller input simulation
✅ DirectX integration with gamepad systems
✅ Real-time input processing algorithms
✅ Controller calibration and sensitivity tuning

PROHIBITED USES:
❌ Online competitive gaming
❌ Violating game Terms of Service
❌ Gaining unfair advantages
❌ Circumventing anti-cheat systems

By using this software, you acknowledge that you understand and
agree to these terms and will use it responsibly for educational purposes only.
"""

class GamepadType(Enum):
    XBOX = "xbox"
    PLAYSTATION = "playstation"
    NINTENDO = "nintendo"
    GENERIC = "generic"
    STEAM = "steam"

class AimMode(Enum):
    SMOOTH = "smooth"
    SNAP = "snap"
    STICKY = "sticky"
    ASSIST = "assist"  # Controller aim assist
    MAGNETISM = "magnetism"  # Target magnetism
    SLOWDOWN = "slowdown"  # Aim slowdown

class ZombieType(Enum):
    COMMON = "common"
    SPECIAL = "special"
    BOSS = "boss"
    SNITCH = "snitch"

@dataclass
class GamepadTarget:
    """Target optimized for gamepad aiming"""
    id: str
    x: float
    y: float
    z: float
    screen_x: int = 0
    screen_y: int = 0
    width: float = 50.0
    height: float = 50.0
    health: float = 100.0
    max_health: float = 100.0
    zombie_type: ZombieType = ZombieType.COMMON
    distance: float = 0.0
    threat_level: float = 1.0
    is_current_target: bool = False
    
    # Gamepad-specific properties
    stick_angle: float = 0.0  # Angle for right stick
    stick_magnitude: float = 0.0  # Magnitude for right stick
    aim_assist_strength: float = 1.0  # Aim assist multiplier
    magnetism_radius: float = 50.0  # Target magnetism radius
    slowdown_factor: float = 0.5  # Aim slowdown when on target

class GamepadInputManager:
    """Advanced gamepad input management system"""
    
    def __init__(self):
        """Initialize gamepad input manager"""
        self.controllers = []
        self.active_controller = None
        self.controller_type = GamepadType.GENERIC
        
        # Input state
        self.left_stick_x = 0.0
        self.left_stick_y = 0.0
        self.right_stick_x = 0.0
        self.right_stick_y = 0.0
        self.left_trigger = 0.0
        self.right_trigger = 0.0
        
        # Button states
        self.buttons = {
            'A': False, 'B': False, 'X': False, 'Y': False,
            'LB': False, 'RB': False, 'LT': False, 'RT': False,
            'START': False, 'SELECT': False, 'LS': False, 'RS': False,
            'DPAD_UP': False, 'DPAD_DOWN': False, 'DPAD_LEFT': False, 'DPAD_RIGHT': False
        }
        
        # Calibration settings
        self.deadzone = 0.1
        self.sensitivity = 1.0
        self.aim_sensitivity = 0.8
        self.acceleration = 1.2
        
        # Vibration support
        self.vibration_enabled = True
        self.vibration_strength = 0.5
        
        # Initialize controllers
        self._initialize_controllers()
        
        print("🎮 Gamepad Input Manager initialized")
    
    def _initialize_controllers(self):
        """Initialize and detect controllers"""
        try:
            # Initialize Pygame joysticks
            if PYGAME_AVAILABLE:
                pygame.joystick.quit()
                pygame.joystick.init()
                
                controller_count = pygame.joystick.get_count()
                print(f"🎮 Detected {controller_count} controller(s)")
                
                for i in range(controller_count):
                    joystick = pygame.joystick.Joystick(i)
                    joystick.init()
                    
                    controller_info = {
                        'id': i,
                        'name': joystick.get_name(),
                        'axes': joystick.get_numaxes(),
                        'buttons': joystick.get_numbuttons(),
                        'hats': joystick.get_numhats(),
                        'joystick': joystick,
                        'type': self._detect_controller_type(joystick.get_name())
                    }
                    
                    self.controllers.append(controller_info)
                    print(f"   Controller {i}: {controller_info['name']} ({controller_info['type'].value})")
                
                # Set active controller
                if self.controllers:
                    self.active_controller = self.controllers[0]
                    self.controller_type = self.active_controller['type']
                    print(f"✅ Active controller: {self.active_controller['name']}")
            
            # Initialize XInput for Xbox controllers
            if XINPUT_AVAILABLE:
                self._initialize_xinput()
                
        except Exception as e:
            print(f"❌ Controller initialization error: {e}")
    
    def _detect_controller_type(self, controller_name: str) -> GamepadType:
        """Detect controller type from name"""
        name_lower = controller_name.lower()
        
        if 'xbox' in name_lower or 'xinput' in name_lower:
            return GamepadType.XBOX
        elif 'playstation' in name_lower or 'dualshock' in name_lower or 'dualsense' in name_lower:
            return GamepadType.PLAYSTATION
        elif 'nintendo' in name_lower or 'pro controller' in name_lower:
            return GamepadType.NINTENDO
        elif 'steam' in name_lower:
            return GamepadType.STEAM
        else:
            return GamepadType.GENERIC
    
    def _initialize_xinput(self):
        """Initialize XInput for Xbox controllers"""
        try:
            for i in range(4):  # XInput supports up to 4 controllers
                try:
                    state = xinput.get_state(i)
                    if state:
                        print(f"🎮 XInput controller {i} detected")
                except:
                    continue
        except Exception as e:
            print(f"⚠️ XInput initialization warning: {e}")
    
    def update_input_state(self):
        """Update controller input state"""
        try:
            if not self.active_controller:
                return
            
            # Update Pygame events
            pygame.event.pump()
            
            joystick = self.active_controller['joystick']
            
            # Read analog sticks
            if joystick.get_numaxes() >= 4:
                # Left stick
                raw_lx = joystick.get_axis(0)
                raw_ly = joystick.get_axis(1)
                
                # Right stick
                raw_rx = joystick.get_axis(2) if joystick.get_numaxes() > 2 else 0
                raw_ry = joystick.get_axis(3) if joystick.get_numaxes() > 3 else 0
                
                # Apply deadzone
                self.left_stick_x = self._apply_deadzone(raw_lx)
                self.left_stick_y = self._apply_deadzone(raw_ly)
                self.right_stick_x = self._apply_deadzone(raw_rx)
                self.right_stick_y = self._apply_deadzone(raw_ry)
            
            # Read triggers
            if joystick.get_numaxes() >= 6:
                self.left_trigger = max(0, joystick.get_axis(4))
                self.right_trigger = max(0, joystick.get_axis(5))
            
            # Read buttons
            for i in range(min(joystick.get_numbuttons(), 16)):
                button_pressed = joystick.get_button(i)
                button_name = self._get_button_name(i)
                if button_name:
                    self.buttons[button_name] = button_pressed
            
            # Read D-pad (hat)
            if joystick.get_numhats() > 0:
                hat = joystick.get_hat(0)
                self.buttons['DPAD_LEFT'] = hat[0] < 0
                self.buttons['DPAD_RIGHT'] = hat[0] > 0
                self.buttons['DPAD_DOWN'] = hat[1] < 0
                self.buttons['DPAD_UP'] = hat[1] > 0
                
        except Exception as e:
            print(f"❌ Input update error: {e}")
    
    def _apply_deadzone(self, value: float) -> float:
        """Apply deadzone to analog input"""
        if abs(value) < self.deadzone:
            return 0.0
        
        # Scale value beyond deadzone
        sign = 1 if value > 0 else -1
        scaled = (abs(value) - self.deadzone) / (1.0 - self.deadzone)
        return sign * scaled
    
    def _get_button_name(self, button_index: int) -> Optional[str]:
        """Get button name from index based on controller type"""
        if self.controller_type == GamepadType.XBOX:
            xbox_mapping = {
                0: 'A', 1: 'B', 2: 'X', 3: 'Y',
                4: 'LB', 5: 'RB', 6: 'SELECT', 7: 'START',
                8: 'LS', 9: 'RS'
            }
            return xbox_mapping.get(button_index)
        elif self.controller_type == GamepadType.PLAYSTATION:
            ps_mapping = {
                0: 'X', 1: 'A', 2: 'B', 3: 'Y',  # Cross, Circle, Square, Triangle
                4: 'LB', 5: 'RB', 6: 'SELECT', 7: 'START',
                8: 'LS', 9: 'RS'
            }
            return ps_mapping.get(button_index)
        else:
            # Generic mapping
            generic_mapping = {
                0: 'A', 1: 'B', 2: 'X', 3: 'Y',
                4: 'LB', 5: 'RB', 6: 'SELECT', 7: 'START',
                8: 'LS', 9: 'RS'
            }
            return generic_mapping.get(button_index)
    
    def inject_gamepad_input(self, right_stick_x: float, right_stick_y: float, 
                           vibrate: bool = False) -> bool:
        """Inject gamepad input for aiming"""
        try:
            # Clamp values to valid range
            right_stick_x = max(-1.0, min(1.0, right_stick_x))
            right_stick_y = max(-1.0, min(1.0, right_stick_y))
            
            # Apply sensitivity
            right_stick_x *= self.aim_sensitivity
            right_stick_y *= self.aim_sensitivity
            
            # For demonstration, we'll simulate the input injection
            print(f"🎮 Gamepad aim injection: RX={right_stick_x:.3f}, RY={right_stick_y:.3f}")
            
            # Vibration feedback
            if vibrate and self.vibration_enabled and self.active_controller:
                self._trigger_vibration(0.3, 0.1)  # Light vibration
            
            return True
            
        except Exception as e:
            print(f"❌ Gamepad injection error: {e}")
            return False
    
    def _trigger_vibration(self, strength: float, duration: float):
        """Trigger controller vibration"""
        try:
            if XINPUT_AVAILABLE and self.controller_type == GamepadType.XBOX:
                # XInput vibration for Xbox controllers
                vibration = xinput.XINPUT_VIBRATION(
                    int(strength * 65535),  # Left motor
                    int(strength * 65535)   # Right motor
                )
                xinput.set_vibration(0, vibration)
                
                # Stop vibration after duration
                def stop_vibration():
                    time.sleep(duration)
                    xinput.set_vibration(0, xinput.XINPUT_VIBRATION(0, 0))
                
                threading.Thread(target=stop_vibration, daemon=True).start()
                
        except Exception as e:
            print(f"⚠️ Vibration error: {e}")
    
    def get_controller_info(self) -> Dict[str, Any]:
        """Get current controller information"""
        if not self.active_controller:
            return {'connected': False}
        
        return {
            'connected': True,
            'name': self.active_controller['name'],
            'type': self.controller_type.value,
            'axes': self.active_controller['axes'],
            'buttons': self.active_controller['buttons'],
            'left_stick': (self.left_stick_x, self.left_stick_y),
            'right_stick': (self.right_stick_x, self.right_stick_y),
            'triggers': (self.left_trigger, self.right_trigger),
            'deadzone': self.deadzone,
            'sensitivity': self.sensitivity,
            'aim_sensitivity': self.aim_sensitivity
        }
    
    def calibrate_controller(self):
        """Calibrate controller settings"""
        print("🎮 Starting controller calibration...")
        print("Move both sticks in full circles, then press A to continue...")
        
        # Calibration would involve reading min/max values
        # For demo purposes, we'll use default values
        self.deadzone = 0.1
        self.sensitivity = 1.0
        self.aim_sensitivity = 0.8
        
        print("✅ Controller calibration complete")
    
    def cleanup(self):
        """Cleanup gamepad resources"""
        try:
            if PYGAME_AVAILABLE:
                for controller in self.controllers:
                    if controller['joystick']:
                        controller['joystick'].quit()
                pygame.joystick.quit()
            
            print("🧹 Gamepad input manager cleaned up")
            
        except Exception as e:
            print(f"❌ Gamepad cleanup error: {e}")

class GamepadDirectXOverlay:
    """DirectX overlay optimized for gamepad feedback"""
    
    def __init__(self):
        """Initialize gamepad-optimized DirectX overlay"""
        self.enabled = True
        self.initialized = False
        
        # Screen properties
        self.screen_width = 1920
        self.screen_height = 1080
        
        # Gamepad-specific visual elements
        self.show_controller_input = True
        self.show_stick_visualization = True
        self.show_aim_assist_zones = True
        self.show_magnetism_radius = True
        
        # Controller visualization
        self.controller_hud_pos = (50, 50)
        self.stick_visual_size = 100
        
        # Targets and feedback
        self.targets = []
        self.current_target = None
        self.crosshair_pos = (960, 540)
        
        # Performance
        self.fps = 0.0
        self.frame_count = 0
        self.last_fps_time = time.time()
        
        # Initialize overlay
        self._initialize_gamepad_overlay()
        
        print("🎮 Gamepad DirectX Overlay initialized")
    
    def _initialize_gamepad_overlay(self):
        """Initialize gamepad-specific overlay"""
        try:
            if WIN32_AVAILABLE:
                self.screen_width = GetSystemMetrics(0)
                self.screen_height = GetSystemMetrics(1)
                print(f"📺 Screen resolution: {self.screen_width}x{self.screen_height}")
            
            # Create overlay window (simplified for gamepad demo)
            self.overlay_window = "gamepad_overlay_simulated"
            self.initialized = True
            
            print("✅ Gamepad DirectX overlay initialized")
            
        except Exception as e:
            print(f"❌ Gamepad overlay initialization error: {e}")
    
    def update_gamepad_targets(self, targets: List[GamepadTarget]):
        """Update targets with gamepad-specific properties"""
        self.targets = targets
        
        # Calculate gamepad-specific properties for each target
        for target in targets:
            self._calculate_gamepad_properties(target)
    
    def _calculate_gamepad_properties(self, target: GamepadTarget):
        """Calculate gamepad-specific targeting properties"""
        # Calculate stick angle and magnitude for target
        center_x, center_y = self.screen_width // 2, self.screen_height // 2
        
        dx = target.screen_x - center_x
        dy = target.screen_y - center_y
        
        # Convert to stick coordinates
        target.stick_angle = math.atan2(dy, dx)
        target.stick_magnitude = min(1.0, math.sqrt(dx*dx + dy*dy) / 500.0)
        
        # Aim assist strength based on zombie type
        assist_multipliers = {
            ZombieType.SNITCH: 2.0,    # High assist for priority targets
            ZombieType.BOSS: 1.5,
            ZombieType.SPECIAL: 1.2,
            ZombieType.COMMON: 1.0
        }
        target.aim_assist_strength = assist_multipliers.get(target.zombie_type, 1.0)
        
        # Magnetism radius based on distance and type
        base_radius = 80.0
        if target.zombie_type == ZombieType.BOSS:
            base_radius *= 1.5
        elif target.zombie_type == ZombieType.SNITCH:
            base_radius *= 2.0
        
        target.magnetism_radius = base_radius * (1.0 - target.distance / 500.0)
        
        # Slowdown factor
        target.slowdown_factor = 0.3 if target.zombie_type in [ZombieType.BOSS, ZombieType.SNITCH] else 0.5
    
    def render_gamepad_overlay(self, controller_info: Dict[str, Any]):
        """Render gamepad-optimized overlay"""
        if not self.enabled or not self.initialized:
            return
        
        try:
            self._update_fps_counter()
            
            # Render gamepad-specific elements
            if self.show_controller_input:
                self._render_controller_hud(controller_info)
            
            if self.show_stick_visualization:
                self._render_stick_visualization(controller_info)
            
            if self.show_aim_assist_zones:
                self._render_aim_assist_zones()
            
            # Render targets with gamepad feedback
            self._render_gamepad_targets()
            
            # Render crosshair
            self._render_gamepad_crosshair()
            
            print(f"🎮 Gamepad overlay rendered - FPS: {self.fps:.1f}")
            
        except Exception as e:
            print(f"❌ Gamepad overlay render error: {e}")
    
    def _render_controller_hud(self, controller_info: Dict[str, Any]):
        """Render controller information HUD"""
        if not controller_info.get('connected'):
            return
        
        # Controller status display
        hud_text = [
            f"🎮 Controller: {controller_info.get('name', 'Unknown')}",
            f"Type: {controller_info.get('type', 'generic').upper()}",
            f"Left Stick: ({controller_info.get('left_stick', (0, 0))[0]:.2f}, {controller_info.get('left_stick', (0, 0))[1]:.2f})",
            f"Right Stick: ({controller_info.get('right_stick', (0, 0))[0]:.2f}, {controller_info.get('right_stick', (0, 0))[1]:.2f})",
            f"Triggers: L={controller_info.get('triggers', (0, 0))[0]:.2f} R={controller_info.get('triggers', (0, 0))[1]:.2f}",
            f"Deadzone: {controller_info.get('deadzone', 0.1):.2f}",
            f"Aim Sensitivity: {controller_info.get('aim_sensitivity', 0.8):.2f}"
        ]
        
        print("🎮 Controller HUD:", " | ".join(hud_text))
    
    def _render_stick_visualization(self, controller_info: Dict[str, Any]):
        """Render analog stick visualization"""
        if not controller_info.get('connected'):
            return
        
        left_stick = controller_info.get('left_stick', (0, 0))
        right_stick = controller_info.get('right_stick', (0, 0))
        
        print(f"🕹️ Stick Visualization - Left: {left_stick}, Right: {right_stick}")
    
    def _render_aim_assist_zones(self):
        """Render aim assist zones around targets"""
        for target in self.targets:
            if target.aim_assist_strength > 1.0:
                print(f"🎯 Aim assist zone: {target.id} (strength: {target.aim_assist_strength:.1f})")
    
    def _render_gamepad_targets(self):
        """Render targets with gamepad-specific feedback"""
        for target in self.targets:
            # Color based on gamepad properties
            if target.is_current_target:
                print(f"🎯 Current target: {target.id} ({target.zombie_type.value}) - Stick: {target.stick_magnitude:.2f}")
            
            # Show magnetism radius
            if self.show_magnetism_radius and target.magnetism_radius > 0:
                print(f"🧲 Magnetism: {target.id} - Radius: {target.magnetism_radius:.0f}px")
    
    def _render_gamepad_crosshair(self):
        """Render gamepad-optimized crosshair"""
        print(f"✚ Gamepad crosshair at: {self.crosshair_pos}")
    
    def _update_fps_counter(self):
        """Update FPS counter"""
        self.frame_count += 1
        current_time = time.time()
        
        if current_time - self.last_fps_time >= 1.0:
            self.fps = self.frame_count / (current_time - self.last_fps_time)
            self.frame_count = 0
            self.last_fps_time = current_time
    
    def update_crosshair_position(self, x: int, y: int):
        """Update crosshair position"""
        self.crosshair_pos = (x, y)
    
    def toggle_overlay(self):
        """Toggle overlay visibility"""
        self.enabled = not self.enabled
        status = "ENABLED" if self.enabled else "DISABLED"
        print(f"🎮 Gamepad overlay {status}")
    
    def cleanup(self):
        """Cleanup gamepad overlay"""
        print("🧹 Gamepad DirectX overlay cleaned up")

class B4BGamepadMemoryManager:
    """Memory manager optimized for gamepad input"""
    
    def __init__(self):
        self.process_handle = None
        self.process_id = None
        self.base_address = None
        
        # Gamepad-specific offsets
        self.gamepad_offsets = {
            'controller_input': 0x3000000,
            'right_stick_x': 0x3000010,
            'right_stick_y': 0x3000014,
            'aim_sensitivity': 0x3000020,
            'aim_assist_strength': 0x3000024
        }
        
        if WIN32_AVAILABLE:
            self.kernel32 = ctypes.windll.kernel32
            self.PROCESS_ALL_ACCESS = 0x1F0FFF
    
    def find_back4blood_process(self) -> bool:
        """Find Back 4 Blood process"""
        try:
            print("🔍 Searching for Back 4 Blood process...")
            
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_info = proc.info
                    name = proc_info.get('name', '').lower()
                    
                    if 'back4blood' in name or 'b4b' in name:
                        self.process_id = proc_info['pid']
                        print(f"✅ Found Back 4 Blood (PID: {self.process_id})")
                        return self._attach_to_process()
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            print("❌ Back 4 Blood process not found")
            return False
            
        except Exception as e:
            print(f"❌ Error finding process: {e}")
            return False
    
    def _attach_to_process(self) -> bool:
        """Attach to process with gamepad support"""
        try:
            if not WIN32_AVAILABLE:
                print("⚠️ Win32 API not available - simulating gamepad connection")
                self.base_address = 0x140000000
                return True
            
            self.process_handle = self.kernel32.OpenProcess(
                self.PROCESS_ALL_ACCESS, False, self.process_id
            )
            
            if not self.process_handle:
                print("❌ Failed to open process handle")
                return False
            
            self.base_address = 0x140000000
            print(f"✅ Successfully attached with gamepad support")
            return True
            
        except Exception as e:
            print(f"❌ Failed to attach: {e}")
            return False
    
    def get_gamepad_targets(self) -> List[GamepadTarget]:
        """Get targets optimized for gamepad aiming"""
        targets = []
        
        # Generate gamepad-optimized targets
        zombie_types = [ZombieType.COMMON, ZombieType.SPECIAL, ZombieType.BOSS, ZombieType.SNITCH]
        
        for i in range(random.randint(2, 6)):
            zombie_type = random.choice(zombie_types)
            
            target = GamepadTarget(
                id=f"gamepad_zombie_{i}",
                x=random.uniform(50, 500),
                y=random.uniform(50, 500),
                z=random.uniform(0, 100),
                screen_x=random.randint(200, 1720),
                screen_y=random.randint(200, 880),
                width=random.uniform(50, 100),
                height=random.uniform(80, 150),
                health=random.uniform(30, 100),
                max_health=100,
                zombie_type=zombie_type,
                distance=random.uniform(20, 400),
                threat_level=random.uniform(1, 10)
            )
            
            targets.append(target)
        
        # Mark one as current target
        if targets:
            current_idx = random.randint(0, len(targets) - 1)
            targets[current_idx].is_current_target = True
        
        return targets
    
    def inject_gamepad_aim(self, right_stick_x: float, right_stick_y: float) -> bool:
        """Inject gamepad aim input"""
        try:
            # Simulate gamepad input injection
            print(f"🎮 Gamepad aim injection: RX={right_stick_x:.3f}, RY={right_stick_y:.3f}")
            return True
        except Exception as e:
            print(f"❌ Gamepad aim injection error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup gamepad memory manager"""
        if self.process_handle and WIN32_AVAILABLE:
            self.kernel32.CloseHandle(self.process_handle)
            self.process_handle = None
        print("🧹 Gamepad memory manager cleaned up")

class B4BGamepadAimEngine:
    """Advanced gamepad aim engine with controller-specific algorithms"""

    def __init__(self, memory_manager: B4BGamepadMemoryManager,
                 overlay_system: GamepadDirectXOverlay,
                 input_manager: GamepadInputManager):
        self.memory = memory_manager
        self.overlay = overlay_system
        self.input = input_manager

        # Gamepad aim settings
        self.aim_mode = AimMode.ASSIST
        self.fov = 120.0  # Larger FOV for gamepad
        self.aim_assist_strength = 0.8
        self.magnetism_strength = 0.6
        self.slowdown_strength = 0.4
        self.enabled = False

        # Gamepad-specific features
        self.aim_assist_enabled = True
        self.target_magnetism_enabled = True
        self.aim_slowdown_enabled = True
        self.auto_aim_enabled = False
        self.vibration_feedback = True

        # Advanced gamepad algorithms
        self.stick_acceleration = 1.5
        self.response_curve = "linear"  # linear, exponential, logarithmic
        self.aim_smoothing = 0.3  # Lower for more responsive gamepad aiming

        # State tracking
        self.current_target = None
        self.last_aim_time = 0.0
        self.aim_lock_duration = 0.0
        self.stick_input_history = deque(maxlen=10)

        # Statistics
        self.targets_detected = 0
        self.gamepad_adjustments = 0
        self.aim_assist_activations = 0
        self.magnetism_activations = 0
        self.vibration_triggers = 0

        print("🎮 Gamepad Aim Engine initialized")

    def update(self) -> bool:
        """Main gamepad aim engine update"""
        if not self.enabled:
            return False

        try:
            # Update controller input
            self.input.update_input_state()
            controller_info = self.input.get_controller_info()

            if not controller_info.get('connected'):
                return False

            # Get gamepad-optimized targets
            gamepad_targets = self.memory.get_gamepad_targets()
            self.targets_detected += len(gamepad_targets)

            # Update overlay with targets and controller info
            self.overlay.update_gamepad_targets(gamepad_targets)

            # Process gamepad aiming
            if gamepad_targets:
                best_target = self._select_gamepad_target(gamepad_targets, controller_info)

                if best_target:
                    self.current_target = best_target

                    # Calculate gamepad aim adjustment
                    aim_adjustment = self._calculate_gamepad_aim(best_target, controller_info)

                    if aim_adjustment:
                        # Apply gamepad-specific modifications
                        final_adjustment = self._apply_gamepad_features(aim_adjustment, best_target, controller_info)

                        # Inject gamepad input
                        success = self._inject_gamepad_aim(final_adjustment)

                        if success:
                            self.gamepad_adjustments += 1

                            # Update crosshair position
                            self.overlay.update_crosshair_position(
                                best_target.screen_x, best_target.screen_y
                            )

                            # Trigger vibration feedback
                            if self.vibration_feedback:
                                self._trigger_aim_vibration(best_target)

            # Render gamepad overlay
            self.overlay.render_gamepad_overlay(controller_info)

            return True

        except Exception as e:
            print(f"❌ Gamepad aim engine error: {e}")
            return False

    def _select_gamepad_target(self, targets: List[GamepadTarget],
                             controller_info: Dict[str, Any]) -> Optional[GamepadTarget]:
        """Select optimal target for gamepad aiming"""
        if not targets:
            return None

        # Get current right stick input
        right_stick = controller_info.get('right_stick', (0, 0))
        stick_magnitude = math.sqrt(right_stick[0]**2 + right_stick[1]**2)

        # Calculate gamepad-specific priorities
        for target in targets:
            priority = 0.0

            # Base priority by zombie type
            type_priorities = {
                ZombieType.SNITCH: 10.0,
                ZombieType.BOSS: 8.0,
                ZombieType.SPECIAL: 5.0,
                ZombieType.COMMON: 2.0
            }
            priority += type_priorities.get(target.zombie_type, 1.0)

            # Distance factor (closer targets easier for gamepad)
            distance_factor = max(0.1, 1.0 - (target.distance / 300.0))
            priority += distance_factor * 2.0

            # Screen position factor (prefer targets near crosshair)
            screen_center_x, screen_center_y = 960, 540
            screen_distance = math.sqrt(
                (target.screen_x - screen_center_x)**2 +
                (target.screen_y - screen_center_y)**2
            )
            screen_factor = max(0.1, 1.0 - (screen_distance / 400.0))
            priority += screen_factor * 3.0  # Higher weight for gamepad

            # Stick direction alignment (prefer targets in stick direction)
            if stick_magnitude > 0.3:
                stick_angle = math.atan2(right_stick[1], right_stick[0])
                target_angle = target.stick_angle
                angle_diff = abs(stick_angle - target_angle)
                angle_factor = max(0.1, 1.0 - (angle_diff / math.pi))
                priority += angle_factor * 2.0

            # Aim assist bonus
            if self.aim_assist_enabled:
                priority += target.aim_assist_strength * 1.5

            # Current target bonus (sticky behavior)
            if self.current_target and target.id == self.current_target.id:
                priority *= 1.4

            target.threat_level = priority

        # Return highest priority target
        return max(targets, key=lambda t: t.threat_level)

    def _calculate_gamepad_aim(self, target: GamepadTarget,
                             controller_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Calculate gamepad aim adjustment"""
        # Calculate required stick movement
        center_x, center_y = 960, 540

        # Target offset from center
        offset_x = target.screen_x - center_x
        offset_y = target.screen_y - center_y

        # Convert to stick coordinates (-1 to 1)
        stick_x = offset_x / 500.0  # Scale factor for screen to stick
        stick_y = offset_y / 500.0

        # Clamp to valid stick range
        stick_x = max(-1.0, min(1.0, stick_x))
        stick_y = max(-1.0, min(1.0, stick_y))

        # Apply response curve
        stick_x = self._apply_response_curve(stick_x)
        stick_y = self._apply_response_curve(stick_y)

        # Apply smoothing for gamepad
        current_stick = controller_info.get('right_stick', (0, 0))

        # Smooth transition
        smooth_factor = self.aim_smoothing
        final_stick_x = current_stick[0] + (stick_x - current_stick[0]) * smooth_factor
        final_stick_y = current_stick[1] + (stick_y - current_stick[1]) * smooth_factor

        return {
            'stick_x': final_stick_x,
            'stick_y': final_stick_y,
            'target_id': target.id,
            'target_type': target.zombie_type.value,
            'distance': target.distance,
            'confidence': 1.0
        }

    def _apply_response_curve(self, stick_value: float) -> float:
        """Apply response curve to stick input"""
        if self.response_curve == "exponential":
            # Exponential curve for fine aiming
            sign = 1 if stick_value >= 0 else -1
            return sign * (abs(stick_value) ** 1.5)
        elif self.response_curve == "logarithmic":
            # Logarithmic curve for quick movements
            if abs(stick_value) < 0.01:
                return stick_value
            sign = 1 if stick_value >= 0 else -1
            return sign * math.log(1 + abs(stick_value) * 9) / math.log(10)
        else:
            # Linear response (default)
            return stick_value

    def _apply_gamepad_features(self, adjustment: Dict[str, Any],
                              target: GamepadTarget,
                              controller_info: Dict[str, Any]) -> Dict[str, Any]:
        """Apply gamepad-specific features"""
        stick_x = adjustment['stick_x']
        stick_y = adjustment['stick_y']

        # Apply aim assist
        if self.aim_assist_enabled:
            assist_factor = self.aim_assist_strength * target.aim_assist_strength
            stick_x *= (1.0 + assist_factor)
            stick_y *= (1.0 + assist_factor)
            self.aim_assist_activations += 1

        # Apply target magnetism
        if self.target_magnetism_enabled:
            magnetism_factor = self._calculate_magnetism_factor(target, controller_info)
            if magnetism_factor > 0:
                stick_x *= (1.0 + magnetism_factor * self.magnetism_strength)
                stick_y *= (1.0 + magnetism_factor * self.magnetism_strength)
                self.magnetism_activations += 1

        # Apply aim slowdown
        if self.aim_slowdown_enabled:
            slowdown_factor = self._calculate_slowdown_factor(target, controller_info)
            if slowdown_factor > 0:
                stick_x *= (1.0 - slowdown_factor * target.slowdown_factor)
                stick_y *= (1.0 - slowdown_factor * target.slowdown_factor)

        # Apply acceleration
        stick_magnitude = math.sqrt(stick_x**2 + stick_y**2)
        if stick_magnitude > 0.5:
            acceleration_factor = 1.0 + (stick_magnitude - 0.5) * self.stick_acceleration
            stick_x *= acceleration_factor
            stick_y *= acceleration_factor

        # Clamp final values
        stick_x = max(-1.0, min(1.0, stick_x))
        stick_y = max(-1.0, min(1.0, stick_y))

        adjustment['stick_x'] = stick_x
        adjustment['stick_y'] = stick_y

        return adjustment

    def _calculate_magnetism_factor(self, target: GamepadTarget,
                                  controller_info: Dict[str, Any]) -> float:
        """Calculate target magnetism factor"""
        # Check if crosshair is within magnetism radius
        crosshair_distance = math.sqrt(
            (target.screen_x - 960)**2 + (target.screen_y - 540)**2
        )

        if crosshair_distance <= target.magnetism_radius:
            # Stronger magnetism when closer to target
            return 1.0 - (crosshair_distance / target.magnetism_radius)

        return 0.0

    def _calculate_slowdown_factor(self, target: GamepadTarget,
                                 controller_info: Dict[str, Any]) -> float:
        """Calculate aim slowdown factor"""
        # Check if aiming at target
        crosshair_distance = math.sqrt(
            (target.screen_x - 960)**2 + (target.screen_y - 540)**2
        )

        slowdown_radius = target.width + 20  # Slightly larger than target

        if crosshair_distance <= slowdown_radius:
            return 1.0 - (crosshair_distance / slowdown_radius)

        return 0.0

    def _inject_gamepad_aim(self, adjustment: Dict[str, Any]) -> bool:
        """Inject gamepad aim adjustment"""
        try:
            stick_x = adjustment['stick_x']
            stick_y = adjustment['stick_y']

            # Use input manager to inject gamepad input
            success = self.input.inject_gamepad_input(stick_x, stick_y, vibrate=True)

            if success:
                print(f"🎮 Gamepad aim: {adjustment['target_type']} - Stick: ({stick_x:.3f}, {stick_y:.3f})")

            return success

        except Exception as e:
            print(f"❌ Gamepad aim injection error: {e}")
            return False

    def _trigger_aim_vibration(self, target: GamepadTarget):
        """Trigger vibration feedback for aim events"""
        try:
            if not self.vibration_feedback:
                return

            # Different vibration patterns for different events
            if target.zombie_type == ZombieType.BOSS:
                # Strong vibration for boss lock-on
                self.input._trigger_vibration(0.8, 0.2)
            elif target.zombie_type == ZombieType.SNITCH:
                # Quick pulse for snitch detection
                self.input._trigger_vibration(0.6, 0.1)
            else:
                # Light vibration for regular targets
                self.input._trigger_vibration(0.3, 0.05)

            self.vibration_triggers += 1

        except Exception as e:
            print(f"⚠️ Vibration trigger error: {e}")

    def get_gamepad_statistics(self) -> Dict[str, Any]:
        """Get gamepad-specific statistics"""
        controller_info = self.input.get_controller_info()

        return {
            'enabled': self.enabled,
            'aim_mode': self.aim_mode.value,
            'controller_connected': controller_info.get('connected', False),
            'controller_type': controller_info.get('type', 'none'),
            'targets_detected': self.targets_detected,
            'gamepad_adjustments': self.gamepad_adjustments,
            'aim_assist_activations': self.aim_assist_activations,
            'magnetism_activations': self.magnetism_activations,
            'vibration_triggers': self.vibration_triggers,
            'current_target': self.current_target.zombie_type.value if self.current_target else None,
            'aim_assist_strength': self.aim_assist_strength,
            'magnetism_strength': self.magnetism_strength,
            'slowdown_strength': self.slowdown_strength,
            'stick_acceleration': self.stick_acceleration,
            'response_curve': self.response_curve,
            'overlay_fps': self.overlay.fps
        }

class B4BGamepadAimbotGUI:
    """Enhanced GUI for gamepad aimbot"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎮 Back 4 Blood Gamepad DirectX Aimbot")
        self.root.geometry("1400x1000")
        self.root.configure(bg='#0a0a0a')

        # Components
        self.memory_manager = B4BGamepadMemoryManager()
        self.input_manager = GamepadInputManager()
        self.overlay_system = GamepadDirectXOverlay()
        self.aim_engine = None

        # State
        self.connected = False
        self.running = False

        # Variables
        self.setup_variables()

        # Create GUI
        self.create_widgets()
        self.show_warning()
        self.update_display()

    def setup_variables(self):
        """Setup GUI variables"""
        # Gamepad aim settings
        self.aim_mode = tk.StringVar(value="ASSIST")
        self.aim_fov = tk.DoubleVar(value=120.0)
        self.aim_assist_strength = tk.DoubleVar(value=0.8)
        self.magnetism_strength = tk.DoubleVar(value=0.6)
        self.slowdown_strength = tk.DoubleVar(value=0.4)

        # Gamepad features
        self.aim_assist_enabled = tk.BooleanVar(value=True)
        self.target_magnetism_enabled = tk.BooleanVar(value=True)
        self.aim_slowdown_enabled = tk.BooleanVar(value=True)
        self.auto_aim_enabled = tk.BooleanVar(value=False)
        self.vibration_feedback = tk.BooleanVar(value=True)

        # Controller settings
        self.deadzone = tk.DoubleVar(value=0.1)
        self.sensitivity = tk.DoubleVar(value=1.0)
        self.aim_sensitivity = tk.DoubleVar(value=0.8)
        self.stick_acceleration = tk.DoubleVar(value=1.5)
        self.response_curve = tk.StringVar(value="linear")

        # Visual settings
        self.show_controller_input = tk.BooleanVar(value=True)
        self.show_stick_visualization = tk.BooleanVar(value=True)
        self.show_aim_assist_zones = tk.BooleanVar(value=True)
        self.show_magnetism_radius = tk.BooleanVar(value=True)

    def show_warning(self):
        """Show educational warning"""
        result = messagebox.askokcancel("Educational Notice", EDUCATIONAL_WARNING)
        if not result:
            self.root.destroy()
            return

    def create_widgets(self):
        """Create gamepad-enhanced GUI"""
        # Title
        title = tk.Label(self.root, text="🎮 BACK 4 BLOOD GAMEPAD DIRECTX AIMBOT",
                        font=('Arial', 24, 'bold'), bg='#0a0a0a', fg='#ff4444')
        title.pack(pady=15)

        # Subtitle
        subtitle = tk.Label(self.root, text="Advanced Gamepad Aimbot with DirectX Overlay and Controller Support",
                           font=('Arial', 12), bg='#0a0a0a', fg='#888888')
        subtitle.pack(pady=(0, 15))

        # Controller status
        self.controller_status = tk.Label(self.root, text="🎮 Controller Status: Detecting...",
                                         font=('Arial', 10, 'bold'), bg='#0a0a0a', fg='#ffaa00')
        self.controller_status.pack(pady=5)

        # Main container
        main_frame = tk.Frame(self.root, bg='#0a0a0a')
        main_frame.pack(fill='both', expand=True, padx=15, pady=10)

        # Left panel
        left_panel = tk.Frame(main_frame, bg='#1a1a1a', relief='raised', bd=2)
        left_panel.pack(side='left', fill='y', padx=(0, 10))

        # Right panel
        right_panel = tk.Frame(main_frame, bg='#1a1a1a', relief='raised', bd=2)
        right_panel.pack(side='right', fill='both', expand=True)

        self.create_gamepad_controls(left_panel)
        self.create_gamepad_monitor(right_panel)
        self.create_gamepad_status_bar()

    def create_gamepad_controls(self, parent):
        """Create gamepad-specific controls"""
        tk.Label(parent, text="🎮 GAMEPAD AIMBOT CONTROLS",
                font=('Arial', 14, 'bold'), bg='#1a1a1a', fg='white').pack(pady=10)

        # Connection section
        conn_frame = tk.LabelFrame(parent, text="🔗 Game & Controller Connection",
                                  font=('Arial', 10, 'bold'), bg='#1a1a1a', fg='white')
        conn_frame.pack(fill='x', padx=10, pady=5)

        self.connect_btn = tk.Button(conn_frame, text="🎮 Connect with Gamepad",
                                    font=('Arial', 10, 'bold'), bg='#4CAF50', fg='white',
                                    command=self.connect_gamepad)
        self.connect_btn.pack(fill='x', padx=10, pady=5)

        self.calibrate_btn = tk.Button(conn_frame, text="⚙️ Calibrate Controller",
                                      font=('Arial', 9), bg='#FF9800', fg='white',
                                      command=self.calibrate_controller)
        self.calibrate_btn.pack(fill='x', padx=10, pady=2)

        # Main controls
        control_frame = tk.LabelFrame(parent, text="🎯 Aimbot Controls",
                                     font=('Arial', 10, 'bold'), bg='#1a1a1a', fg='white')
        control_frame.pack(fill='x', padx=10, pady=5)

        btn_frame = tk.Frame(control_frame, bg='#1a1a1a')
        btn_frame.pack(fill='x', padx=10, pady=8)

        self.start_btn = tk.Button(btn_frame, text="🚀 START", bg='#4CAF50', fg='white',
                                  font=('Arial', 9, 'bold'), width=6, command=self.start_gamepad_aimbot, state='disabled')
        self.start_btn.pack(side='left', padx=2)

        self.stop_btn = tk.Button(btn_frame, text="🛑 STOP", bg='#f44336', fg='white',
                                 font=('Arial', 9, 'bold'), width=6, command=self.stop_aimbot, state='disabled')
        self.stop_btn.pack(side='left', padx=2)

        self.overlay_btn = tk.Button(btn_frame, text="👁️ OVERLAY", bg='#FF9800', fg='white',
                                    font=('Arial', 9, 'bold'), width=8, command=self.toggle_overlay)
        self.overlay_btn.pack(side='left', padx=2)

        # Gamepad aim settings
        aim_frame = tk.LabelFrame(parent, text="🎯 Gamepad Aim Settings",
                                 font=('Arial', 10, 'bold'), bg='#1a1a1a', fg='white')
        aim_frame.pack(fill='x', padx=10, pady=5)

        # Aim mode
        tk.Label(aim_frame, text="Aim Mode:", bg='#1a1a1a', fg='white', font=('Arial', 9)).pack(anchor='w', padx=10, pady=2)
        mode_combo = ttk.Combobox(aim_frame, textvariable=self.aim_mode,
                                 values=['ASSIST', 'MAGNETISM', 'SLOWDOWN', 'SMOOTH', 'SNAP'],
                                 state='readonly', width=15)
        mode_combo.pack(fill='x', padx=10, pady=2)

        # FOV
        tk.Label(aim_frame, text="FOV:", bg='#1a1a1a', fg='white', font=('Arial', 9)).pack(anchor='w', padx=10, pady=2)
        fov_scale = tk.Scale(aim_frame, from_=60, to=180, orient='horizontal',
                            variable=self.aim_fov, bg='#1a1a1a', fg='white', length=160)
        fov_scale.pack(fill='x', padx=10, pady=2)

        # Aim assist strength
        tk.Label(aim_frame, text="Aim Assist Strength:", bg='#1a1a1a', fg='white', font=('Arial', 9)).pack(anchor='w', padx=10, pady=2)
        assist_scale = tk.Scale(aim_frame, from_=0.0, to=2.0, resolution=0.1, orient='horizontal',
                               variable=self.aim_assist_strength, bg='#1a1a1a', fg='white', length=160)
        assist_scale.pack(fill='x', padx=10, pady=2)

        # Magnetism strength
        tk.Label(aim_frame, text="Target Magnetism:", bg='#1a1a1a', fg='white', font=('Arial', 9)).pack(anchor='w', padx=10, pady=2)
        mag_scale = tk.Scale(aim_frame, from_=0.0, to=1.0, resolution=0.1, orient='horizontal',
                            variable=self.magnetism_strength, bg='#1a1a1a', fg='white', length=160)
        mag_scale.pack(fill='x', padx=10, pady=2)

        # Gamepad features
        features_frame = tk.LabelFrame(parent, text="🎮 Gamepad Features",
                                      font=('Arial', 10, 'bold'), bg='#1a1a1a', fg='white')
        features_frame.pack(fill='x', padx=10, pady=5)

        gamepad_features = [
            ("🎯 Aim Assist", self.aim_assist_enabled),
            ("🧲 Target Magnetism", self.target_magnetism_enabled),
            ("🐌 Aim Slowdown", self.aim_slowdown_enabled),
            ("🤖 Auto Aim", self.auto_aim_enabled),
            ("📳 Vibration Feedback", self.vibration_feedback)
        ]

        for text, var in gamepad_features:
            tk.Checkbutton(features_frame, text=text, variable=var,
                          bg='#1a1a1a', fg='white', selectcolor='#333333',
                          font=('Arial', 8)).pack(anchor='w', padx=10, pady=1)

        # Controller settings
        controller_frame = tk.LabelFrame(parent, text="🕹️ Controller Settings",
                                        font=('Arial', 10, 'bold'), bg='#1a1a1a', fg='white')
        controller_frame.pack(fill='x', padx=10, pady=5)

        # Deadzone
        tk.Label(controller_frame, text="Deadzone:", bg='#1a1a1a', fg='white', font=('Arial', 9)).pack(anchor='w', padx=10, pady=2)
        deadzone_scale = tk.Scale(controller_frame, from_=0.0, to=0.5, resolution=0.01, orient='horizontal',
                                 variable=self.deadzone, bg='#1a1a1a', fg='white', length=160)
        deadzone_scale.pack(fill='x', padx=10, pady=2)

        # Aim sensitivity
        tk.Label(controller_frame, text="Aim Sensitivity:", bg='#1a1a1a', fg='white', font=('Arial', 9)).pack(anchor='w', padx=10, pady=2)
        aim_sens_scale = tk.Scale(controller_frame, from_=0.1, to=2.0, resolution=0.1, orient='horizontal',
                                 variable=self.aim_sensitivity, bg='#1a1a1a', fg='white', length=160)
        aim_sens_scale.pack(fill='x', padx=10, pady=2)

        # Response curve
        tk.Label(controller_frame, text="Response Curve:", bg='#1a1a1a', fg='white', font=('Arial', 9)).pack(anchor='w', padx=10, pady=2)
        curve_combo = ttk.Combobox(controller_frame, textvariable=self.response_curve,
                                  values=['linear', 'exponential', 'logarithmic'],
                                  state='readonly', width=15)
        curve_combo.pack(fill='x', padx=10, pady=2)

        # Visual settings
        visual_frame = tk.LabelFrame(parent, text="👁️ Visual Settings",
                                    font=('Arial', 10, 'bold'), bg='#1a1a1a', fg='white')
        visual_frame.pack(fill='x', padx=10, pady=5)

        visual_options = [
            ("🎮 Controller Input", self.show_controller_input),
            ("🕹️ Stick Visualization", self.show_stick_visualization),
            ("🎯 Aim Assist Zones", self.show_aim_assist_zones),
            ("🧲 Magnetism Radius", self.show_magnetism_radius)
        ]

        for text, var in visual_options:
            tk.Checkbutton(visual_frame, text=text, variable=var,
                          bg='#1a1a1a', fg='white', selectcolor='#333333',
                          font=('Arial', 8)).pack(anchor='w', padx=10, pady=1)

    def create_gamepad_monitor(self, parent):
        """Create gamepad monitoring panel"""
        tk.Label(parent, text="📊 GAMEPAD AIMBOT MONITORING",
                font=('Arial', 14, 'bold'), bg='#1a1a1a', fg='white').pack(pady=10)

        self.monitor_text = tk.Text(parent, bg='#0a0a0a', fg='#00ff00',
                                   font=('Courier', 9), wrap=tk.WORD)
        self.monitor_text.pack(fill='both', expand=True, padx=10, pady=10)

        scrollbar = tk.Scrollbar(self.monitor_text)
        scrollbar.pack(side='right', fill='y')
        self.monitor_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.monitor_text.yview)

    def create_gamepad_status_bar(self):
        """Create gamepad status bar"""
        status_frame = tk.Frame(self.root, bg='#333333', relief='sunken', bd=1)
        status_frame.pack(side='bottom', fill='x')

        self.status_label = tk.Label(status_frame, text="Status: Ready",
                                   bg='#333333', fg='white', font=('Arial', 9))
        self.status_label.pack(side='left', padx=10, pady=3)

        self.conn_label = tk.Label(status_frame, text="Connection: Disconnected",
                                 bg='#333333', fg='red', font=('Arial', 9))
        self.conn_label.pack(side='left', padx=10, pady=3)

        self.gamepad_label = tk.Label(status_frame, text="Controller: Not Connected",
                                     bg='#333333', fg='red', font=('Arial', 9))
        self.gamepad_label.pack(side='right', padx=10, pady=3)

    def connect_gamepad(self):
        """Connect with gamepad support"""
        try:
            self.status_label.config(text="Status: Connecting with gamepad...", fg='yellow')
            self.root.update()

            # Check controller connection
            controller_info = self.input_manager.get_controller_info()

            if not controller_info.get('connected'):
                messagebox.showerror("Controller Error",
                                   "No controller detected!\n\nPlease:\n• Connect your gamepad\n• Check USB/Bluetooth connection\n• Try reconnecting")
                self.status_label.config(text="Status: Controller Not Found", fg='red')
                return

            # Connect to game
            if self.memory_manager.find_back4blood_process():
                self.aim_engine = B4BGamepadAimEngine(
                    self.memory_manager, self.overlay_system, self.input_manager
                )
                self.connected = True

                self.conn_label.config(text="Connection: Gamepad Connected", fg='green')
                self.status_label.config(text="Status: Gamepad Ready", fg='green')

                # Update controller status
                controller_name = controller_info.get('name', 'Unknown')
                controller_type = controller_info.get('type', 'generic').upper()
                self.gamepad_label.config(text=f"Controller: {controller_type}", fg='green')
                self.controller_status.config(text=f"🎮 Controller: {controller_name} ({controller_type})", fg='#00ff00')

                self.start_btn.config(state='normal')
                self.connect_btn.config(text="✅ Gamepad Connected", state='disabled')

                messagebox.showinfo("Success", f"✅ Connected to Back 4 Blood with gamepad!\n\nController: {controller_name}\nType: {controller_type}")
            else:
                self.conn_label.config(text="Connection: Game Not Found", fg='red')
                self.status_label.config(text="Status: Game Connection Failed", fg='red')
                messagebox.showerror("Error", "❌ Failed to connect to Back 4 Blood")

        except Exception as e:
            messagebox.showerror("Error", f"Gamepad connection error: {e}")

    def calibrate_controller(self):
        """Calibrate controller"""
        try:
            self.input_manager.calibrate_controller()
            messagebox.showinfo("Calibration", "✅ Controller calibration complete!")
        except Exception as e:
            messagebox.showerror("Error", f"Calibration error: {e}")

    def start_gamepad_aimbot(self):
        """Start gamepad aimbot"""
        try:
            if not self.connected:
                messagebox.showerror("Error", "Please connect gamepad first!")
                return

            # Apply gamepad settings
            self.apply_gamepad_settings()

            # Start aimbot
            self.aim_engine.enabled = True
            self.running = True

            # Start gamepad aimbot thread
            self.aimbot_thread = threading.Thread(target=self.gamepad_aimbot_loop, daemon=True)
            self.aimbot_thread.start()

            self.status_label.config(text="Status: GAMEPAD RUNNING", fg='lime')
            self.start_btn.config(state='disabled')
            self.stop_btn.config(state='normal')

            messagebox.showinfo("Success", "🎮 Gamepad Aimbot started with controller support!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start gamepad aimbot: {e}")

    def stop_aimbot(self):
        """Stop aimbot"""
        try:
            self.running = False
            if self.aim_engine:
                self.aim_engine.enabled = False

            self.status_label.config(text="Status: STOPPED", fg='red')
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')

            messagebox.showinfo("Success", "🛑 Gamepad Aimbot stopped!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop: {e}")

    def toggle_overlay(self):
        """Toggle gamepad overlay"""
        self.overlay_system.toggle_overlay()
        status = "Active" if self.overlay_system.enabled else "Disabled"
        print(f"🎮 Gamepad overlay: {status}")

    def apply_gamepad_settings(self):
        """Apply gamepad settings"""
        if not self.aim_engine:
            return

        # Apply aim settings
        aim_mode_map = {
            'ASSIST': AimMode.ASSIST,
            'MAGNETISM': AimMode.MAGNETISM,
            'SLOWDOWN': AimMode.SLOWDOWN,
            'SMOOTH': AimMode.SMOOTH,
            'SNAP': AimMode.SNAP
        }

        self.aim_engine.aim_mode = aim_mode_map.get(self.aim_mode.get(), AimMode.ASSIST)
        self.aim_engine.fov = self.aim_fov.get()
        self.aim_engine.aim_assist_strength = self.aim_assist_strength.get()
        self.aim_engine.magnetism_strength = self.magnetism_strength.get()
        self.aim_engine.slowdown_strength = self.slowdown_strength.get()

        # Apply gamepad features
        self.aim_engine.aim_assist_enabled = self.aim_assist_enabled.get()
        self.aim_engine.target_magnetism_enabled = self.target_magnetism_enabled.get()
        self.aim_engine.aim_slowdown_enabled = self.aim_slowdown_enabled.get()
        self.aim_engine.auto_aim_enabled = self.auto_aim_enabled.get()
        self.aim_engine.vibration_feedback = self.vibration_feedback.get()

        # Apply controller settings
        self.input_manager.deadzone = self.deadzone.get()
        self.input_manager.sensitivity = self.sensitivity.get()
        self.input_manager.aim_sensitivity = self.aim_sensitivity.get()
        self.aim_engine.stick_acceleration = self.stick_acceleration.get()
        self.aim_engine.response_curve = self.response_curve.get()

        # Apply visual settings
        self.overlay_system.show_controller_input = self.show_controller_input.get()
        self.overlay_system.show_stick_visualization = self.show_stick_visualization.get()
        self.overlay_system.show_aim_assist_zones = self.show_aim_assist_zones.get()
        self.overlay_system.show_magnetism_radius = self.show_magnetism_radius.get()

    def gamepad_aimbot_loop(self):
        """Gamepad aimbot loop"""
        while self.running:
            try:
                if self.aim_engine:
                    self.aim_engine.update()
                time.sleep(1.0 / 120.0)  # 120 FPS for responsive gamepad input
            except Exception as e:
                print(f"Gamepad aimbot loop error: {e}")
                time.sleep(0.01)

    def update_display(self):
        """Update display with gamepad statistics"""
        try:
            stats_text = "🎮 BACK 4 BLOOD GAMEPAD DIRECTX AIMBOT - REAL-TIME STATUS\n"
            stats_text += "=" * 80 + "\n\n"

            # Controller Status
            controller_info = self.input_manager.get_controller_info()
            stats_text += "🎮 CONTROLLER STATUS:\n"
            if controller_info.get('connected'):
                stats_text += f"   Controller: {controller_info.get('name', 'Unknown')}\n"
                stats_text += f"   Type: {controller_info.get('type', 'generic').upper()}\n"
                stats_text += f"   Axes: {controller_info.get('axes', 0)} | Buttons: {controller_info.get('buttons', 0)}\n"
                stats_text += f"   Left Stick: ({controller_info.get('left_stick', (0, 0))[0]:.2f}, {controller_info.get('left_stick', (0, 0))[1]:.2f})\n"
                stats_text += f"   Right Stick: ({controller_info.get('right_stick', (0, 0))[0]:.2f}, {controller_info.get('right_stick', (0, 0))[1]:.2f})\n"
                stats_text += f"   Triggers: L={controller_info.get('triggers', (0, 0))[0]:.2f} R={controller_info.get('triggers', (0, 0))[1]:.2f}\n"
                stats_text += f"   Deadzone: {controller_info.get('deadzone', 0.1):.2f}\n"
                stats_text += f"   Aim Sensitivity: {controller_info.get('aim_sensitivity', 0.8):.2f}\n"
            else:
                stats_text += "   Status: No Controller Connected\n"
            stats_text += "\n"

            # Connection Status
            stats_text += "🔗 CONNECTION STATUS:\n"
            stats_text += f"   Game Process: {'Connected' if self.connected else 'Disconnected'}\n"
            stats_text += f"   Gamepad Overlay: {'Active' if self.overlay_system.enabled else 'Disabled'}\n"
            stats_text += f"   Aimbot Status: {'RUNNING' if self.running else 'STOPPED'}\n"
            if self.memory_manager.process_id:
                stats_text += f"   Process ID: {self.memory_manager.process_id}\n"
            stats_text += "\n"

            # Gamepad Performance
            if self.aim_engine:
                stats = self.aim_engine.get_gamepad_statistics()
                stats_text += "📊 GAMEPAD PERFORMANCE:\n"
                stats_text += f"   Targets Detected: {stats['targets_detected']}\n"
                stats_text += f"   Gamepad Adjustments: {stats['gamepad_adjustments']}\n"
                stats_text += f"   Aim Assist Activations: {stats['aim_assist_activations']}\n"
                stats_text += f"   Magnetism Activations: {stats['magnetism_activations']}\n"
                stats_text += f"   Vibration Triggers: {stats['vibration_triggers']}\n"
                stats_text += f"   Current Target: {stats['current_target'] or 'None'}\n"
                stats_text += f"   Overlay FPS: {stats['overlay_fps']:.1f}\n"
                stats_text += "\n"

            # Gamepad Configuration
            stats_text += "🎯 GAMEPAD CONFIGURATION:\n"
            stats_text += f"   Aim Mode: {self.aim_mode.get()}\n"
            stats_text += f"   FOV: {self.aim_fov.get():.1f}°\n"
            stats_text += f"   Aim Assist Strength: {self.aim_assist_strength.get():.1f}\n"
            stats_text += f"   Target Magnetism: {self.magnetism_strength.get():.1f}\n"
            stats_text += f"   Aim Slowdown: {self.slowdown_strength.get():.1f}\n"
            stats_text += f"   Stick Acceleration: {self.stick_acceleration.get():.1f}\n"
            stats_text += f"   Response Curve: {self.response_curve.get().upper()}\n"
            stats_text += "\n"

            # Gamepad Features
            stats_text += "🎮 GAMEPAD FEATURES:\n"
            stats_text += f"   🎯 Aim Assist: {'ON' if self.aim_assist_enabled.get() else 'OFF'}\n"
            stats_text += f"   🧲 Target Magnetism: {'ON' if self.target_magnetism_enabled.get() else 'OFF'}\n"
            stats_text += f"   🐌 Aim Slowdown: {'ON' if self.aim_slowdown_enabled.get() else 'OFF'}\n"
            stats_text += f"   🤖 Auto Aim: {'ON' if self.auto_aim_enabled.get() else 'OFF'}\n"
            stats_text += f"   📳 Vibration Feedback: {'ON' if self.vibration_feedback.get() else 'OFF'}\n"
            stats_text += "\n"

            # Visual Features
            stats_text += "👁️ VISUAL FEATURES:\n"
            stats_text += f"   🎮 Controller Input Display: {'ON' if self.show_controller_input.get() else 'OFF'}\n"
            stats_text += f"   🕹️ Stick Visualization: {'ON' if self.show_stick_visualization.get() else 'OFF'}\n"
            stats_text += f"   🎯 Aim Assist Zones: {'ON' if self.show_aim_assist_zones.get() else 'OFF'}\n"
            stats_text += f"   🧲 Magnetism Radius: {'ON' if self.show_magnetism_radius.get() else 'OFF'}\n"
            stats_text += "\n"

            # Gamepad Capabilities
            stats_text += "🚀 GAMEPAD CAPABILITIES:\n"
            stats_text += "   ✅ Xbox/PlayStation/Generic Controller Support\n"
            stats_text += "   ✅ Advanced Aim Assist Algorithms\n"
            stats_text += "   ✅ Target Magnetism System\n"
            stats_text += "   ✅ Aim Slowdown on Target\n"
            stats_text += "   ✅ Controller Vibration Feedback\n"
            stats_text += "   ✅ Customizable Response Curves\n"
            stats_text += "   ✅ Real-Time Input Monitoring\n"
            stats_text += "   ✅ DirectX Hardware Acceleration\n"
            stats_text += "\n"

            # System Information
            stats_text += "💻 SYSTEM INFORMATION:\n"
            stats_text += f"   Pygame: {'Available' if PYGAME_AVAILABLE else 'Not Available'}\n"
            stats_text += f"   XInput: {'Available' if XINPUT_AVAILABLE else 'Not Available'}\n"
            stats_text += f"   DirectX: {'Available' if DX_AVAILABLE else 'Not Available'}\n"
            stats_text += f"   Controllers Detected: {len(self.input_manager.controllers)}\n"
            stats_text += "\n"

            stats_text += "⚠️  EDUCATIONAL USE ONLY - GAMEPAD PROGRAMMING DEMONSTRATION ⚠️\n"
            stats_text += "This demonstrates advanced gamepad programming and controller input techniques."

            # Update display
            self.monitor_text.delete(1.0, tk.END)
            self.monitor_text.insert(1.0, stats_text)
            self.monitor_text.see(tk.END)

        except Exception as e:
            print(f"Display update error: {e}")

        # Schedule next update
        self.root.after(1000, self.update_display)

    def run(self):
        """Run gamepad GUI"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            print(f"Gamepad GUI error: {e}")

    def on_closing(self):
        """Handle closing with gamepad cleanup"""
        self.running = False
        if self.memory_manager:
            self.memory_manager.cleanup()
        if self.overlay_system:
            self.overlay_system.cleanup()
        if self.input_manager:
            self.input_manager.cleanup()
        self.root.destroy()

def main():
    """Main entry point"""
    print(EDUCATIONAL_WARNING)

    response = input("\nDo you agree to use this gamepad aimbot for educational purposes only? (yes/no): ")
    if response.lower() != 'yes':
        print("❌ Agreement not accepted. Exiting.")
        return

    try:
        print("\n🎮 Starting Back 4 Blood Gamepad DirectX Aimbot...")
        print("🎮 Initializing gamepad input system...")
        print("🔧 Initializing DirectX overlay system...")

        app = B4BGamepadAimbotGUI()
        app.run()

    except Exception as e:
        print(f"❌ Gamepad Error: {e}")
    finally:
        print("👋 Gamepad Aimbot closed!")

if __name__ == "__main__":
    main()
