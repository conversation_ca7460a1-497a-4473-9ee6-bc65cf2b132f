#!/usr/bin/env python3
"""
🎯 STICKY AIM AIMBOT - MAIN APPLICATION
======================================
Complete sticky aim aimbot system with GUI and command-line interfaces.

⚠️  EDUCATIONAL PURPOSE ONLY ⚠️
This software is for educational and research purposes only.
Do not use in online games or violate Terms of Service.
"""

import sys
import os
import argparse
import logging
import time
import signal
from typing import Optional

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import sticky_aimbot
from sticky_aimbot.core.aimbot_engine import AimbotEng<PERSON>, AimbotState
from sticky_aimbot.gui.main_window import AimbotGUI
from sticky_aimbot.config.settings_manager import SettingsManager
from sticky_aimbot.utils.logger import setup_logger

class StickyAimbotApp:
    """Main application class for the sticky aimbot system"""
    
    def __init__(self):
        """Initialize the application"""
        self.logger = None
        self.aimbot_engine: Optional[AimbotEngine] = None
        self.gui: Optional[AimbotGUI] = None
        self.running = False
        self.config_path = None
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n🛑 Received signal {signum}, shutting down gracefully...")
        self.shutdown()
        sys.exit(0)
    
    def initialize(self, config_path: Optional[str] = None, 
                  log_level: str = "INFO") -> bool:
        """Initialize the application"""
        try:
            # Show educational warning
            sticky_aimbot.show_educational_warning()
            
            # Setup logging
            self.logger = setup_logger(
                name="sticky_aimbot_app",
                level=log_level,
                log_file="logs/app.log"
            )
            
            self.logger.info("🎯 Initializing Sticky Aimbot Application")
            
            # Store config path
            self.config_path = config_path
            
            # Check system requirements
            if not self._check_requirements():
                return False
            
            self.logger.info("✅ Application initialized successfully")
            return True
            
        except Exception as e:
            print(f"❌ Application initialization failed: {e}")
            return False
    
    def _check_requirements(self) -> bool:
        """Check system requirements"""
        try:
            # Check Python version
            if sys.version_info < (3, 9):
                self.logger.error("❌ Python 3.9+ required")
                return False
            
            # Check platform
            if sys.platform != "win32":
                self.logger.warning("⚠️ This system is optimized for Windows")
            
            # Check dependencies
            sticky_aimbot.check_dependencies()
            
            # Check system requirements
            sticky_aimbot.check_system_requirements()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Requirements check failed: {e}")
            return False
    
    def run_gui(self) -> int:
        """Run the application with GUI"""
        try:
            self.logger.info("🖥️ Starting GUI mode")
            
            # Create and run GUI
            self.gui = AimbotGUI(config_path=self.config_path)
            self.gui.run()
            
            return 0
            
        except Exception as e:
            self.logger.error(f"❌ GUI mode failed: {e}")
            return 1
    
    def run_cli(self, auto_start: bool = False) -> int:
        """Run the application in CLI mode"""
        try:
            self.logger.info("💻 Starting CLI mode")
            
            # Create aimbot engine
            self.aimbot_engine = AimbotEngine(self.config_path)
            
            if not self.aimbot_engine.initialize_components():
                self.logger.error("❌ Failed to initialize aimbot components")
                return 1
            
            # Setup callbacks
            self._setup_callbacks()
            
            # Auto-start if requested
            if auto_start:
                self.logger.info("🚀 Auto-starting aimbot...")
                if not self.aimbot_engine.start():
                    self.logger.error("❌ Failed to start aimbot")
                    return 1
            
            # CLI interaction loop
            self._cli_loop()
            
            return 0
            
        except Exception as e:
            self.logger.error(f"❌ CLI mode failed: {e}")
            return 1
    
    def _setup_callbacks(self):
        """Setup aimbot engine callbacks"""
        if not self.aimbot_engine:
            return
        
        self.aimbot_engine.add_callback('target_acquired', self._on_target_acquired)
        self.aimbot_engine.add_callback('target_lost', self._on_target_lost)
        self.aimbot_engine.add_callback('state_changed', self._on_state_changed)
        self.aimbot_engine.add_callback('error_occurred', self._on_error_occurred)
    
    def _on_target_acquired(self, target):
        """Handle target acquired event"""
        self.logger.info(f"🎯 Target acquired: {target.id if target else 'Unknown'}")
    
    def _on_target_lost(self, target):
        """Handle target lost event"""
        self.logger.info(f"❌ Target lost: {target.id if target else 'Unknown'}")
    
    def _on_state_changed(self, state: AimbotState):
        """Handle state change event"""
        self.logger.info(f"🔄 State changed: {state.value}")
    
    def _on_error_occurred(self, error):
        """Handle error event"""
        self.logger.error(f"❌ Aimbot error: {error}")
    
    def _cli_loop(self):
        """Main CLI interaction loop"""
        self.running = True
        
        print("\n🎯 STICKY AIMBOT CLI")
        print("====================")
        print("Commands:")
        print("  start    - Start the aimbot")
        print("  stop     - Stop the aimbot")
        print("  pause    - Pause the aimbot")
        print("  resume   - Resume the aimbot")
        print("  status   - Show status")
        print("  stats    - Show statistics")
        print("  config   - Show configuration")
        print("  help     - Show this help")
        print("  quit     - Exit application")
        print()
        
        while self.running:
            try:
                command = input("aimbot> ").strip().lower()
                
                if command == "start":
                    self._handle_start_command()
                elif command == "stop":
                    self._handle_stop_command()
                elif command == "pause":
                    self._handle_pause_command()
                elif command == "resume":
                    self._handle_resume_command()
                elif command == "status":
                    self._handle_status_command()
                elif command == "stats":
                    self._handle_stats_command()
                elif command == "config":
                    self._handle_config_command()
                elif command == "help":
                    self._handle_help_command()
                elif command in ["quit", "exit", "q"]:
                    break
                elif command == "":
                    continue
                else:
                    print(f"❌ Unknown command: {command}")
                    print("Type 'help' for available commands")
                
            except KeyboardInterrupt:
                print("\n🛑 Interrupted by user")
                break
            except EOFError:
                print("\n🛑 EOF received")
                break
            except Exception as e:
                print(f"❌ Command error: {e}")
        
        print("👋 Goodbye!")
    
    def _handle_start_command(self):
        """Handle start command"""
        if not self.aimbot_engine:
            print("❌ Aimbot engine not initialized")
            return
        
        if self.aimbot_engine.is_running():
            print("⚠️ Aimbot is already running")
            return
        
        print("🚀 Starting aimbot...")
        if self.aimbot_engine.start():
            print("✅ Aimbot started successfully")
        else:
            print("❌ Failed to start aimbot")
    
    def _handle_stop_command(self):
        """Handle stop command"""
        if not self.aimbot_engine:
            print("❌ Aimbot engine not initialized")
            return
        
        if not self.aimbot_engine.is_running():
            print("⚠️ Aimbot is not running")
            return
        
        print("🛑 Stopping aimbot...")
        if self.aimbot_engine.stop():
            print("✅ Aimbot stopped successfully")
        else:
            print("❌ Failed to stop aimbot")
    
    def _handle_pause_command(self):
        """Handle pause command"""
        if not self.aimbot_engine:
            print("❌ Aimbot engine not initialized")
            return
        
        if self.aimbot_engine.pause():
            print("⏸️ Aimbot paused")
        else:
            print("❌ Failed to pause aimbot")
    
    def _handle_resume_command(self):
        """Handle resume command"""
        if not self.aimbot_engine:
            print("❌ Aimbot engine not initialized")
            return
        
        if self.aimbot_engine.resume():
            print("▶️ Aimbot resumed")
        else:
            print("❌ Failed to resume aimbot")
    
    def _handle_status_command(self):
        """Handle status command"""
        if not self.aimbot_engine:
            print("❌ Aimbot engine not initialized")
            return
        
        state = self.aimbot_engine.get_state()
        print(f"📊 Status: {state.value}")
        
        if self.aimbot_engine.is_running():
            stats = self.aimbot_engine.get_stats()
            print(f"🎯 Targets detected: {stats.targets_detected}")
            print(f"🔒 Targets locked: {stats.targets_locked}")
            print(f"📈 FPS: {stats.fps:.1f}")
            print(f"⏱️ Runtime: {stats.total_runtime:.1f}s")
    
    def _handle_stats_command(self):
        """Handle stats command"""
        if not self.aimbot_engine:
            print("❌ Aimbot engine not initialized")
            return
        
        stats = self.aimbot_engine.get_stats()
        print("\n📊 AIMBOT STATISTICS")
        print("====================")
        print(f"Targets detected: {stats.targets_detected}")
        print(f"Targets locked: {stats.targets_locked}")
        print(f"Shots fired: {stats.shots_fired}")
        print(f"Accuracy: {stats.accuracy_percentage:.1f}%")
        print(f"Average lock time: {stats.average_lock_time:.2f}s")
        print(f"Total runtime: {stats.total_runtime:.1f}s")
        print(f"Current FPS: {stats.fps:.1f}")
        print()
    
    def _handle_config_command(self):
        """Handle config command"""
        if not self.aimbot_engine:
            print("❌ Aimbot engine not initialized")
            return
        
        config = self.aimbot_engine.config
        print("\n⚙️ CONFIGURATION")
        print("================")
        for section, values in config.items():
            print(f"\n[{section}]")
            if isinstance(values, dict):
                for key, value in values.items():
                    print(f"  {key}: {value}")
            else:
                print(f"  {values}")
        print()
    
    def _handle_help_command(self):
        """Handle help command"""
        print("\n🎯 STICKY AIMBOT COMMANDS")
        print("=========================")
        print("start    - Start the aimbot engine")
        print("stop     - Stop the aimbot engine")
        print("pause    - Pause aimbot operation")
        print("resume   - Resume aimbot operation")
        print("status   - Show current status")
        print("stats    - Show detailed statistics")
        print("config   - Show current configuration")
        print("help     - Show this help message")
        print("quit     - Exit the application")
        print()
    
    def shutdown(self):
        """Shutdown the application"""
        self.running = False
        
        if self.logger:
            self.logger.info("🛑 Shutting down application")
        
        # Stop aimbot engine
        if self.aimbot_engine and self.aimbot_engine.is_running():
            self.aimbot_engine.stop()
        
        # Close GUI
        if self.gui:
            self.gui.close()
        
        if self.logger:
            self.logger.info("✅ Application shutdown complete")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="🎯 Sticky Aim Aimbot - Educational Research Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Run with GUI
  python main.py --cli              # Run in CLI mode
  python main.py --cli --auto-start # Auto-start in CLI mode
  python main.py --config custom.json # Use custom config
        """
    )
    
    parser.add_argument(
        "--cli", action="store_true",
        help="Run in command-line interface mode"
    )
    
    parser.add_argument(
        "--auto-start", action="store_true",
        help="Automatically start aimbot (CLI mode only)"
    )
    
    parser.add_argument(
        "--config", type=str, metavar="PATH",
        help="Path to configuration file"
    )
    
    parser.add_argument(
        "--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO", help="Set logging level"
    )
    
    parser.add_argument(
        "--version", action="version",
        version=f"Sticky Aimbot v{sticky_aimbot.__version__}"
    )
    
    args = parser.parse_args()
    
    # Create application
    app = StickyAimbotApp()
    
    # Initialize
    if not app.initialize(config_path=args.config, log_level=args.log_level):
        return 1
    
    try:
        # Run in appropriate mode
        if args.cli:
            return app.run_cli(auto_start=args.auto_start)
        else:
            return app.run_gui()
    
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        return 0
    
    except Exception as e:
        print(f"❌ Application error: {e}")
        return 1
    
    finally:
        app.shutdown()

if __name__ == "__main__":
    sys.exit(main())
