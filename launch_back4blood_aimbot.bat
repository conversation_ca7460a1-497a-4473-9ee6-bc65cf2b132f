@echo off
title Back 4 Blood Steam Aimbot Launcher
color 0A

echo.
echo ========================================
echo   BACK 4 BLOOD STEAM AIMBOT LAUNCHER
echo ========================================
echo.
echo Starting Back 4 Blood Steam Aimbot...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if Back 4 Blood is running
tasklist /FI "IMAGENAME eq Back4Blood-Win64-Shipping.exe" 2>NUL | find /I /N "Back4Blood-Win64-Shipping.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Back 4 Blood detected - Steam version
) else (
    echo ⚠️  Back 4 Blood not detected
    echo    Please start Back 4 Blood through Steam first
    echo.
)

REM Install required packages
echo Installing required packages...
pip install opencv-python ultralytics torch psutil pywin32 tkinter

echo.
echo Starting aimbot...
echo.

REM Run the aimbot
python back4blood_steam_aimbot.py

echo.
echo Aimbot closed.
pause
