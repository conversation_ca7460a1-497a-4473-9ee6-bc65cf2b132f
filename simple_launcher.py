#!/usr/bin/env python3
"""
🚀 SIMPLE AIMBOT LAUNCHER
========================
Choose between different aimbot interfaces
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
import os

class SimpleLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Aimbot Launcher")
        self.root.geometry("500x400")
        self.root.configure(bg='#1e1e1e')
        
        self.create_widgets()
        
    def create_widgets(self):
        # Title
        title_label = tk.Label(self.root, text="🎯 AI AIMBOT LAUNCHER", 
                              font=('Arial', 20, 'bold'), 
                              bg='#1e1e1e', fg='white')
        title_label.pack(pady=30)
        
        # Subtitle
        subtitle_label = tk.Label(self.root, text="Choose your interface:", 
                                 font=('Arial', 12), 
                                 bg='#1e1e1e', fg='lightgray')
        subtitle_label.pack(pady=10)
        
        # Button frame
        button_frame = tk.Frame(self.root, bg='#1e1e1e')
        button_frame.pack(pady=30)
        
        # Option 1: Fixed GUI (Recommended)
        btn1 = tk.Button(button_frame, text="🎛️ CONTROL PANEL\n(Recommended)", 
                        font=('Arial', 12, 'bold'),
                        bg='#4CAF50', fg='white',
                        width=20, height=3,
                        command=self.launch_fixed_gui)
        btn1.pack(pady=10)
        
        # Option 2: Direct Aimbot
        btn2 = tk.Button(button_frame, text="🎯 DIRECT AIMBOT\n(Advanced Users)", 
                        font=('Arial', 12, 'bold'),
                        bg='#2196F3', fg='white',
                        width=20, height=3,
                        command=self.launch_direct_aimbot)
        btn2.pack(pady=10)
        
        # Option 3: Original Settings GUI
        btn3 = tk.Button(button_frame, text="⚙️ ORIGINAL SETTINGS\n(May have issues)", 
                        font=('Arial', 12, 'bold'),
                        bg='#FF9800', fg='white',
                        width=20, height=3,
                        command=self.launch_original_gui)
        btn3.pack(pady=10)
        
        # Info
        info_label = tk.Label(self.root, 
                            text="💡 Tip: Use Control Panel for easiest experience", 
                            font=('Arial', 10), 
                            bg='#1e1e1e', fg='yellow')
        info_label.pack(pady=20)
        
    def launch_fixed_gui(self):
        """Launch the fixed GUI with activation buttons"""
        try:
            subprocess.Popen([sys.executable, 'fixed_aimbot_gui.py'])
            messagebox.showinfo("Launched", "🎛️ Control Panel launched!")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch Control Panel:\n{str(e)}")
            
    def launch_direct_aimbot(self):
        """Launch aimbot directly"""
        try:
            if os.path.exists('visual_aimbot_overlay.py'):
                subprocess.Popen([sys.executable, 'visual_aimbot_overlay.py'])
                messagebox.showinfo("Launched", 
                                  "🎯 Direct Aimbot launched!\n\n" +
                                  "• Press F1 to activate/deactivate\n" +
                                  "• Press Q to quit")
                self.root.quit()
            else:
                messagebox.showerror("Error", "visual_aimbot_overlay.py not found!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch aimbot:\n{str(e)}")
            
    def launch_original_gui(self):
        """Launch original settings GUI"""
        try:
            subprocess.Popen([sys.executable, 'aimbot_settings_gui.py'])
            messagebox.showwarning("Launched", 
                                 "⚙️ Original Settings GUI launched!\n\n" +
                                 "Note: This may have threading issues.")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch original GUI:\n{str(e)}")
            
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    launcher = SimpleLauncher()
    launcher.run()
