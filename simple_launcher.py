#!/usr/bin/env python3
"""
🚀 SIMPLE AIMBOT LAUNCHER
========================
Choose between different aimbot interfaces
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
import os

class SimpleLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Aimbot Launcher")
        self.root.geometry("500x400")
        self.root.configure(bg='#1e1e1e')
        
        self.create_widgets()
        
    def create_widgets(self):
        # Title
        title_label = tk.Label(self.root, text="🎯 AI AIMBOT LAUNCHER", 
                              font=('Arial', 20, 'bold'), 
                              bg='#1e1e1e', fg='white')
        title_label.pack(pady=30)
        
        # Subtitle
        subtitle_label = tk.Label(self.root, text="Choose your interface:", 
                                 font=('Arial', 12), 
                                 bg='#1e1e1e', fg='lightgray')
        subtitle_label.pack(pady=10)
        
        # Button frame
        button_frame = tk.Frame(self.root, bg='#1e1e1e')
        button_frame.pack(pady=30)
        
        # Option 1: Fixed GUI (Recommended)
        btn1 = tk.Button(button_frame, text="🎛️ CONTROL PANEL\n(Recommended)", 
                        font=('Arial', 12, 'bold'),
                        bg='#4CAF50', fg='white',
                        width=20, height=3,
                        command=self.launch_fixed_gui)
        btn1.pack(pady=10)
        
        # Option 2: Direct Aimbot
        btn2 = tk.Button(button_frame, text="🎯 DIRECT AIMBOT\n(Advanced Users)", 
                        font=('Arial', 12, 'bold'),
                        bg='#2196F3', fg='white',
                        width=20, height=3,
                        command=self.launch_direct_aimbot)
        btn2.pack(pady=10)
        
        # Option 3: Remote Gamepad Aimbot (NEW!)
        btn3 = tk.Button(button_frame, text="🎮 REMOTE GAMEPAD\n(Console Style)",
                        font=('Arial', 12, 'bold'),
                        bg='#9C27B0', fg='white',
                        width=20, height=3,
                        command=self.launch_gamepad_aimbot)
        btn3.pack(pady=10)

        # Option 4: Memory Injection Aimbot (NEW!)
        btn4 = tk.Button(button_frame, text="🧠 MEMORY INJECTION\n(Maximum Performance)",
                        font=('Arial', 12, 'bold'),
                        bg='#8B0000', fg='white',
                        width=20, height=3,
                        command=self.launch_memory_aimbot)
        btn4.pack(pady=10)

        # Option 5: Original Settings GUI
        btn5 = tk.Button(button_frame, text="⚙️ ORIGINAL SETTINGS\n(May have issues)",
                        font=('Arial', 12, 'bold'),
                        bg='#FF9800', fg='white',
                        width=20, height=3,
                        command=self.launch_original_gui)
        btn5.pack(pady=10)
        
        # Info
        info_label = tk.Label(self.root,
                            text="💡 NEW: Memory Injection for maximum performance!",
                            font=('Arial', 10),
                            bg='#1e1e1e', fg='yellow')
        info_label.pack(pady=20)
        
    def launch_fixed_gui(self):
        """Launch the fixed GUI with activation buttons"""
        try:
            subprocess.Popen([sys.executable, 'fixed_aimbot_gui.py'])
            messagebox.showinfo("Launched", "🎛️ Control Panel launched!")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch Control Panel:\n{str(e)}")
            
    def launch_direct_aimbot(self):
        """Launch aimbot directly"""
        try:
            if os.path.exists('visual_aimbot_overlay.py'):
                subprocess.Popen([sys.executable, 'visual_aimbot_overlay.py'])
                messagebox.showinfo("Launched", 
                                  "🎯 Direct Aimbot launched!\n\n" +
                                  "• Press F1 to activate/deactivate\n" +
                                  "• Press Q to quit")
                self.root.quit()
            else:
                messagebox.showerror("Error", "visual_aimbot_overlay.py not found!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch aimbot:\n{str(e)}")

    def launch_gamepad_aimbot(self):
        """Launch remote gamepad aimbot"""
        try:
            subprocess.Popen([sys.executable, 'gamepad_aimbot_gui.py'])
            messagebox.showinfo("Launched",
                              "🎮 Remote Gamepad Aimbot launched!\n\n" +
                              "• Creates virtual Xbox controller\n" +
                              "• Perfect for console-style games\n" +
                              "• Uses right stick for aiming\n" +
                              "• Press F1 to activate/deactivate")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch gamepad aimbot:\n{str(e)}")

    def launch_memory_aimbot(self):
        """Launch memory injection aimbot"""
        try:
            # Show warning first
            warning = messagebox.askyesno("⚠️ CRITICAL WARNING",
                                        "MEMORY INJECTION AIMBOT\n\n" +
                                        "⚠️ WARNINGS:\n" +
                                        "• This is for EDUCATIONAL PURPOSES ONLY\n" +
                                        "• May trigger anti-cheat systems\n" +
                                        "• Use only in offline/practice modes\n" +
                                        "• Requires administrator privileges\n" +
                                        "• Direct memory access to game processes\n\n" +
                                        "Continue anyway?")

            if not warning:
                return

            subprocess.Popen([sys.executable, 'memory_aimbot_gui.py'])
            messagebox.showinfo("Launched",
                              "🧠 Memory Injection Aimbot launched!\n\n" +
                              "• Direct memory access for max performance\n" +
                              "• Reads player positions from game memory\n" +
                              "• Injects aim angles directly into game\n" +
                              "• Select target process from dropdown\n\n" +
                              "⚠️ Use responsibly and ethically!")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch memory aimbot:\n{str(e)}")

    def launch_original_gui(self):
        """Launch original settings GUI"""
        try:
            subprocess.Popen([sys.executable, 'aimbot_settings_gui.py'])
            messagebox.showwarning("Launched", 
                                 "⚙️ Original Settings GUI launched!\n\n" +
                                 "Note: This may have threading issues.")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch original GUI:\n{str(e)}")
            
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    launcher = SimpleLauncher()
    launcher.run()
