#!/usr/bin/env python3
"""
🎯 STICKY AIM DEMONSTRATION
===========================
Visual demonstration of sticky aim mechanics and behavior
"""

import tkinter as tk
import math
import time
import random
import numpy as np
from sticky_aim_system import StickyAimSystem, StickyAimConfig, Target

class StickyAimDemo:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Sticky Aim Demonstration")
        self.root.geometry("1000x700")
        self.root.configure(bg='black')
        
        # Demo settings
        self.running = True
        self.demo_speed = 1.0
        
        # Simulation state
        self.local_player = {'x': 500, 'y': 350, 'pitch': 0, 'yaw': 0}
        self.targets = []
        self.crosshair_pos = [500, 350]
        
        # Sticky aim system
        config = StickyAimConfig(
            max_lock_distance=300,
            max_lock_fov=60,
            lock_strength=0.8,
            smoothing_factor=4.0,
            prediction_enabled=True,
            micro_adjustments=True,
            detection_avoidance=True
        )
        self.sticky_aim = StickyAimSystem(config)
        
        # Create GUI
        self.create_widgets()
        
        # Start simulation
        self.start_simulation()
        
    def create_widgets(self):
        """Create demo GUI"""
        # Control panel
        control_frame = tk.Frame(self.root, bg='#333333')
        control_frame.pack(side='top', fill='x', padx=10, pady=5)
        
        tk.Label(control_frame, text="🎯 Sticky Aim Demo Controls:", 
                bg='#333333', fg='white', font=('Arial', 12, 'bold')).pack(side='left')
        
        tk.Button(control_frame, text="▶️ Start", bg='green', fg='white',
                 command=self.start_demo).pack(side='left', padx=5)
        
        tk.Button(control_frame, text="⏸️ Pause", bg='orange', fg='white',
                 command=self.pause_demo).pack(side='left', padx=5)
        
        tk.Button(control_frame, text="🔄 Reset", bg='blue', fg='white',
                 command=self.reset_demo).pack(side='left', padx=5)
        
        tk.Button(control_frame, text="⚙️ Config", bg='purple', fg='white',
                 command=self.open_config).pack(side='left', padx=5)
        
        # Speed control
        tk.Label(control_frame, text="Speed:", bg='#333333', fg='white').pack(side='left', padx=10)
        self.speed_var = tk.DoubleVar(value=1.0)
        speed_scale = tk.Scale(control_frame, from_=0.1, to=3.0, resolution=0.1,
                              orient='horizontal', variable=self.speed_var,
                              bg='#333333', fg='white', length=100)
        speed_scale.pack(side='left', padx=5)
        
        # Canvas for visualization
        self.canvas = tk.Canvas(self.root, bg='black', highlightthickness=0)
        self.canvas.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Status panel
        status_frame = tk.Frame(self.root, bg='#333333')
        status_frame.pack(side='bottom', fill='x', padx=10, pady=5)
        
        self.status_label = tk.Label(status_frame, text="Status: Ready", 
                                   bg='#333333', fg='lime', font=('Arial', 10, 'bold'))
        self.status_label.pack(side='left')
        
        self.metrics_label = tk.Label(status_frame, text="Metrics: --", 
                                    bg='#333333', fg='cyan', font=('Arial', 10))
        self.metrics_label.pack(side='right')
        
    def create_targets(self):
        """Create demo targets"""
        self.targets = []
        
        # Create 5 moving targets
        for i in range(5):
            target = Target(
                id=i,
                x=random.uniform(100, 900),
                y=random.uniform(100, 600),
                z=0,
                health=random.uniform(50, 100),
                team=2,  # Enemy team
                last_seen=time.time(),
                velocity_x=random.uniform(-50, 50),
                velocity_y=random.uniform(-50, 50)
            )
            self.targets.append(target)
            
    def update_targets(self):
        """Update target positions"""
        current_time = time.time()
        dt = 0.016 * self.demo_speed  # 60 FPS
        
        for target in self.targets:
            # Update position
            target.x += target.velocity_x * dt
            target.y += target.velocity_y * dt
            
            # Bounce off walls
            if target.x < 50 or target.x > 950:
                target.velocity_x *= -1
            if target.y < 50 or target.y > 650:
                target.velocity_y *= -1
            
            # Keep in bounds
            target.x = max(50, min(950, target.x))
            target.y = max(50, min(650, target.y))
            
            # Add some random movement
            target.velocity_x += random.uniform(-5, 5)
            target.velocity_y += random.uniform(-5, 5)
            
            # Limit velocity
            max_vel = 100
            vel_mag = math.sqrt(target.velocity_x**2 + target.velocity_y**2)
            if vel_mag > max_vel:
                target.velocity_x = (target.velocity_x / vel_mag) * max_vel
                target.velocity_y = (target.velocity_y / vel_mag) * max_vel
            
            target.last_seen = current_time
            
    def update_sticky_aim(self):
        """Update sticky aim system"""
        # Convert screen coordinates to world coordinates
        local_pos = (self.local_player['x'], self.local_player['y'], 0)
        local_angles = (self.local_player['pitch'], self.local_player['yaw'])
        
        # Update sticky aim
        aim_adjustment = self.sticky_aim.update(self.targets, local_pos, local_angles)
        
        if aim_adjustment:
            # Apply adjustment to crosshair
            self.crosshair_pos[0] += aim_adjustment[1] * 10  # Yaw adjustment
            self.crosshair_pos[1] += aim_adjustment[0] * 10  # Pitch adjustment
            
            # Keep crosshair in bounds
            self.crosshair_pos[0] = max(50, min(950, self.crosshair_pos[0]))
            self.crosshair_pos[1] = max(50, min(650, self.crosshair_pos[1]))
            
    def draw_scene(self):
        """Draw the demo scene"""
        self.canvas.delete("all")
        
        # Get canvas dimensions
        width = self.canvas.winfo_width()
        height = self.canvas.winfo_height()
        
        if width <= 1 or height <= 1:
            return
        
        # Draw FOV circle
        fov_radius = self.sticky_aim.config.max_lock_fov * 3
        self.canvas.create_oval(
            self.crosshair_pos[0] - fov_radius, self.crosshair_pos[1] - fov_radius,
            self.crosshair_pos[0] + fov_radius, self.crosshair_pos[1] + fov_radius,
            outline='yellow', width=2, dash=(5, 5)
        )
        
        # Draw targets
        for target in self.targets:
            # Determine color based on status
            if (self.sticky_aim.current_target and 
                target.id == self.sticky_aim.current_target.id):
                color = 'red'  # Locked target
                size = 20
            else:
                color = 'orange'  # Available target
                size = 15
            
            # Draw target
            fill_color = '#ff4444' if color == 'red' else '#ffaa44'
            self.canvas.create_rectangle(
                target.x - size, target.y - size,
                target.x + size, target.y + size,
                outline=color, width=3, fill=fill_color
            )
            
            # Draw target info
            info = f"T{target.id}\nHP:{target.health:.0f}"
            self.canvas.create_text(
                target.x, target.y - size - 20,
                text=info, fill=color, font=('Arial', 8, 'bold')
            )
            
            # Draw velocity vector
            vel_scale = 2
            self.canvas.create_line(
                target.x, target.y,
                target.x + target.velocity_x * vel_scale,
                target.y + target.velocity_y * vel_scale,
                fill='cyan', width=2, arrow=tk.LAST
            )
        
        # Draw crosshair
        size = 15
        self.canvas.create_line(
            self.crosshair_pos[0] - size, self.crosshair_pos[1],
            self.crosshair_pos[0] + size, self.crosshair_pos[1],
            fill='lime', width=3
        )
        self.canvas.create_line(
            self.crosshair_pos[0], self.crosshair_pos[1] - size,
            self.crosshair_pos[0], self.crosshair_pos[1] + size,
            fill='lime', width=3
        )
        
        # Draw aim line to locked target
        if (self.sticky_aim.current_target and 
            self.sticky_aim.target_locked):
            target = self.sticky_aim.current_target
            self.canvas.create_line(
                self.crosshair_pos[0], self.crosshair_pos[1],
                target.x, target.y,
                fill='red', width=2, dash=(3, 3)
            )
        
        # Draw legend
        legend_x = 20
        legend_y = 20
        
        legend_items = [
            ("🎯 Crosshair", 'lime'),
            ("🔴 Locked Target", 'red'),
            ("🟠 Available Target", 'orange'),
            ("🟡 FOV Circle", 'yellow'),
            ("🔵 Velocity Vector", 'cyan'),
            ("🔴 Aim Line", 'red')
        ]
        
        for i, (text, color) in enumerate(legend_items):
            self.canvas.create_text(
                legend_x, legend_y + i * 20,
                text=text, fill=color, font=('Arial', 10, 'bold'),
                anchor='w'
            )
            
    def update_status(self):
        """Update status display"""
        if self.sticky_aim.target_locked:
            target_id = self.sticky_aim.current_target.id if self.sticky_aim.current_target else "None"
            self.status_label.config(text=f"Status: LOCKED ON TARGET {target_id}", fg='red')
        else:
            self.status_label.config(text="Status: SCANNING FOR TARGETS", fg='yellow')
        
        # Update metrics
        status = self.sticky_aim.get_status()
        metrics_text = (f"Accuracy: {status['lock_accuracy']:.2f} | "
                       f"Switches: {status['target_switches']} | "
                       f"Targets: {len(self.targets)}")
        self.metrics_label.config(text=metrics_text)
        
    def simulation_loop(self):
        """Main simulation loop"""
        if self.running:
            # Update demo speed
            self.demo_speed = self.speed_var.get()
            
            # Update simulation
            self.update_targets()
            self.update_sticky_aim()
            
            # Update display
            self.draw_scene()
            self.update_status()
            
            # Schedule next update
            self.root.after(16, self.simulation_loop)  # ~60 FPS
            
    def start_simulation(self):
        """Start the simulation"""
        self.create_targets()
        self.simulation_loop()
        
    def start_demo(self):
        """Start demo"""
        self.running = True
        self.simulation_loop()
        
    def pause_demo(self):
        """Pause demo"""
        self.running = False
        
    def reset_demo(self):
        """Reset demo"""
        self.crosshair_pos = [500, 350]
        self.sticky_aim.release_target()
        self.create_targets()
        
    def open_config(self):
        """Open configuration GUI"""
        try:
            from sticky_aim_config_gui import StickyAimConfigGUI
            config_gui = StickyAimConfigGUI()
            new_config = config_gui.run()
            if new_config:
                self.sticky_aim.config = new_config
        except ImportError:
            tk.messagebox.showerror("Error", "Configuration GUI not available")
            
    def run(self):
        """Run the demo"""
        print("🎯 Sticky Aim Demonstration")
        print("===========================")
        print("Watch the crosshair lock onto and track targets")
        print("Controls:")
        print("  ▶️ Start - Begin demonstration")
        print("  ⏸️ Pause - Pause simulation")
        print("  🔄 Reset - Reset to initial state")
        print("  ⚙️ Config - Open configuration")
        print("  Speed - Adjust simulation speed")
        
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """Handle window closing"""
        self.running = False
        self.root.destroy()

if __name__ == "__main__":
    demo = StickyAimDemo()
    demo.run()
