#!/usr/bin/env python3
"""
🎯 AIMBOT ENGINE - CORE SYSTEM
==============================
Main aimbot engine that orchestrates all components for sticky aim functionality.
"""

import time
import threading
import logging
from typing import Optional, List, Dict, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np

from ..detection.base_detector import BaseDetector, Target
from ..input.base_controller import BaseController
from .target_tracker import StickyTargetTracker
from .humanization import HumanizationEngine
from .performance import PerformanceMonitor
from ..utils.math_utils import calculate_distance, normalize_angle
from ..config.settings_manager import SettingsManager

class AimbotState(Enum):
    """Aimbot operational states"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    ERROR = "error"

@dataclass
class AimbotStats:
    """Aimbot performance statistics"""
    targets_detected: int = 0
    targets_locked: int = 0
    shots_fired: int = 0
    accuracy_percentage: float = 0.0
    average_lock_time: float = 0.0
    total_runtime: float = 0.0
    fps: float = 0.0
    last_update: float = field(default_factory=time.time)

class AimbotEngine:
    """
    Main aimbot engine that coordinates all subsystems.
    
    This class orchestrates:
    - Target detection (CV, memory, hybrid)
    - Sticky aim tracking
    - Input simulation (mouse, memory injection)
    - Humanization and anti-detection
    - Performance monitoring
    - Configuration management
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the aimbot engine"""
        self.logger = logging.getLogger(__name__)
        self.logger.info("🎯 Initializing Aimbot Engine")
        
        # Core state
        self.state = AimbotState.STOPPED
        self.running = False
        self.paused = False
        
        # Configuration
        self.settings = SettingsManager(config_path)
        self.config = self.settings.get_config()
        
        # Core components (initialized later)
        self.detector: Optional[BaseDetector] = None
        self.controller: Optional[BaseController] = None
        self.tracker: Optional[StickyTargetTracker] = None
        self.humanizer: Optional[HumanizationEngine] = None
        self.performance: Optional[PerformanceMonitor] = None
        
        # Threading
        self.main_thread: Optional[threading.Thread] = None
        self.thread_lock = threading.RLock()
        
        # Statistics
        self.stats = AimbotStats()
        self.start_time = 0.0
        
        # Callbacks
        self.callbacks: Dict[str, List[Callable]] = {
            'target_acquired': [],
            'target_lost': [],
            'shot_fired': [],
            'state_changed': [],
            'error_occurred': []
        }
        
        # Safety features
        self.emergency_stop = False
        self.max_runtime = self.config.get('safety', {}).get('max_runtime', 3600)  # 1 hour
        self.last_activity = time.time()
        
        self.logger.info("✅ Aimbot Engine initialized")
    
    def initialize_components(self):
        """Initialize all aimbot components"""
        try:
            self.logger.info("🔧 Initializing aimbot components...")
            
            # Initialize detector based on config
            detector_type = self.config.get('detection', {}).get('method', 'cv')
            self.detector = self._create_detector(detector_type)
            
            # Initialize controller based on config
            controller_type = self.config.get('input', {}).get('method', 'mouse')
            self.controller = self._create_controller(controller_type)
            
            # Initialize tracker
            tracker_config = self.config.get('tracking', {})
            self.tracker = StickyTargetTracker(tracker_config)
            
            # Initialize humanization
            humanization_config = self.config.get('humanization', {})
            self.humanizer = HumanizationEngine(humanization_config)
            
            # Initialize performance monitor
            self.performance = PerformanceMonitor()
            
            self.logger.info("✅ All components initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Component initialization failed: {e}")
            self.state = AimbotState.ERROR
            return False
    
    def _create_detector(self, detector_type: str) -> BaseDetector:
        """Create detector based on type"""
        if detector_type == 'cv':
            from ..detection.cv_detector import ComputerVisionDetector
            return ComputerVisionDetector(self.config.get('cv_detection', {}))
        elif detector_type == 'memory':
            from ..detection.memory_detector import MemoryDetector
            return MemoryDetector(self.config.get('memory_detection', {}))
        elif detector_type == 'hybrid':
            from ..detection.hybrid_detector import HybridDetector
            return HybridDetector(self.config.get('hybrid_detection', {}))
        else:
            raise ValueError(f"Unknown detector type: {detector_type}")
    
    def _create_controller(self, controller_type: str) -> BaseController:
        """Create input controller based on type"""
        if controller_type == 'mouse':
            from ..input.mouse_controller import MouseController
            return MouseController(self.config.get('mouse_control', {}))
        elif controller_type == 'memory':
            from ..input.memory_injector import MemoryInjector
            return MemoryInjector(self.config.get('memory_injection', {}))
        else:
            raise ValueError(f"Unknown controller type: {controller_type}")
    
    def start(self) -> bool:
        """Start the aimbot engine"""
        with self.thread_lock:
            if self.state in [AimbotState.RUNNING, AimbotState.STARTING]:
                self.logger.warning("⚠️ Aimbot is already running or starting")
                return False
            
            self.logger.info("🚀 Starting Aimbot Engine...")
            self.state = AimbotState.STARTING
            self._notify_callbacks('state_changed', self.state)
            
            # Initialize components if not already done
            if not self.detector or not self.controller:
                if not self.initialize_components():
                    return False
            
            # Reset statistics
            self.stats = AimbotStats()
            self.start_time = time.time()
            self.emergency_stop = False
            
            # Start main thread
            self.running = True
            self.main_thread = threading.Thread(target=self._main_loop, daemon=True)
            self.main_thread.start()
            
            self.state = AimbotState.RUNNING
            self._notify_callbacks('state_changed', self.state)
            self.logger.info("✅ Aimbot Engine started successfully")
            return True
    
    def stop(self) -> bool:
        """Stop the aimbot engine"""
        with self.thread_lock:
            if self.state == AimbotState.STOPPED:
                self.logger.warning("⚠️ Aimbot is already stopped")
                return False
            
            self.logger.info("🛑 Stopping Aimbot Engine...")
            self.state = AimbotState.STOPPING
            self._notify_callbacks('state_changed', self.state)
            
            # Signal stop
            self.running = False
            self.emergency_stop = True
            
            # Wait for main thread to finish
            if self.main_thread and self.main_thread.is_alive():
                self.main_thread.join(timeout=5.0)
                if self.main_thread.is_alive():
                    self.logger.warning("⚠️ Main thread did not stop gracefully")
            
            # Cleanup components
            self._cleanup_components()
            
            # Update statistics
            self.stats.total_runtime = time.time() - self.start_time
            
            self.state = AimbotState.STOPPED
            self._notify_callbacks('state_changed', self.state)
            self.logger.info("✅ Aimbot Engine stopped")
            return True
    
    def pause(self) -> bool:
        """Pause the aimbot engine"""
        with self.thread_lock:
            if self.state != AimbotState.RUNNING:
                return False
            
            self.paused = True
            self.state = AimbotState.PAUSED
            self._notify_callbacks('state_changed', self.state)
            self.logger.info("⏸️ Aimbot Engine paused")
            return True
    
    def resume(self) -> bool:
        """Resume the aimbot engine"""
        with self.thread_lock:
            if self.state != AimbotState.PAUSED:
                return False
            
            self.paused = False
            self.state = AimbotState.RUNNING
            self._notify_callbacks('state_changed', self.state)
            self.logger.info("▶️ Aimbot Engine resumed")
            return True
    
    def emergency_stop_now(self):
        """Immediate emergency stop"""
        self.logger.warning("🚨 EMERGENCY STOP ACTIVATED")
        self.emergency_stop = True
        self.running = False
        
        # Immediately release any locks or controls
        if self.tracker:
            self.tracker.release_all_targets()
        
        if self.controller:
            self.controller.reset()
    
    def _main_loop(self):
        """Main aimbot processing loop"""
        self.logger.info("🔄 Main aimbot loop started")
        frame_count = 0
        last_fps_time = time.time()
        
        try:
            while self.running and not self.emergency_stop:
                loop_start = time.time()
                
                # Check for pause
                if self.paused:
                    time.sleep(0.1)
                    continue
                
                # Safety timeout check
                if time.time() - self.start_time > self.max_runtime:
                    self.logger.warning("⚠️ Maximum runtime exceeded, stopping")
                    break
                
                # Main processing
                try:
                    self._process_frame()
                    frame_count += 1
                    
                    # Update FPS every second
                    current_time = time.time()
                    if current_time - last_fps_time >= 1.0:
                        self.stats.fps = frame_count / (current_time - last_fps_time)
                        frame_count = 0
                        last_fps_time = current_time
                    
                    # Performance monitoring
                    if self.performance:
                        loop_time = time.time() - loop_start
                        self.performance.record_frame_time(loop_time)
                    
                    # Target FPS limiting
                    target_fps = self.config.get('performance', {}).get('target_fps', 60)
                    target_frame_time = 1.0 / target_fps
                    elapsed = time.time() - loop_start
                    
                    if elapsed < target_frame_time:
                        time.sleep(target_frame_time - elapsed)
                
                except Exception as e:
                    self.logger.error(f"❌ Frame processing error: {e}")
                    self._notify_callbacks('error_occurred', e)
                    time.sleep(0.1)  # Prevent tight error loop
        
        except Exception as e:
            self.logger.error(f"❌ Main loop error: {e}")
            self.state = AimbotState.ERROR
            self._notify_callbacks('error_occurred', e)
        
        finally:
            self.logger.info("🔄 Main aimbot loop ended")
    
    def _process_frame(self):
        """Process a single frame of aimbot logic"""
        # Detect targets
        targets = self.detector.detect_targets()
        self.stats.targets_detected += len(targets)
        
        if not targets:
            # No targets found, release current lock if any
            if self.tracker.has_active_target():
                self.tracker.release_target()
                self._notify_callbacks('target_lost', None)
            return
        
        # Update tracker with new targets
        self.tracker.update_targets(targets)
        
        # Get current target (sticky aim logic)
        current_target = self.tracker.get_current_target()
        
        if current_target:
            # Calculate aim adjustment
            aim_adjustment = self.tracker.calculate_aim_adjustment()
            
            if aim_adjustment:
                # Apply humanization
                if self.humanizer:
                    aim_adjustment = self.humanizer.apply_humanization(aim_adjustment)
                
                # Execute aim movement
                if self.controller:
                    success = self.controller.apply_aim_adjustment(aim_adjustment)
                    
                    if success:
                        self.stats.targets_locked += 1
                        self.last_activity = time.time()
                        
                        # Check if this is a new target lock
                        if self.tracker.is_new_target_lock():
                            self._notify_callbacks('target_acquired', current_target)
    
    def _cleanup_components(self):
        """Cleanup all components"""
        try:
            if self.tracker:
                self.tracker.cleanup()
            
            if self.controller:
                self.controller.cleanup()
            
            if self.detector:
                self.detector.cleanup()
            
            if self.humanizer:
                self.humanizer.cleanup()
            
            if self.performance:
                self.performance.cleanup()
                
        except Exception as e:
            self.logger.error(f"❌ Cleanup error: {e}")
    
    def add_callback(self, event: str, callback: Callable):
        """Add event callback"""
        if event in self.callbacks:
            self.callbacks[event].append(callback)
    
    def remove_callback(self, event: str, callback: Callable):
        """Remove event callback"""
        if event in self.callbacks and callback in self.callbacks[event]:
            self.callbacks[event].remove(callback)
    
    def _notify_callbacks(self, event: str, data: Any):
        """Notify all callbacks for an event"""
        for callback in self.callbacks.get(event, []):
            try:
                callback(data)
            except Exception as e:
                self.logger.error(f"❌ Callback error for {event}: {e}")
    
    def get_stats(self) -> AimbotStats:
        """Get current statistics"""
        self.stats.last_update = time.time()
        if self.start_time > 0:
            self.stats.total_runtime = time.time() - self.start_time
        return self.stats
    
    def get_state(self) -> AimbotState:
        """Get current state"""
        return self.state
    
    def is_running(self) -> bool:
        """Check if aimbot is running"""
        return self.state == AimbotState.RUNNING
    
    def update_config(self, new_config: Dict[str, Any]):
        """Update configuration"""
        self.config.update(new_config)
        self.settings.save_config(self.config)
        
        # Notify components of config change
        if self.tracker:
            self.tracker.update_config(new_config.get('tracking', {}))
        
        if self.humanizer:
            self.humanizer.update_config(new_config.get('humanization', {}))
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        if self.is_running():
            self.stop()
