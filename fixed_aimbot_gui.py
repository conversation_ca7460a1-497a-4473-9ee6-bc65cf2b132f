#!/usr/bin/env python3
"""
🎛️ FIXED AIMBOT SETTINGS GUI
============================
Working version with proper activation buttons
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import subprocess
import sys

class FixedAimbotGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Aimbot Control Panel")
        self.root.geometry("600x500")
        self.root.configure(bg='#2b2b2b')
        
        # Aimbot process tracking
        self.aimbot_process = None
        self.aimbot_running = False
        
        # Settings
        self.settings = {
            'fov_size': 400,
            'sensitivity': 0.8,
            'confidence_threshold': 0.6,
            'smoothing': 0.8,
            'show_fov': True,
            'show_targets': True,
            'show_crosshair': True,
            'show_stats': True,
            'activation_key': 'F1'
        }
        
        self.create_widgets()
        self.update_status()
        
    def create_widgets(self):
        """Create the GUI widgets"""
        # Title
        title_label = tk.Label(self.root, text="🎯 AIMBOT CONTROL PANEL", 
                              font=('Arial', 18, 'bold'), 
                              bg='#2b2b2b', fg='white')
        title_label.pack(pady=20)
        
        # Status frame
        status_frame = tk.LabelFrame(self.root, text="📊 Status", 
                                   bg='#2b2b2b', fg='white', font=('Arial', 12, 'bold'))
        status_frame.pack(pady=10, padx=20, fill='x')
        
        self.status_label = tk.Label(status_frame, text="💤 AIMBOT: INACTIVE", 
                                   font=('Arial', 14, 'bold'), 
                                   bg='#2b2b2b', fg='red')
        self.status_label.pack(pady=10)
        
        # Control buttons frame
        control_frame = tk.LabelFrame(self.root, text="🎮 Aimbot Control", 
                                    bg='#2b2b2b', fg='white', font=('Arial', 12, 'bold'))
        control_frame.pack(pady=10, padx=20, fill='x')
        
        button_frame = tk.Frame(control_frame, bg='#2b2b2b')
        button_frame.pack(pady=15)
        
        # Start button
        self.start_btn = tk.Button(button_frame, text="🚀 START AIMBOT", 
                                  font=('Arial', 12, 'bold'),
                                  bg='#4CAF50', fg='white', width=15, height=2,
                                  command=self.start_aimbot)
        self.start_btn.grid(row=0, column=0, padx=10)
        
        # Stop button  
        self.stop_btn = tk.Button(button_frame, text="⏹️ STOP AIMBOT", 
                                 font=('Arial', 12, 'bold'),
                                 bg='#f44336', fg='white', width=15, height=2,
                                 command=self.stop_aimbot)
        self.stop_btn.grid(row=0, column=1, padx=10)
        
        # Toggle button
        self.toggle_btn = tk.Button(button_frame, text="🔄 TOGGLE (F1)", 
                                   font=('Arial', 12, 'bold'),
                                   bg='#2196F3', fg='white', width=15, height=2,
                                   command=self.toggle_aimbot)
        self.toggle_btn.grid(row=0, column=2, padx=10)
        
        # Settings frame
        settings_frame = tk.LabelFrame(self.root, text="⚙️ Quick Settings", 
                                     bg='#2b2b2b', fg='white', font=('Arial', 12, 'bold'))
        settings_frame.pack(pady=10, padx=20, fill='x')
        
        # FOV setting
        fov_frame = tk.Frame(settings_frame, bg='#2b2b2b')
        fov_frame.pack(pady=5, fill='x')
        
        tk.Label(fov_frame, text="🔵 FOV Size:", bg='#2b2b2b', fg='white', 
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.fov_var = tk.IntVar(value=400)
        fov_scale = tk.Scale(fov_frame, from_=200, to=800, orient='horizontal',
                           variable=self.fov_var, bg='#2b2b2b', fg='white',
                           highlightbackground='#2b2b2b')
        fov_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Sensitivity setting
        sens_frame = tk.Frame(settings_frame, bg='#2b2b2b')
        sens_frame.pack(pady=5, fill='x')
        
        tk.Label(sens_frame, text="🎯 Sensitivity:", bg='#2b2b2b', fg='white',
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.sens_var = tk.DoubleVar(value=0.8)
        sens_scale = tk.Scale(sens_frame, from_=0.1, to=2.0, resolution=0.1,
                            orient='horizontal', variable=self.sens_var,
                            bg='#2b2b2b', fg='white', highlightbackground='#2b2b2b')
        sens_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Info frame
        info_frame = tk.LabelFrame(self.root, text="ℹ️ Instructions", 
                                 bg='#2b2b2b', fg='white', font=('Arial', 12, 'bold'))
        info_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        info_text = """
🎯 How to use:
• Click START AIMBOT to launch the visual aimbot
• Press F1 in-game to activate/deactivate aiming
• Visual overlay shows targets even when inactive
• Click STOP to completely close the aimbot

⚠️ Important:
• Aimbot starts INACTIVE for safety
• You have full mouse control until F1 is pressed
• Works best in windowed/borderless windowed mode
        """
        
        info_label = tk.Label(info_frame, text=info_text, bg='#2b2b2b', fg='lightgray',
                            font=('Arial', 9), justify='left')
        info_label.pack(pady=10, padx=10)
        
    def start_aimbot(self):
        """Start the aimbot process"""
        if self.aimbot_process and self.aimbot_process.poll() is None:
            messagebox.showinfo("Already Running", "Aimbot is already running!")
            return
            
        try:
            # Try to launch visual aimbot
            if os.path.exists('visual_aimbot_overlay.py'):
                self.aimbot_process = subprocess.Popen([sys.executable, 'visual_aimbot_overlay.py'])
                self.aimbot_running = True
                messagebox.showinfo("Aimbot Started", 
                                  "🚀 Visual Aimbot launched!\n\n" +
                                  "• Look for the overlay window\n" +
                                  "• Press F1 to activate aiming\n" +
                                  "• Aimbot starts INACTIVE for safety")
            elif os.path.exists('fixed_visual_aimbot.py'):
                self.aimbot_process = subprocess.Popen([sys.executable, 'fixed_visual_aimbot.py'])
                self.aimbot_running = True
                messagebox.showinfo("Aimbot Started", 
                                  "🚀 Fixed Visual Aimbot launched!\n\n" +
                                  "• Look for the overlay window\n" +
                                  "• Press F1 to activate aiming")
            else:
                messagebox.showerror("File Not Found", 
                                   "Could not find aimbot files!\n\n" +
                                   "Make sure visual_aimbot_overlay.py exists.")
                return
                
        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to start aimbot:\n{str(e)}")
            
    def stop_aimbot(self):
        """Stop the aimbot process"""
        if self.aimbot_process:
            try:
                self.aimbot_process.terminate()
                self.aimbot_process = None
                self.aimbot_running = False
                messagebox.showinfo("Aimbot Stopped", "⏹️ Aimbot has been stopped")
            except Exception as e:
                messagebox.showerror("Stop Error", f"Error stopping aimbot:\n{str(e)}")
        else:
            messagebox.showwarning("Not Running", "No aimbot process to stop")
            
    def toggle_aimbot(self):
        """Toggle aimbot state"""
        if self.aimbot_running:
            self.stop_aimbot()
        else:
            self.start_aimbot()
            
    def update_status(self):
        """Update the status display"""
        if self.aimbot_process and self.aimbot_process.poll() is None:
            self.status_label.config(text="🔥 AIMBOT: RUNNING", fg='green')
            self.aimbot_running = True
        else:
            self.status_label.config(text="💤 AIMBOT: INACTIVE", fg='red')
            self.aimbot_running = False
            
        # Schedule next update
        self.root.after(1000, self.update_status)
        
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🎛️ Launching Fixed Aimbot GUI...")
    app = FixedAimbotGUI()
    app.run()
