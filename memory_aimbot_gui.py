#!/usr/bin/env python3
"""
🎯 MEMORY INJECTION AIMBOT GUI
=============================
Control panel for memory-based aimbot
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
import psutil
import threading
import time

class MemoryAimbotGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Memory Injection Aimbot")
        self.root.geometry("600x700")
        self.root.configure(bg='#1a0d1a')
        
        # Process tracking
        self.aimbot_process = None
        self.aimbot_running = False
        self.selected_process = None
        
        # Settings
        self.fov_var = tk.DoubleVar(value=90.0)
        self.smooth_var = tk.DoubleVar(value=2.0)
        self.process_var = tk.StringVar()
        
        self.create_widgets()
        self.update_status()
        self.refresh_processes()
        
    def create_widgets(self):
        """Create GUI widgets"""
        # Title
        title_label = tk.Label(self.root, text="🎯 MEMORY INJECTION AIMBOT", 
                              font=('Arial', 18, 'bold'), 
                              bg='#1a0d1a', fg='white')
        title_label.pack(pady=20)
        
        # Description
        desc_label = tk.Label(self.root, 
                            text="Direct memory access for maximum performance\nReads game memory and injects aim data", 
                            font=('Arial', 10), 
                            bg='#1a0d1a', fg='lightgray')
        desc_label.pack(pady=10)
        
        # Process selection frame
        process_frame = tk.LabelFrame(self.root, text="🎮 Target Process", 
                                    bg='#1a0d1a', fg='white', font=('Arial', 12, 'bold'))
        process_frame.pack(pady=10, padx=20, fill='x')
        
        # Process dropdown
        process_select_frame = tk.Frame(process_frame, bg='#1a0d1a')
        process_select_frame.pack(pady=10, fill='x')
        
        tk.Label(process_select_frame, text="Select Game Process:", 
                bg='#1a0d1a', fg='white', font=('Arial', 10)).pack(side='left', padx=10)
        
        self.process_combo = ttk.Combobox(process_select_frame, textvariable=self.process_var,
                                         state='readonly', width=30)
        self.process_combo.pack(side='left', padx=10, fill='x', expand=True)
        
        refresh_btn = tk.Button(process_select_frame, text="🔄 Refresh", 
                               bg='#4a4a4a', fg='white', command=self.refresh_processes)
        refresh_btn.pack(side='right', padx=10)
        
        # Status frame
        status_frame = tk.LabelFrame(self.root, text="📊 Status", 
                                   bg='#1a0d1a', fg='white', font=('Arial', 12, 'bold'))
        status_frame.pack(pady=10, padx=20, fill='x')
        
        self.status_label = tk.Label(status_frame, text="💤 MEMORY AIMBOT: INACTIVE", 
                                   font=('Arial', 14, 'bold'), 
                                   bg='#1a0d1a', fg='red')
        self.status_label.pack(pady=10)
        
        # Control buttons
        control_frame = tk.LabelFrame(self.root, text="🎮 Control", 
                                    bg='#1a0d1a', fg='white', font=('Arial', 12, 'bold'))
        control_frame.pack(pady=10, padx=20, fill='x')
        
        button_frame = tk.Frame(control_frame, bg='#1a0d1a')
        button_frame.pack(pady=15)
        
        # Start button
        self.start_btn = tk.Button(button_frame, text="🚀 START MEMORY AIMBOT", 
                                  font=('Arial', 12, 'bold'),
                                  bg='#8b0000', fg='white', width=20, height=2,
                                  command=self.start_aimbot)
        self.start_btn.grid(row=0, column=0, padx=5)
        
        # Stop button
        self.stop_btn = tk.Button(button_frame, text="⏹️ STOP AIMBOT", 
                                 font=('Arial', 12, 'bold'),
                                 bg='#4a4a4a', fg='white', width=20, height=2,
                                 command=self.stop_aimbot)
        self.stop_btn.grid(row=0, column=1, padx=5)
        
        # Settings frame
        settings_frame = tk.LabelFrame(self.root, text="⚙️ Memory Settings", 
                                     bg='#1a0d1a', fg='white', font=('Arial', 12, 'bold'))
        settings_frame.pack(pady=10, padx=20, fill='x')
        
        # FOV Setting
        fov_frame = tk.Frame(settings_frame, bg='#1a0d1a')
        fov_frame.pack(pady=5, fill='x')
        
        tk.Label(fov_frame, text="🎯 FOV (degrees):", bg='#1a0d1a', fg='white', 
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.fov_label = tk.Label(fov_frame, text="90.0", bg='#1a0d1a', fg='#ff6b6b',
                                 font=('Arial', 10, 'bold'))
        self.fov_label.pack(side='right', padx=10)
        
        fov_scale = tk.Scale(fov_frame, from_=30.0, to=180.0, resolution=5.0, orient='horizontal',
                           variable=self.fov_var, bg='#1a0d1a', fg='white',
                           highlightbackground='#1a0d1a', command=self.update_fov_label)
        fov_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Smoothing Setting
        smooth_frame = tk.Frame(settings_frame, bg='#1a0d1a')
        smooth_frame.pack(pady=5, fill='x')
        
        tk.Label(smooth_frame, text="🌊 Smoothing:", bg='#1a0d1a', fg='white',
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.smooth_label = tk.Label(smooth_frame, text="2.0", bg='#1a0d1a', fg='#ff6b6b',
                                    font=('Arial', 10, 'bold'))
        self.smooth_label.pack(side='right', padx=10)
        
        smooth_scale = tk.Scale(smooth_frame, from_=1.0, to=10.0, resolution=0.5,
                              orient='horizontal', variable=self.smooth_var,
                              bg='#1a0d1a', fg='white', highlightbackground='#1a0d1a',
                              command=self.update_smooth_label)
        smooth_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Instructions frame
        info_frame = tk.LabelFrame(self.root, text="⚠️ IMPORTANT", 
                                 bg='#1a0d1a', fg='red', font=('Arial', 12, 'bold'))
        info_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        info_text = """
🎯 Memory Injection Features:
• Direct memory access for maximum speed
• Reads player positions from game memory
• Injects aim angles directly into game
• No screen capture needed - pure memory operations

⚠️ CRITICAL WARNINGS:
• This is for EDUCATIONAL PURPOSES ONLY
• Memory injection may trigger anti-cheat systems
• Use only in offline/practice modes
• Game offsets need to be updated for each game
• Requires administrator privileges

🎮 Controls (when running):
• F1: Toggle aimbot ON/OFF
• F2: Quit aimbot

🔧 Technical Notes:
• Offsets are game-specific and need updating
• Works by reading player data structures
• Injects view angles into local player memory
• High-frequency updates (1000+ FPS possible)
        """
        
        info_label = tk.Label(info_frame, text=info_text, bg='#1a0d1a', fg='lightgray',
                            font=('Arial', 9), justify='left')
        info_label.pack(pady=10, padx=10)
        
    def update_fov_label(self, value):
        self.fov_label.config(text=f"{float(value):.1f}")
        
    def update_smooth_label(self, value):
        self.smooth_label.config(text=f"{float(value):.1f}")
    
    def refresh_processes(self):
        """Refresh the list of running processes"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    name = proc.info['name']
                    if name.endswith('.exe') and 'python' not in name.lower():
                        processes.append(f"{name} (PID: {proc.info['pid']})")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            processes.sort()
            self.process_combo['values'] = processes
            
            if processes:
                self.process_combo.set(processes[0])
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh processes:\n{str(e)}")
    
    def start_aimbot(self):
        """Start the memory injection aimbot"""
        if self.aimbot_process and self.aimbot_process.poll() is None:
            messagebox.showinfo("Already Running", "Memory Aimbot is already running!")
            return
        
        selected = self.process_var.get()
        if not selected:
            messagebox.showerror("No Process Selected", "Please select a target process first!")
            return
            
        try:
            # Extract process name from selection
            process_name = selected.split(' (PID:')[0]
            
            # Check if memory aimbot file exists
            if not os.path.exists('memory_injection_aimbot.py'):
                messagebox.showerror("File Not Found", 
                                   "memory_injection_aimbot.py not found!\n\n" +
                                   "Make sure the memory aimbot file exists.")
                return
            
            # Show warning dialog
            warning = messagebox.askyesno("⚠️ WARNING", 
                                        f"You are about to inject into: {process_name}\n\n" +
                                        "⚠️ WARNINGS:\n" +
                                        "• This may trigger anti-cheat systems\n" +
                                        "• Use only for educational purposes\n" +
                                        "• Requires administrator privileges\n" +
                                        "• Game offsets may need updating\n\n" +
                                        "Continue anyway?")
            
            if not warning:
                return
            
            # Start the memory aimbot process
            # Note: In a real implementation, you'd pass the process name as an argument
            self.aimbot_process = subprocess.Popen([sys.executable, 'memory_injection_aimbot.py'])
            self.aimbot_running = True
            
            messagebox.showinfo("Memory Aimbot Started", 
                              f"🚀 Memory Injection Aimbot launched!\n\n" +
                              f"🎯 Target: {process_name}\n" +
                              f"🎮 FOV: {self.fov_var.get():.1f}°\n" +
                              f"🌊 Smoothing: {self.smooth_var.get():.1f}\n\n" +
                              "🎮 Controls:\n" +
                              "• F1: Toggle aimbot ON/OFF\n" +
                              "• F2: Quit aimbot\n\n" +
                              "⚠️ Aimbot starts INACTIVE - Press F1 to activate!")
                              
        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to start memory aimbot:\n{str(e)}")
    
    def stop_aimbot(self):
        """Stop the memory injection aimbot"""
        if self.aimbot_process:
            try:
                self.aimbot_process.terminate()
                self.aimbot_process = None
                self.aimbot_running = False
                messagebox.showinfo("Aimbot Stopped", "⏹️ Memory Aimbot has been stopped")
            except Exception as e:
                messagebox.showerror("Stop Error", f"Error stopping aimbot:\n{str(e)}")
        else:
            messagebox.showwarning("Not Running", "No memory aimbot process to stop")
    
    def update_status(self):
        """Update status display"""
        if self.aimbot_process and self.aimbot_process.poll() is None:
            self.status_label.config(text="🔥 MEMORY AIMBOT: RUNNING", fg='#8b0000')
            self.aimbot_running = True
        else:
            self.status_label.config(text="💤 MEMORY AIMBOT: INACTIVE", fg='#4a4a4a')
            self.aimbot_running = False
            
        # Schedule next update
        self.root.after(1000, self.update_status)
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🎯 Launching Memory Injection Aimbot GUI...")
    app = MemoryAimbotGUI()
    app.run()
