#!/usr/bin/env python3
"""
🎯 STICKY AIM CONFIGURATION GUI
===============================
Advanced configuration interface for sticky aim system
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from sticky_aim_system import StickyAimConfig

class StickyAimConfigGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Sticky Aim Configuration")
        self.root.geometry("800x900")
        self.root.configure(bg='#1a0d1a')
        
        # Configuration object
        self.config = StickyAimConfig()
        
        # GUI variables
        self.setup_variables()
        
        # Create GUI
        self.create_widgets()
        
        # Load saved config
        self.load_config()
        
    def setup_variables(self):
        """Setup tkinter variables for configuration"""
        # Target selection
        self.max_lock_distance = tk.DoubleVar(value=self.config.max_lock_distance)
        self.max_lock_fov = tk.DoubleVar(value=self.config.max_lock_fov)
        self.target_switch_delay = tk.DoubleVar(value=self.config.target_switch_delay)
        
        # Tracking behavior
        self.lock_strength = tk.DoubleVar(value=self.config.lock_strength)
        self.smoothing_factor = tk.DoubleVar(value=self.config.smoothing_factor)
        self.prediction_enabled = tk.BooleanVar(value=self.config.prediction_enabled)
        self.prediction_time = tk.DoubleVar(value=self.config.prediction_time)
        
        # Humanization
        self.micro_adjustments = tk.BooleanVar(value=self.config.micro_adjustments)
        self.random_jitter = tk.DoubleVar(value=self.config.random_jitter)
        self.fatigue_simulation = tk.BooleanVar(value=self.config.fatigue_simulation)
        self.reaction_delay = tk.DoubleVar(value=self.config.reaction_delay)
        
        # Persistence
        self.target_lost_timeout = tk.DoubleVar(value=self.config.target_lost_timeout)
        self.reacquisition_time = tk.DoubleVar(value=self.config.reacquisition_time)
        
        # Aim zones
        self.preferred_bone = tk.StringVar(value=self.config.preferred_bone)
        self.bone_switch_chance = tk.DoubleVar(value=self.config.bone_switch_chance)
        
        # Anti-detection
        self.detection_avoidance = tk.BooleanVar(value=self.config.detection_avoidance)
        self.max_snap_speed = tk.DoubleVar(value=self.config.max_snap_speed)
        self.natural_drift = tk.BooleanVar(value=self.config.natural_drift)
        self.drift_amount = tk.DoubleVar(value=self.config.drift_amount)
        
    def create_widgets(self):
        """Create GUI widgets"""
        # Title
        title_label = tk.Label(self.root, text="🎯 STICKY AIM CONFIGURATION", 
                              font=('Arial', 18, 'bold'), 
                              bg='#1a0d1a', fg='white')
        title_label.pack(pady=20)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Create tabs
        self.create_targeting_tab(notebook)
        self.create_tracking_tab(notebook)
        self.create_humanization_tab(notebook)
        self.create_persistence_tab(notebook)
        self.create_detection_tab(notebook)
        
        # Control buttons
        self.create_control_buttons()
        
    def create_targeting_tab(self, notebook):
        """Create target selection tab"""
        frame = ttk.Frame(notebook, padding="20")
        notebook.add(frame, text="🎯 Target Selection")
        
        # Max lock distance
        self.create_slider(frame, "🔍 Max Lock Distance", self.max_lock_distance, 
                          100, 1000, "Maximum distance to acquire targets")
        
        # Max lock FOV
        self.create_slider(frame, "👁️ Max Lock FOV", self.max_lock_fov, 
                          10, 180, "Maximum field of view for target acquisition")
        
        # Target switch delay
        self.create_slider(frame, "⏱️ Target Switch Delay", self.target_switch_delay, 
                          0.0, 2.0, "Minimum time between target switches", resolution=0.1)
        
    def create_tracking_tab(self, notebook):
        """Create tracking behavior tab"""
        frame = ttk.Frame(notebook, padding="20")
        notebook.add(frame, text="🎮 Tracking")
        
        # Lock strength
        self.create_slider(frame, "💪 Lock Strength", self.lock_strength, 
                          0.0, 1.0, "How strongly the aim locks to target", resolution=0.1)
        
        # Smoothing factor
        self.create_slider(frame, "🌊 Smoothing Factor", self.smoothing_factor, 
                          1.0, 10.0, "Higher values = smoother movement", resolution=0.5)
        
        # Prediction enabled
        self.create_checkbox(frame, "🔮 Target Prediction", self.prediction_enabled,
                           "Predict target movement for better tracking")
        
        # Prediction time
        self.create_slider(frame, "⏰ Prediction Time", self.prediction_time, 
                          0.0, 0.5, "How far ahead to predict (seconds)", resolution=0.01)
        
    def create_humanization_tab(self, notebook):
        """Create humanization tab"""
        frame = ttk.Frame(notebook, padding="20")
        notebook.add(frame, text="🤖 Humanization")
        
        # Micro adjustments
        self.create_checkbox(frame, "🎯 Micro Adjustments", self.micro_adjustments,
                           "Add small random movements for realism")
        
        # Random jitter
        self.create_slider(frame, "📳 Random Jitter", self.random_jitter, 
                          0.0, 0.1, "Amount of random movement", resolution=0.001)
        
        # Fatigue simulation
        self.create_checkbox(frame, "😴 Fatigue Simulation", self.fatigue_simulation,
                           "Simulate aim degradation over time")
        
        # Reaction delay
        self.create_slider(frame, "⚡ Reaction Delay", self.reaction_delay, 
                          0.0, 0.2, "Human reaction time simulation", resolution=0.01)
        
    def create_persistence_tab(self, notebook):
        """Create persistence tab"""
        frame = ttk.Frame(notebook, padding="20")
        notebook.add(frame, text="🔒 Persistence")
        
        # Target lost timeout
        self.create_slider(frame, "⏰ Target Lost Timeout", self.target_lost_timeout, 
                          0.5, 5.0, "Time before switching to new target", resolution=0.1)
        
        # Reacquisition time
        self.create_slider(frame, "🔄 Reacquisition Time", self.reacquisition_time, 
                          0.1, 2.0, "Time to reacquire lost target", resolution=0.1)
        
        # Preferred bone
        bone_frame = tk.Frame(frame, bg='#f0f0f0')
        bone_frame.pack(fill='x', pady=10)
        
        tk.Label(bone_frame, text="🎯 Preferred Aim Point:", font=('Arial', 10, 'bold')).pack(anchor='w')
        bone_combo = ttk.Combobox(bone_frame, textvariable=self.preferred_bone,
                                 values=['head', 'chest', 'center'], state='readonly')
        bone_combo.pack(fill='x', pady=5)
        
        # Bone switch chance
        self.create_slider(frame, "🎲 Aim Point Switch Chance", self.bone_switch_chance, 
                          0.0, 1.0, "Chance to switch aim point", resolution=0.1)
        
    def create_detection_tab(self, notebook):
        """Create anti-detection tab"""
        frame = ttk.Frame(notebook, padding="20")
        notebook.add(frame, text="🛡️ Anti-Detection")
        
        # Detection avoidance
        self.create_checkbox(frame, "🛡️ Detection Avoidance", self.detection_avoidance,
                           "Enable anti-detection measures")
        
        # Max snap speed
        self.create_slider(frame, "⚡ Max Snap Speed", self.max_snap_speed, 
                          30, 360, "Maximum degrees per second movement")
        
        # Natural drift
        self.create_checkbox(frame, "🌊 Natural Drift", self.natural_drift,
                           "Add natural aim drift for realism")
        
        # Drift amount
        self.create_slider(frame, "📊 Drift Amount", self.drift_amount, 
                          0.0, 2.0, "Amount of natural drift", resolution=0.1)
        
    def create_slider(self, parent, label, variable, min_val, max_val, description, resolution=1):
        """Create a labeled slider"""
        frame = tk.Frame(parent, bg='#f0f0f0')
        frame.pack(fill='x', pady=10)
        
        # Label and value
        label_frame = tk.Frame(frame, bg='#f0f0f0')
        label_frame.pack(fill='x')
        
        tk.Label(label_frame, text=label, font=('Arial', 10, 'bold'), bg='#f0f0f0').pack(side='left')
        value_label = tk.Label(label_frame, text=f"{variable.get():.3f}", 
                              font=('Arial', 10, 'bold'), fg='blue', bg='#f0f0f0')
        value_label.pack(side='right')
        
        # Slider
        slider = tk.Scale(frame, from_=min_val, to=max_val, resolution=resolution,
                         orient='horizontal', variable=variable, bg='#f0f0f0')
        slider.pack(fill='x', pady=5)
        
        # Update value label when slider changes
        def update_label(*args):
            value_label.config(text=f"{variable.get():.3f}")
        variable.trace('w', update_label)
        
        # Description
        tk.Label(frame, text=description, font=('Arial', 8), 
                fg='gray', bg='#f0f0f0').pack(anchor='w')
        
    def create_checkbox(self, parent, label, variable, description):
        """Create a labeled checkbox"""
        frame = tk.Frame(parent, bg='#f0f0f0')
        frame.pack(fill='x', pady=10)
        
        tk.Checkbutton(frame, text=label, variable=variable, 
                      font=('Arial', 10, 'bold'), bg='#f0f0f0').pack(anchor='w')
        
        tk.Label(frame, text=description, font=('Arial', 8), 
                fg='gray', bg='#f0f0f0').pack(anchor='w', padx=20)
        
    def create_control_buttons(self):
        """Create control buttons"""
        button_frame = tk.Frame(self.root, bg='#1a0d1a')
        button_frame.pack(pady=20)
        
        tk.Button(button_frame, text="💾 Save Config", 
                 font=('Arial', 12, 'bold'),
                 bg='#4CAF50', fg='white', width=15, height=2,
                 command=self.save_config).pack(side='left', padx=10)
        
        tk.Button(button_frame, text="📁 Load Config", 
                 font=('Arial', 12, 'bold'),
                 bg='#2196F3', fg='white', width=15, height=2,
                 command=self.load_config).pack(side='left', padx=10)
        
        tk.Button(button_frame, text="🔄 Reset Defaults", 
                 font=('Arial', 12, 'bold'),
                 bg='#FF9800', fg='white', width=15, height=2,
                 command=self.reset_defaults).pack(side='left', padx=10)
        
        tk.Button(button_frame, text="✅ Apply & Close", 
                 font=('Arial', 12, 'bold'),
                 bg='#9C27B0', fg='white', width=15, height=2,
                 command=self.apply_and_close).pack(side='left', padx=10)
        
    def update_config(self):
        """Update config object from GUI values"""
        self.config.max_lock_distance = self.max_lock_distance.get()
        self.config.max_lock_fov = self.max_lock_fov.get()
        self.config.target_switch_delay = self.target_switch_delay.get()
        
        self.config.lock_strength = self.lock_strength.get()
        self.config.smoothing_factor = self.smoothing_factor.get()
        self.config.prediction_enabled = self.prediction_enabled.get()
        self.config.prediction_time = self.prediction_time.get()
        
        self.config.micro_adjustments = self.micro_adjustments.get()
        self.config.random_jitter = self.random_jitter.get()
        self.config.fatigue_simulation = self.fatigue_simulation.get()
        self.config.reaction_delay = self.reaction_delay.get()
        
        self.config.target_lost_timeout = self.target_lost_timeout.get()
        self.config.reacquisition_time = self.reacquisition_time.get()
        self.config.preferred_bone = self.preferred_bone.get()
        self.config.bone_switch_chance = self.bone_switch_chance.get()
        
        self.config.detection_avoidance = self.detection_avoidance.get()
        self.config.max_snap_speed = self.max_snap_speed.get()
        self.config.natural_drift = self.natural_drift.get()
        self.config.drift_amount = self.drift_amount.get()
        
    def save_config(self):
        """Save configuration to file"""
        try:
            self.update_config()
            
            config_dict = {
                'max_lock_distance': self.config.max_lock_distance,
                'max_lock_fov': self.config.max_lock_fov,
                'target_switch_delay': self.config.target_switch_delay,
                'lock_strength': self.config.lock_strength,
                'smoothing_factor': self.config.smoothing_factor,
                'prediction_enabled': self.config.prediction_enabled,
                'prediction_time': self.config.prediction_time,
                'micro_adjustments': self.config.micro_adjustments,
                'random_jitter': self.config.random_jitter,
                'fatigue_simulation': self.config.fatigue_simulation,
                'reaction_delay': self.config.reaction_delay,
                'target_lost_timeout': self.config.target_lost_timeout,
                'reacquisition_time': self.config.reacquisition_time,
                'preferred_bone': self.config.preferred_bone,
                'bone_switch_chance': self.config.bone_switch_chance,
                'detection_avoidance': self.config.detection_avoidance,
                'max_snap_speed': self.config.max_snap_speed,
                'natural_drift': self.config.natural_drift,
                'drift_amount': self.config.drift_amount
            }
            
            with open('sticky_aim_config.json', 'w') as f:
                json.dump(config_dict, f, indent=2)
            
            messagebox.showinfo("Saved", "🎯 Sticky aim configuration saved!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save config:\n{str(e)}")
            
    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists('sticky_aim_config.json'):
                with open('sticky_aim_config.json', 'r') as f:
                    config_dict = json.load(f)
                
                # Update variables
                self.max_lock_distance.set(config_dict.get('max_lock_distance', 500.0))
                self.max_lock_fov.set(config_dict.get('max_lock_fov', 45.0))
                self.target_switch_delay.set(config_dict.get('target_switch_delay', 0.2))
                
                self.lock_strength.set(config_dict.get('lock_strength', 0.8))
                self.smoothing_factor.set(config_dict.get('smoothing_factor', 3.0))
                self.prediction_enabled.set(config_dict.get('prediction_enabled', True))
                self.prediction_time.set(config_dict.get('prediction_time', 0.1))
                
                self.micro_adjustments.set(config_dict.get('micro_adjustments', True))
                self.random_jitter.set(config_dict.get('random_jitter', 0.02))
                self.fatigue_simulation.set(config_dict.get('fatigue_simulation', True))
                self.reaction_delay.set(config_dict.get('reaction_delay', 0.05))
                
                self.target_lost_timeout.set(config_dict.get('target_lost_timeout', 1.0))
                self.reacquisition_time.set(config_dict.get('reacquisition_time', 0.3))
                self.preferred_bone.set(config_dict.get('preferred_bone', 'head'))
                self.bone_switch_chance.set(config_dict.get('bone_switch_chance', 0.1))
                
                self.detection_avoidance.set(config_dict.get('detection_avoidance', True))
                self.max_snap_speed.set(config_dict.get('max_snap_speed', 180.0))
                self.natural_drift.set(config_dict.get('natural_drift', True))
                self.drift_amount.set(config_dict.get('drift_amount', 0.5))
                
                messagebox.showinfo("Loaded", "🎯 Sticky aim configuration loaded!")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load config:\n{str(e)}")
            
    def reset_defaults(self):
        """Reset to default configuration"""
        default_config = StickyAimConfig()
        
        self.max_lock_distance.set(default_config.max_lock_distance)
        self.max_lock_fov.set(default_config.max_lock_fov)
        self.target_switch_delay.set(default_config.target_switch_delay)
        
        self.lock_strength.set(default_config.lock_strength)
        self.smoothing_factor.set(default_config.smoothing_factor)
        self.prediction_enabled.set(default_config.prediction_enabled)
        self.prediction_time.set(default_config.prediction_time)
        
        self.micro_adjustments.set(default_config.micro_adjustments)
        self.random_jitter.set(default_config.random_jitter)
        self.fatigue_simulation.set(default_config.fatigue_simulation)
        self.reaction_delay.set(default_config.reaction_delay)
        
        self.target_lost_timeout.set(default_config.target_lost_timeout)
        self.reacquisition_time.set(default_config.reacquisition_time)
        self.preferred_bone.set(default_config.preferred_bone)
        self.bone_switch_chance.set(default_config.bone_switch_chance)
        
        self.detection_avoidance.set(default_config.detection_avoidance)
        self.max_snap_speed.set(default_config.max_snap_speed)
        self.natural_drift.set(default_config.natural_drift)
        self.drift_amount.set(default_config.drift_amount)
        
        messagebox.showinfo("Reset", "🎯 Configuration reset to defaults!")
        
    def apply_and_close(self):
        """Apply configuration and close"""
        self.update_config()
        self.save_config()
        self.root.destroy()
        
    def run(self):
        """Run the configuration GUI"""
        self.root.mainloop()
        return self.config

if __name__ == "__main__":
    print("🎯 Sticky Aim Configuration GUI")
    gui = StickyAimConfigGUI()
    config = gui.run()
    print("Configuration completed!")
