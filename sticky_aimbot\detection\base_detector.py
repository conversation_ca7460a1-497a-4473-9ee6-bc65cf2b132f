#!/usr/bin/env python3
"""
🔍 BASE DETECTOR
================
Base class and data structures for target detection systems.
"""

import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Optional, Dict, Any

@dataclass
class Target:
    """Target data structure"""
    id: str
    x: float
    y: float
    z: float
    width: float = 50.0
    height: float = 50.0
    confidence: float = 1.0
    detection_method: str = "unknown"
    class_name: str = "target"
    timestamp: float = 0.0
    health: float = 100.0
    team: int = 0
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()

class BaseDetector(ABC):
    """Base class for all target detection systems"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the detector"""
        self.config = config or {}
        self.enabled = True
        self.last_detection_time = 0.0
        self.detection_count = 0
    
    @abstractmethod
    def detect_targets(self) -> List[Target]:
        """Detect targets and return list of Target objects"""
        pass
    
    def is_enabled(self) -> bool:
        """Check if detector is enabled"""
        return self.enabled
    
    def enable(self):
        """Enable the detector"""
        self.enabled = True
    
    def disable(self):
        """Disable the detector"""
        self.enabled = False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get detector statistics"""
        return {
            'enabled': self.enabled,
            'detection_count': self.detection_count,
            'last_detection_time': self.last_detection_time
        }
    
    def cleanup(self):
        """Cleanup detector resources"""
        pass
