#!/usr/bin/env python3
"""
🔍 MEMORY AIMBOT VERIFICATION LAUNCHER
=====================================
Launch all verification and debugging tools for memory injection aimbot
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
import os
import threading
import time

class MemoryVerificationLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 Memory Aimbot Verification Suite")
        self.root.geometry("600x500")
        self.root.configure(bg='#0a0a0a')
        
        # Track launched processes
        self.processes = {}
        self.status_labels = {}

        self.create_widgets()
        
    def create_widgets(self):
        """Create launcher GUI"""
        # Title
        title_label = tk.Label(self.root, text="🔍 MEMORY AIMBOT VERIFICATION SUITE", 
                              font=('Arial', 16, 'bold'), 
                              bg='#0a0a0a', fg='white')
        title_label.pack(pady=20)
        
        # Description
        desc_label = tk.Label(self.root, 
                            text="Comprehensive verification tools for memory injection aimbot\nConfirm memory reading, target detection, and aim injection", 
                            font=('Arial', 10), 
                            bg='#0a0a0a', fg='lightgray')
        desc_label.pack(pady=10)
        
        # Verification tools frame
        tools_frame = tk.LabelFrame(self.root, text="🛠️ Verification Tools", 
                                  bg='#0a0a0a', fg='white', font=('Arial', 12, 'bold'))
        tools_frame.pack(pady=20, padx=20, fill='both', expand=True)
        
        # Tool buttons
        self.create_tool_button(tools_frame, "🔍 Debug System", 
                               "Comprehensive debugging with memory logs, target visualization, and performance metrics",
                               self.launch_debug_system, '#8B0000')
        
        self.create_tool_button(tools_frame, "📊 Status Monitor", 
                               "Real-time monitoring of aimbot status, performance graphs, and memory operations",
                               self.launch_status_monitor, '#006400')
        
        self.create_tool_button(tools_frame, "👁️ Visual Overlay", 
                               "Visual overlay showing detected targets, FOV circle, and aim indicators",
                               self.launch_visual_overlay, '#4B0082')
        
        self.create_tool_button(tools_frame, "🧪 Memory Test", 
                               "Test memory injection capabilities and verify Windows API access",
                               self.launch_memory_test, '#FF8C00')
        
        self.create_tool_button(tools_frame, "🎯 Launch Memory Aimbot", 
                               "Start the actual memory injection aimbot with full debugging enabled",
                               self.launch_memory_aimbot, '#DC143C')
        
        # Control buttons
        control_frame = tk.Frame(self.root, bg='#0a0a0a')
        control_frame.pack(pady=10)
        
        tk.Button(control_frame, text="🚀 Launch All Tools", 
                 font=('Arial', 12, 'bold'),
                 bg='#228B22', fg='white', width=15, height=2,
                 command=self.launch_all_tools).pack(side='left', padx=10)
        
        tk.Button(control_frame, text="⏹️ Stop All Tools", 
                 font=('Arial', 12, 'bold'),
                 bg='#B22222', fg='white', width=15, height=2,
                 command=self.stop_all_tools).pack(side='left', padx=10)
        
        # Status display
        self.status_frame = tk.LabelFrame(self.root, text="📊 Tool Status",
                                        bg='#0a0a0a', fg='white', font=('Arial', 10, 'bold'))
        self.status_frame.pack(pady=10, padx=20, fill='x')
        
    def create_tool_button(self, parent, title, description, command, color):
        """Create a tool launch button"""
        button_frame = tk.Frame(parent, bg='#0a0a0a')
        button_frame.pack(fill='x', pady=5, padx=10)
        
        # Main button
        btn = tk.Button(button_frame, text=title, 
                       font=('Arial', 11, 'bold'),
                       bg=color, fg='white', width=25, height=2,
                       command=command)
        btn.pack(side='left', padx=5)
        
        # Description
        desc_label = tk.Label(button_frame, text=description, 
                            bg='#0a0a0a', fg='lightgray', 
                            font=('Arial', 9), wraplength=300, justify='left')
        desc_label.pack(side='left', padx=10, fill='x', expand=True)
        
        # Status indicator
        status_label = tk.Label(button_frame, text="⚫ STOPPED", 
                              bg='#0a0a0a', fg='red', font=('Arial', 9, 'bold'))
        status_label.pack(side='right', padx=5)
        
        self.status_labels[title] = status_label
        
    def launch_debug_system(self):
        """Launch debug system"""
        try:
            if 'Debug System' not in self.processes:
                process = subprocess.Popen([sys.executable, 'memory_aimbot_debug.py'])
                self.processes['Debug System'] = process
                self.status_labels["🔍 Debug System"].config(text="🟢 RUNNING", fg='lime')
                messagebox.showinfo("Launched", "🔍 Debug System launched!\n\nShows comprehensive debugging with:\n• Memory operation logs\n• Target visualization\n• Performance metrics\n• Raw data inspector")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch Debug System:\n{str(e)}")
            
    def launch_status_monitor(self):
        """Launch status monitor"""
        try:
            if 'Status Monitor' not in self.processes:
                process = subprocess.Popen([sys.executable, 'memory_aimbot_monitor.py'])
                self.processes['Status Monitor'] = process
                self.status_labels["📊 Status Monitor"].config(text="🟢 RUNNING", fg='lime')
                messagebox.showinfo("Launched", "📊 Status Monitor launched!\n\nProvides real-time monitoring:\n• Aimbot status indicators\n• Performance graphs\n• Memory operation statistics\n• Target tracking")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch Status Monitor:\n{str(e)}")
            
    def launch_visual_overlay(self):
        """Launch visual overlay"""
        try:
            if 'Visual Overlay' not in self.processes:
                process = subprocess.Popen([sys.executable, 'memory_aimbot_overlay.py'])
                self.processes['Visual Overlay'] = process
                self.status_labels["👁️ Visual Overlay"].config(text="🟢 RUNNING", fg='lime')
                messagebox.showinfo("Launched", "👁️ Visual Overlay launched!\n\nShows what the aimbot sees:\n• FOV circle\n• Detected targets\n• Aim line to current target\n• Debug information overlay")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch Visual Overlay:\n{str(e)}")
            
    def launch_memory_test(self):
        """Launch memory test"""
        try:
            process = subprocess.Popen([sys.executable, 'test_memory_injection.py'])
            messagebox.showinfo("Launched", "🧪 Memory Test launched!\n\nTests memory injection capabilities:\n• Process enumeration\n• Windows API access\n• Memory operations\n• Game offset system")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch Memory Test:\n{str(e)}")
            
    def launch_memory_aimbot(self):
        """Launch memory aimbot with debugging"""
        try:
            warning = messagebox.askyesno("⚠️ WARNING", 
                                        "Launch Memory Injection Aimbot?\n\n" +
                                        "⚠️ This will attempt to inject into game memory\n" +
                                        "• Use only for educational purposes\n" +
                                        "• May trigger anti-cheat systems\n" +
                                        "• Requires administrator privileges\n\n" +
                                        "Continue?")
            
            if warning:
                if 'Memory Aimbot' not in self.processes:
                    process = subprocess.Popen([sys.executable, 'memory_injection_aimbot.py'])
                    self.processes['Memory Aimbot'] = process
                    self.status_labels["🎯 Launch Memory Aimbot"].config(text="🟢 RUNNING", fg='lime')
                    messagebox.showinfo("Launched", "🎯 Memory Aimbot launched!\n\nWith full debugging enabled:\n• Memory operation logging\n• Target detection verification\n• Aim calculation debugging\n• Performance monitoring")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch Memory Aimbot:\n{str(e)}")
            
    def launch_all_tools(self):
        """Launch all verification tools"""
        tools = [
            ("Debug System", self.launch_debug_system),
            ("Status Monitor", self.launch_status_monitor),
            ("Visual Overlay", self.launch_visual_overlay)
        ]
        
        launched = []
        for name, launcher in tools:
            try:
                launcher()
                launched.append(name)
                time.sleep(1)  # Delay between launches
            except Exception as e:
                messagebox.showerror("Error", f"Failed to launch {name}:\n{str(e)}")
        
        if launched:
            messagebox.showinfo("Tools Launched", 
                              f"Successfully launched {len(launched)} verification tools:\n" +
                              "\n".join([f"• {tool}" for tool in launched]) +
                              "\n\nNow you can launch the Memory Aimbot to see all verification data!")
            
    def stop_all_tools(self):
        """Stop all launched tools"""
        stopped = []
        for name, process in list(self.processes.items()):
            try:
                process.terminate()
                stopped.append(name)
                del self.processes[name]
                
                # Update status
                for title, label in self.status_labels.items():
                    if name.lower() in title.lower():
                        label.config(text="⚫ STOPPED", fg='red')
                        
            except Exception as e:
                print(f"Error stopping {name}: {e}")
        
        if stopped:
            messagebox.showinfo("Tools Stopped", 
                              f"Stopped {len(stopped)} tools:\n" +
                              "\n".join([f"• {tool}" for tool in stopped]))
        else:
            messagebox.showinfo("No Tools", "No tools were running")
            
    def update_status(self):
        """Update tool status indicators"""
        for name, process in list(self.processes.items()):
            if process.poll() is not None:  # Process has ended
                del self.processes[name]
                
                # Update status label
                for title, label in self.status_labels.items():
                    if name.lower() in title.lower():
                        label.config(text="⚫ STOPPED", fg='red')
        
        # Schedule next update
        self.root.after(2000, self.update_status)
        
    def run(self):
        """Run the launcher"""
        print("🔍 Memory Aimbot Verification Suite")
        print("===================================")
        print("Launch verification tools to confirm memory injection aimbot functionality")
        
        # Start status updates
        self.update_status()
        
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """Handle window closing"""
        # Stop all processes
        for process in self.processes.values():
            try:
                process.terminate()
            except:
                pass
        
        self.root.destroy()

if __name__ == "__main__":
    launcher = MemoryVerificationLauncher()
    launcher.run()
