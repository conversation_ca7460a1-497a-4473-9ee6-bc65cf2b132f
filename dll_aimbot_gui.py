#!/usr/bin/env python3
"""
🎛️ DLL AIMBOT GUI CONTROLLER
============================
Simple GUI to control the DLL-based aimbot
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
import threading
import time

class DLLAimbotGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 DLL Aimbot Controller")
        self.root.geometry("500x600")
        self.root.configure(bg='#1a1a1a')
        
        # Process tracking
        self.aimbot_process = None
        self.aimbot_running = False
        
        # Settings
        self.fov_var = tk.IntVar(value=400)
        self.sens_var = tk.DoubleVar(value=0.8)
        self.conf_var = tk.DoubleVar(value=0.6)
        self.smooth_var = tk.DoubleVar(value=0.8)
        
        self.create_widgets()
        self.update_status()
        
    def create_widgets(self):
        """Create GUI widgets"""
        # Title
        title_label = tk.Label(self.root, text="🎯 DLL AIMBOT CONTROLLER", 
                              font=('Arial', 18, 'bold'), 
                              bg='#1a1a1a', fg='white')
        title_label.pack(pady=20)
        
        # Description
        desc_label = tk.Label(self.root, 
                            text="Fast DLL-based aimbot with direct mouse control\nNo visual overlay needed!", 
                            font=('Arial', 10), 
                            bg='#1a1a1a', fg='lightgray')
        desc_label.pack(pady=10)
        
        # Status frame
        status_frame = tk.LabelFrame(self.root, text="📊 Status", 
                                   bg='#1a1a1a', fg='white', font=('Arial', 12, 'bold'))
        status_frame.pack(pady=10, padx=20, fill='x')
        
        self.status_label = tk.Label(status_frame, text="💤 AIMBOT: INACTIVE", 
                                   font=('Arial', 14, 'bold'), 
                                   bg='#1a1a1a', fg='red')
        self.status_label.pack(pady=10)
        
        # Control buttons
        control_frame = tk.LabelFrame(self.root, text="🎮 Control", 
                                    bg='#1a1a1a', fg='white', font=('Arial', 12, 'bold'))
        control_frame.pack(pady=10, padx=20, fill='x')
        
        button_frame = tk.Frame(control_frame, bg='#1a1a1a')
        button_frame.pack(pady=15)
        
        # Start button
        self.start_btn = tk.Button(button_frame, text="🚀 START DLL AIMBOT", 
                                  font=('Arial', 12, 'bold'),
                                  bg='#4CAF50', fg='white', width=18, height=2,
                                  command=self.start_aimbot)
        self.start_btn.grid(row=0, column=0, padx=5)
        
        # Stop button
        self.stop_btn = tk.Button(button_frame, text="⏹️ STOP AIMBOT", 
                                 font=('Arial', 12, 'bold'),
                                 bg='#f44336', fg='white', width=18, height=2,
                                 command=self.stop_aimbot)
        self.stop_btn.grid(row=0, column=1, padx=5)
        
        # Settings frame
        settings_frame = tk.LabelFrame(self.root, text="⚙️ Settings", 
                                     bg='#1a1a1a', fg='white', font=('Arial', 12, 'bold'))
        settings_frame.pack(pady=10, padx=20, fill='x')
        
        # FOV Size
        fov_frame = tk.Frame(settings_frame, bg='#1a1a1a')
        fov_frame.pack(pady=5, fill='x')
        
        tk.Label(fov_frame, text="🔵 FOV Size:", bg='#1a1a1a', fg='white', 
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.fov_label = tk.Label(fov_frame, text="400", bg='#1a1a1a', fg='yellow',
                                 font=('Arial', 10, 'bold'))
        self.fov_label.pack(side='right', padx=10)
        
        fov_scale = tk.Scale(fov_frame, from_=200, to=800, orient='horizontal',
                           variable=self.fov_var, bg='#1a1a1a', fg='white',
                           highlightbackground='#1a1a1a', command=self.update_fov_label)
        fov_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Sensitivity
        sens_frame = tk.Frame(settings_frame, bg='#1a1a1a')
        sens_frame.pack(pady=5, fill='x')
        
        tk.Label(sens_frame, text="🎯 Sensitivity:", bg='#1a1a1a', fg='white',
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.sens_label = tk.Label(sens_frame, text="0.8", bg='#1a1a1a', fg='yellow',
                                  font=('Arial', 10, 'bold'))
        self.sens_label.pack(side='right', padx=10)
        
        sens_scale = tk.Scale(sens_frame, from_=0.1, to=2.0, resolution=0.1,
                            orient='horizontal', variable=self.sens_var,
                            bg='#1a1a1a', fg='white', highlightbackground='#1a1a1a',
                            command=self.update_sens_label)
        sens_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Confidence
        conf_frame = tk.Frame(settings_frame, bg='#1a1a1a')
        conf_frame.pack(pady=5, fill='x')
        
        tk.Label(conf_frame, text="🎲 Confidence:", bg='#1a1a1a', fg='white',
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.conf_label = tk.Label(conf_frame, text="0.6", bg='#1a1a1a', fg='yellow',
                                  font=('Arial', 10, 'bold'))
        self.conf_label.pack(side='right', padx=10)
        
        conf_scale = tk.Scale(conf_frame, from_=0.1, to=1.0, resolution=0.1,
                            orient='horizontal', variable=self.conf_var,
                            bg='#1a1a1a', fg='white', highlightbackground='#1a1a1a',
                            command=self.update_conf_label)
        conf_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Smoothing
        smooth_frame = tk.Frame(settings_frame, bg='#1a1a1a')
        smooth_frame.pack(pady=5, fill='x')
        
        tk.Label(smooth_frame, text="🌊 Smoothing:", bg='#1a1a1a', fg='white',
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.smooth_label = tk.Label(smooth_frame, text="0.8", bg='#1a1a1a', fg='yellow',
                                    font=('Arial', 10, 'bold'))
        self.smooth_label.pack(side='right', padx=10)
        
        smooth_scale = tk.Scale(smooth_frame, from_=0.1, to=1.0, resolution=0.1,
                              orient='horizontal', variable=self.smooth_var,
                              bg='#1a1a1a', fg='white', highlightbackground='#1a1a1a',
                              command=self.update_smooth_label)
        smooth_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Instructions frame
        info_frame = tk.LabelFrame(self.root, text="ℹ️ Instructions", 
                                 bg='#1a1a1a', fg='white', font=('Arial', 12, 'bold'))
        info_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        info_text = """
🎯 DLL Aimbot Features:
• Uses DDXoft DLL for fast mouse input
• No visual overlay needed
• Direct game integration
• Much faster than screen capture methods

🎮 Controls (when aimbot is running):
• F1: Toggle aimbot ON/OFF
• F2: Quit aimbot

⚠️ Important:
• Aimbot starts INACTIVE for safety
• Press F1 to activate aiming
• Works with any game that shows players
• Requires windowed/borderless windowed mode
        """
        
        info_label = tk.Label(info_frame, text=info_text, bg='#1a1a1a', fg='lightgray',
                            font=('Arial', 9), justify='left')
        info_label.pack(pady=10, padx=10)
        
    def update_fov_label(self, value):
        self.fov_label.config(text=str(int(float(value))))
        
    def update_sens_label(self, value):
        self.sens_label.config(text=f"{float(value):.1f}")
        
    def update_conf_label(self, value):
        self.conf_label.config(text=f"{float(value):.1f}")
        
    def update_smooth_label(self, value):
        self.smooth_label.config(text=f"{float(value):.1f}")
    
    def start_aimbot(self):
        """Start the DLL aimbot"""
        if self.aimbot_process and self.aimbot_process.poll() is None:
            messagebox.showinfo("Already Running", "DLL Aimbot is already running!")
            return
            
        try:
            # Check if DLL aimbot file exists
            if not os.path.exists('dll_aimbot.py'):
                messagebox.showerror("File Not Found", 
                                   "dll_aimbot.py not found!\n\n" +
                                   "Make sure the DLL aimbot file exists.")
                return
            
            # Start the DLL aimbot process
            self.aimbot_process = subprocess.Popen([sys.executable, 'dll_aimbot.py'])
            self.aimbot_running = True
            
            messagebox.showinfo("DLL Aimbot Started", 
                              "🚀 DLL Aimbot launched successfully!\n\n" +
                              "🎮 Controls:\n" +
                              "• F1: Toggle aimbot ON/OFF\n" +
                              "• F2: Quit aimbot\n\n" +
                              "⚠️ Aimbot starts INACTIVE - Press F1 to activate!")
                              
        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to start DLL aimbot:\n{str(e)}")
    
    def stop_aimbot(self):
        """Stop the DLL aimbot"""
        if self.aimbot_process:
            try:
                self.aimbot_process.terminate()
                self.aimbot_process = None
                self.aimbot_running = False
                messagebox.showinfo("Aimbot Stopped", "⏹️ DLL Aimbot has been stopped")
            except Exception as e:
                messagebox.showerror("Stop Error", f"Error stopping aimbot:\n{str(e)}")
        else:
            messagebox.showwarning("Not Running", "No DLL aimbot process to stop")
    
    def update_status(self):
        """Update status display"""
        if self.aimbot_process and self.aimbot_process.poll() is None:
            self.status_label.config(text="🔥 DLL AIMBOT: RUNNING", fg='green')
            self.aimbot_running = True
        else:
            self.status_label.config(text="💤 DLL AIMBOT: INACTIVE", fg='red')
            self.aimbot_running = False
            
        # Schedule next update
        self.root.after(1000, self.update_status)
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🎛️ Launching DLL Aimbot GUI Controller...")
    app = DLLAimbotGUI()
    app.run()
