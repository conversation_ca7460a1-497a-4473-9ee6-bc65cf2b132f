#!/usr/bin/env python3
"""
🎯 STICKY AIM AIMBOT
===================
Complete sticky aim aimbot system for educational and research purposes.

This package provides a comprehensive implementation of advanced aimbot
technology including computer vision detection, memory reading, sticky
aim tracking, humanization, and anti-detection measures.

⚠️  EDUCATIONAL PURPOSE ONLY ⚠️
This software is intended for educational and research purposes only.
Do not use in online games or violate any Terms of Service.

Author: Educational Research Project
License: MIT (Educational Use Only)
Version: 1.0.0
"""

import sys
import warnings
from typing import Dict, Any

# Version information
__version__ = "1.0.0"
__author__ = "Educational Research Project"
__license__ = "MIT (Educational Use Only)"
__copyright__ = "2024 Educational Research Project"

# Minimum Python version check
MIN_PYTHON_VERSION = (3, 9)
if sys.version_info < MIN_PYTHON_VERSION:
    raise RuntimeError(
        f"Python {MIN_PYTHON_VERSION[0]}.{MIN_PYTHON_VERSION[1]} or higher is required. "
        f"Current version: {sys.version_info.major}.{sys.version_info.minor}"
    )

# Platform check
if sys.platform != "win32":
    warnings.warn(
        "This aimbot system is designed for Windows. "
        "Some features may not work on other platforms.",
        UserWarning
    )

# Educational warning
EDUCATIONAL_WARNING = """
🎯 STICKY AIM AIMBOT - EDUCATIONAL SOFTWARE
==========================================

⚠️  IMPORTANT NOTICE ⚠️

This software is provided for EDUCATIONAL and RESEARCH purposes only.

PERMITTED USES:
✅ Learning about computer vision and AI
✅ Understanding game mechanics and memory structures
✅ Research into human-computer interaction
✅ Educational demonstrations and tutorials
✅ Offline testing and experimentation

PROHIBITED USES:
❌ Online competitive gaming
❌ Violating game Terms of Service
❌ Gaining unfair advantages in multiplayer games
❌ Commercial use without proper licensing
❌ Malicious or harmful activities

LEGAL DISCLAIMER:
The authors and contributors are not responsible for any misuse
of this software. Users are solely responsible for ensuring their
use complies with applicable laws and regulations.

By using this software, you acknowledge that you understand and
agree to these terms.
"""

def show_educational_warning():
    """Display educational warning to users"""
    print(EDUCATIONAL_WARNING)

# Package configuration
CONFIG: Dict[str, Any] = {
    "version": __version__,
    "debug": False,
    "log_level": "INFO",
    "max_fps": 120,
    "safety_timeout": 300,  # 5 minutes
    "educational_mode": True
}

# Import main components (lazy loading for performance)
def get_aimbot_engine():
    """Lazy import of aimbot engine"""
    from .core.aimbot_engine import AimbotEngine
    return AimbotEngine

def get_target_tracker():
    """Lazy import of target tracker"""
    from .core.target_tracker import StickyTargetTracker
    return StickyTargetTracker

def get_cv_detector():
    """Lazy import of computer vision detector"""
    from .detection.cv_detector import ComputerVisionDetector
    return ComputerVisionDetector

def get_memory_detector():
    """Lazy import of memory detector"""
    from .detection.memory_detector import MemoryDetector
    return MemoryDetector

def get_gui():
    """Lazy import of GUI"""
    from .gui.main_window import AimbotGUI
    return AimbotGUI

# Utility functions
def check_dependencies():
    """Check if all required dependencies are installed"""
    required_modules = [
        "cv2", "numpy", "torch", "ultralytics", "psutil", 
        "win32api", "keyboard", "matplotlib"
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        raise ImportError(
            f"Missing required dependencies: {', '.join(missing_modules)}\n"
            f"Install with: pip install sticky-aim-aimbot[all]"
        )
    
    return True

def check_system_requirements():
    """Check system requirements"""
    import platform
    import psutil
    
    # Check OS
    if platform.system() != "Windows":
        warnings.warn("This system is optimized for Windows", UserWarning)
    
    # Check memory
    memory_gb = psutil.virtual_memory().total / (1024**3)
    if memory_gb < 8:
        warnings.warn(
            f"Low system memory: {memory_gb:.1f}GB. "
            f"8GB+ recommended for optimal performance.",
            UserWarning
        )
    
    # Check Python version
    python_version = sys.version_info
    if python_version < (3, 10):
        warnings.warn(
            f"Python {python_version.major}.{python_version.minor} detected. "
            f"Python 3.10+ recommended for best performance.",
            UserWarning
        )
    
    return True

def initialize_logging():
    """Initialize logging system"""
    import logging
    import os
    from .utils.logger import setup_logger
    
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Setup logger
    logger = setup_logger(
        name="sticky_aimbot",
        level=CONFIG["log_level"],
        log_file="logs/aimbot.log"
    )
    
    logger.info(f"Sticky Aimbot v{__version__} initialized")
    return logger

# Package initialization
def initialize():
    """Initialize the aimbot package"""
    try:
        # Show educational warning
        if CONFIG["educational_mode"]:
            show_educational_warning()
        
        # Check dependencies
        check_dependencies()
        
        # Check system requirements
        check_system_requirements()
        
        # Initialize logging
        logger = initialize_logging()
        logger.info("Package initialization complete")
        
        return True
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return False

# Auto-initialize when package is imported
_initialized = False
if not _initialized:
    _initialized = initialize()

# Export main classes and functions
__all__ = [
    # Version info
    "__version__",
    "__author__",
    "__license__",
    
    # Main components (lazy loaded)
    "get_aimbot_engine",
    "get_target_tracker", 
    "get_cv_detector",
    "get_memory_detector",
    "get_gui",
    
    # Utility functions
    "check_dependencies",
    "check_system_requirements",
    "initialize_logging",
    "show_educational_warning",
    
    # Configuration
    "CONFIG"
]
