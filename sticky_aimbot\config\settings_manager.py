#!/usr/bin/env python3
"""
⚙️ SETTINGS MANAGER
===================
Configuration management for the sticky aimbot system.
"""

import json
import os
from typing import Dict, Any, Optional

class SettingsManager:
    """Manages configuration settings for the aimbot system"""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize settings manager"""
        self.config_path = config_path or "config/default.json"
        self.config = self._load_default_config()
        
        # Load config file if it exists
        if os.path.exists(self.config_path):
            self.load_config()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration"""
        return {
            "detection": {
                "method": "cv",
                "confidence": 0.7,
                "max_targets": 10
            },
            "tracking": {
                "max_lock_distance": 500.0,
                "max_lock_fov": 45.0,
                "lock_strength": 0.8,
                "smoothing_factor": 4.0,
                "prediction_enabled": True,
                "prediction_time": 0.15,
                "target_switch_delay": 0.3
            },
            "input": {
                "method": "mouse",
                "sensitivity": 1.0
            },
            "humanization": {
                "micro_adjustments": True,
                "fatigue_simulation": True,
                "reaction_delay": 0.05,
                "random_jitter": 0.02
            },
            "performance": {
                "target_fps": 60,
                "max_cpu_usage": 15.0
            },
            "safety": {
                "max_runtime": 3600,
                "emergency_stop_key": "F12"
            }
        }
    
    def get_config(self) -> Dict[str, Any]:
        """Get current configuration"""
        return self.config.copy()
    
    def load_config(self) -> bool:
        """Load configuration from file"""
        try:
            with open(self.config_path, 'r') as f:
                loaded_config = json.load(f)
            
            # Merge with defaults
            self._merge_config(self.config, loaded_config)
            return True
            
        except Exception as e:
            print(f"❌ Failed to load config: {e}")
            return False
    
    def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """Save configuration to file"""
        try:
            config_to_save = config or self.config
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w') as f:
                json.dump(config_to_save, f, indent=2)
            
            if config:
                self.config = config
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to save config: {e}")
            return False
    
    def _merge_config(self, base: Dict[str, Any], update: Dict[str, Any]):
        """Recursively merge configuration dictionaries"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
