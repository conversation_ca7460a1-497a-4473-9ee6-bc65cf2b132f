#!/usr/bin/env python3
"""
🧪 TEST VIRTUAL GAMEPAD
======================
Quick test to verify vgamepad functionality
"""

import time

try:
    import vgamepad as vg
    print("✅ vgamepad library found")
    
    print("🎮 Creating virtual Xbox 360 gamepad...")
    gamepad = vg.VX360Gamepad()
    time.sleep(1)
    
    print("🎮 Testing button press...")
    gamepad.press_button(button=vg.XUSB_BUTTON.XUSB_GAMEPAD_A)
    gamepad.update()
    time.sleep(0.5)
    gamepad.release_button(button=vg.XUSB_BUTTON.XUSB_GAMEPAD_A)
    gamepad.update()
    
    print("🕹️ Testing right stick movement...")
    gamepad.right_joystick_float(x_value_float=0.5, y_value_float=0.5)
    gamepad.update()
    time.sleep(0.5)
    gamepad.right_joystick_float(x_value_float=0.0, y_value_float=0.0)
    gamepad.update()
    
    print("🎯 Testing right trigger...")
    gamepad.right_trigger_float(value_float=1.0)
    gamepad.update()
    time.sleep(0.5)
    gamepad.right_trigger_float(value_float=0.0)
    gamepad.update()
    
    print("🔄 Resetting gamepad...")
    gamepad.reset()
    gamepad.update()
    
    print("✅ Virtual gamepad test completed successfully!")
    print("🎮 Your system should have detected an Xbox controller")
    
except ImportError:
    print("❌ vgamepad library not installed!")
    print("💡 Install with: pip install vgamepad")
except Exception as e:
    print(f"❌ Test failed: {e}")
    print("💡 Make sure ViGEmBus driver is installed")

input("\nPress Enter to exit...")
