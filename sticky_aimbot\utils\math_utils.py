#!/usr/bin/env python3
"""
🧮 MATH UTILITIES
=================
Mathematical functions for aimbot calculations.
"""

import math
import numpy as np
from typing import Tuple, List

def calculate_distance(pos1: <PERSON><PERSON>[float, float, float], 
                      pos2: <PERSON><PERSON>[float, float, float]) -> float:
    """Calculate 3D distance between two positions"""
    return math.sqrt(
        (pos2[0] - pos1[0])**2 + 
        (pos2[1] - pos1[1])**2 + 
        (pos2[2] - pos1[2])**2
    )

def calculate_angle(from_pos: Tuple[float, float, float], 
                   to_pos: Tuple[float, float, float]) -> <PERSON><PERSON>[float, float]:
    """Calculate pitch and yaw angles to target"""
    dx = to_pos[0] - from_pos[0]
    dy = to_pos[1] - from_pos[1]
    dz = to_pos[2] - from_pos[2]
    
    distance = math.sqrt(dx*dx + dy*dy + dz*dz)
    
    if distance == 0:
        return (0.0, 0.0)
    
    # Calculate angles
    yaw = math.atan2(dy, dx) * 180.0 / math.pi
    pitch = -math.asin(dz / distance) * 180.0 / math.pi
    
    return (pitch, yaw)

def normalize_angle(angle: float) -> float:
    """Normalize angle to [-180, 180] range"""
    while angle > 180:
        angle -= 360
    while angle < -180:
        angle += 360
    return angle

def interpolate_smooth(start: float, end: float, t: float) -> float:
    """Smooth interpolation between two values"""
    # Cubic easing
    t = t * t * (3.0 - 2.0 * t)
    return start + (end - start) * t

def interpolate_bezier(control_points: List[np.ndarray], t: float) -> np.ndarray:
    """Bezier curve interpolation"""
    n = len(control_points) - 1
    result = np.zeros_like(control_points[0])
    
    for i, point in enumerate(control_points):
        # Binomial coefficient
        coeff = math.comb(n, i) * (t**i) * ((1-t)**(n-i))
        result += coeff * point
    
    return result

def predict_position(current_pos: Tuple[float, float], 
                    velocity: Tuple[float, float], 
                    time_ahead: float) -> Tuple[float, float]:
    """Predict future position based on velocity"""
    return (
        current_pos[0] + velocity[0] * time_ahead,
        current_pos[1] + velocity[1] * time_ahead
    )

def calculate_iou(box1: Tuple[float, float, float, float], 
                 box2: Tuple[float, float, float, float]) -> float:
    """Calculate Intersection over Union for bounding boxes"""
    x1_min, y1_min, x1_max, y1_max = box1
    x2_min, y2_min, x2_max, y2_max = box2
    
    # Calculate intersection
    x_min = max(x1_min, x2_min)
    y_min = max(y1_min, y2_min)
    x_max = min(x1_max, x2_max)
    y_max = min(y1_max, y2_max)
    
    if x_max <= x_min or y_max <= y_min:
        return 0.0
    
    intersection = (x_max - x_min) * (y_max - y_min)
    
    # Calculate union
    area1 = (x1_max - x1_min) * (y1_max - y1_min)
    area2 = (x2_max - x2_min) * (y2_max - y2_min)
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0
