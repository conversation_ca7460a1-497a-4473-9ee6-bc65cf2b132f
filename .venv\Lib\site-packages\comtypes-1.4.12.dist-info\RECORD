../../Scripts/clear_comtypes_cache.exe,sha256=z9rXYBtXqzYbC5YaNRe3RoT9-x8IK_HiRBHOKnRTYpg,108392
comtypes-1.4.12.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
comtypes-1.4.12.dist-info/METADATA,sha256=W7gESIYTlbZBlfWBjx0OytybGNmVlNozpeLeu9G7GRY,7309
comtypes-1.4.12.dist-info/RECORD,,
comtypes-1.4.12.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
comtypes-1.4.12.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
comtypes-1.4.12.dist-info/entry_points.txt,sha256=Qv2GxaRYrKn2xxEqjcg2BbqNzAP8PZp4rYfBUVpvag0,67
comtypes-1.4.12.dist-info/licenses/LICENSE.txt,sha256=Oxdn8BCYC0aSayO_Cvzl1y8zWe5eKye6ynG5tCCas4M,1273
comtypes-1.4.12.dist-info/top_level.txt,sha256=bxrAJZpBG5XMT64eKx908THCbupyI0ywd7cYIlx4Uco,9
comtypes/GUID.py,sha256=d78UkD3Q1atD3uRsiq0lfV6Tw6-9SjJT1093nXv8ZwA,3579
comtypes/__init__.py,sha256=Z5ihts9w-4XEnDPD4Aq4eGIWGMDy1cTX-V6yOXTAeL0,9170
comtypes/__pycache__/GUID.cpython-313.pyc,,
comtypes/__pycache__/__init__.cpython-313.pyc,,
comtypes/__pycache__/_comobject.cpython-313.pyc,,
comtypes/__pycache__/_memberspec.cpython-313.pyc,,
comtypes/__pycache__/_meta.cpython-313.pyc,,
comtypes/__pycache__/_npsupport.cpython-313.pyc,,
comtypes/__pycache__/_safearray.cpython-313.pyc,,
comtypes/__pycache__/_tlib_version_checker.cpython-313.pyc,,
comtypes/__pycache__/_vtbl.cpython-313.pyc,,
comtypes/__pycache__/automation.cpython-313.pyc,,
comtypes/__pycache__/clear_cache.cpython-313.pyc,,
comtypes/__pycache__/connectionpoints.cpython-313.pyc,,
comtypes/__pycache__/errorinfo.cpython-313.pyc,,
comtypes/__pycache__/git.cpython-313.pyc,,
comtypes/__pycache__/hresult.cpython-313.pyc,,
comtypes/__pycache__/logutil.cpython-313.pyc,,
comtypes/__pycache__/messageloop.cpython-313.pyc,,
comtypes/__pycache__/patcher.cpython-313.pyc,,
comtypes/__pycache__/persist.cpython-313.pyc,,
comtypes/__pycache__/safearray.cpython-313.pyc,,
comtypes/__pycache__/shelllink.cpython-313.pyc,,
comtypes/__pycache__/stream.cpython-313.pyc,,
comtypes/__pycache__/typeinfo.cpython-313.pyc,,
comtypes/__pycache__/util.cpython-313.pyc,,
comtypes/__pycache__/viewobject.cpython-313.pyc,,
comtypes/_comobject.py,sha256=AzK9Mi9Z0y2hiR-u3nbrCZYuZD7aGRlLiCk8QG8QzcM,19596
comtypes/_memberspec.py,sha256=6p591h3fGgTowodh8uzaBQYrXsnJJyEEorHhrAHEtRE,24832
comtypes/_meta.py,sha256=EckTJGcRdsiF9qRpeTps47ieEp5LHja_XwKYahh_-XE,3916
comtypes/_npsupport.py,sha256=01qb3TN4n9O8R6NCkkuCYo_attbf5tXKPnh0N5H8Zgw,5050
comtypes/_post_coinit/__init__.py,sha256=2NugBum--2JO6fvSm8wuO77V45LiCpjDd8emUVyA79I,727
comtypes/_post_coinit/__pycache__/__init__.cpython-313.pyc,,
comtypes/_post_coinit/__pycache__/_cominterface_meta_patcher.cpython-313.pyc,,
comtypes/_post_coinit/__pycache__/activeobj.cpython-313.pyc,,
comtypes/_post_coinit/__pycache__/bstr.cpython-313.pyc,,
comtypes/_post_coinit/__pycache__/instancemethod.cpython-313.pyc,,
comtypes/_post_coinit/__pycache__/misc.cpython-313.pyc,,
comtypes/_post_coinit/__pycache__/unknwn.cpython-313.pyc,,
comtypes/_post_coinit/_cominterface_meta_patcher.py,sha256=Bo6D-1kastq5zesM1bspGnf6q7Eh2Bwfy2SUBu0WYxg,5843
comtypes/_post_coinit/activeobj.py,sha256=GLkMCMN8igvVhHLiaX4cH5Hqn2SSLabmJcFUDeHBQ7E,2024
comtypes/_post_coinit/bstr.py,sha256=R2dPCPq2mqcJfODshOHkwFaXxHcjRwSBnrmTWREvdhM,1288
comtypes/_post_coinit/instancemethod.py,sha256=p-DF-NM4DpKXWrRawQ9mW78zmT36qKWump-0RNOiXOE,369
comtypes/_post_coinit/misc.py,sha256=d5gclY4bOv_RV7P44kqk53AARo27izTEt85SEyNeHW0,10962
comtypes/_post_coinit/unknwn.py,sha256=jOrewMnI-OC3pCcMW3mx8aF85y17m4kpueMXMlv97LQ,17338
comtypes/_safearray.py,sha256=toNpuKYP00DYr-d_y0v8QNysAnx7CmWjmndY2LK63z8,4558
comtypes/_tlib_version_checker.py,sha256=gxQNW5BhKch3Gds9mBSAnsFkceU4vWzMtYDhfDt28OA,616
comtypes/_vtbl.py,sha256=KUKOp_grUEj-Xzqn6JDEJpRj-kt1qLKn3bajjjF5cLs,16051
comtypes/automation.py,sha256=CD2gWSwx9j-ZpM-meNLkhUqo5lS3fGScW0i_PlQyuXs,35195
comtypes/clear_cache.py,sha256=J2MHIgMf9b3DLxR6uINg611cLM81IyVEzsk1R-5iZPs,1759
comtypes/client/__init__.py,sha256=KkmiYa_XwKLJcjxFqFA6xit74Vdm0231hUJADvL7gLI,1320
comtypes/client/__pycache__/__init__.cpython-313.pyc,,
comtypes/client/__pycache__/_activeobj.cpython-313.pyc,,
comtypes/client/__pycache__/_code_cache.cpython-313.pyc,,
comtypes/client/__pycache__/_constants.cpython-313.pyc,,
comtypes/client/__pycache__/_create.cpython-313.pyc,,
comtypes/client/__pycache__/_events.cpython-313.pyc,,
comtypes/client/__pycache__/_generate.cpython-313.pyc,,
comtypes/client/__pycache__/_managing.cpython-313.pyc,,
comtypes/client/__pycache__/dynamic.cpython-313.pyc,,
comtypes/client/__pycache__/lazybind.cpython-313.pyc,,
comtypes/client/_activeobj.py,sha256=1HR4lC9BDYik8kSgOPxO4v1eTnAUokqgjkKkIPrDyxU,1985
comtypes/client/_code_cache.py,sha256=Vv5mea7ExrKpOYi2VOrgSO5vbja2JtRlTwPij8atEBk,5394
comtypes/client/_constants.py,sha256=qTwemUKpoOgg64-SZ1z0Rmw8LOTyc_8tJXqodIfmGlM,4569
comtypes/client/_create.py,sha256=4qwaQUwUbd3dvKEYNbvITGS1yQzipI58yoNB4WvQhoc,5644
comtypes/client/_events.py,sha256=3L_lOdfYOp5gDxaEEc4btOADS4JD2hJ_G6mnPTCTa0w,13811
comtypes/client/_generate.py,sha256=-gunNXjhANOXAEbBC8RCC4eYdNqnFCUBcrEqwtVM7QM,11933
comtypes/client/_managing.py,sha256=9Pmru-17KxugccKc44KpDkabUoIKWqAyCou8Ucci7iU,4266
comtypes/client/dynamic.py,sha256=E_edylBolMD_iWv_wfreMgmCMVLkF7QmcLr7iamNUbE,6009
comtypes/client/lazybind.py,sha256=RipdT6mtzMdqpvpNVTufXEJ-NjLcFHB9sDb48AlnvR8,8900
comtypes/connectionpoints.py,sha256=TFLGNbFsISGMETZ2cbWfc7kyVQHOebT3vs6aaL0Sd1M,4669
comtypes/errorinfo.py,sha256=Nv49NP4JxZgay_RekebC-qHFUZGCD6SKU3rm6HLWvKQ,5924
comtypes/git.py,sha256=_LQHMM-c5a3ejxEI9Aq36_pC79ioXWtmOoqr0j4tDuc,2750
comtypes/hints.pyi,sha256=Z3NhkZ1PBMcz4bYL_eKck6izAolMJWon5vCHMhKx-Vs,11358
comtypes/hresult.py,sha256=ZboKv4KH5-O4o7RuquyP9_cq67keMhSYrVm9n9A0AwE,5415
comtypes/logutil.py,sha256=YLUooXhgUre2ehzWea_J1x2_23wlzCZ66Oz5t1ElX1k,2014
comtypes/messageloop.py,sha256=02RpiXiZBsDhFyvH32rY3V4KO80lp90fn8WBVhNhyxA,1656
comtypes/patcher.py,sha256=XRWvmzTL7HQQak9QivplSIiNafPRLoYQB_2qL3OzdQM,2030
comtypes/persist.py,sha256=0A5xhzT-4suYGjGgGWQ13WJZ3zvxjFoId_fKCAmbmxU,9121
comtypes/safearray.py,sha256=WpF2LhxaNjqk93xEanvnq_sy4jJbInwt65N5FZHdnWQ,17842
comtypes/server/__init__.py,sha256=8nto-zYz7miioL5gIinoLNRNCxcFJrnaJ03wKp4lLqo,2564
comtypes/server/__pycache__/__init__.cpython-313.pyc,,
comtypes/server/__pycache__/automation.cpython-313.pyc,,
comtypes/server/__pycache__/connectionpoints.cpython-313.pyc,,
comtypes/server/__pycache__/inprocserver.cpython-313.pyc,,
comtypes/server/__pycache__/localserver.cpython-313.pyc,,
comtypes/server/__pycache__/register.cpython-313.pyc,,
comtypes/server/__pycache__/w_getopt.cpython-313.pyc,,
comtypes/server/automation.py,sha256=oNFoogggdIoHpdrbQvig2ZXc6AuqPCx8JkrNSkNRU3Q,2843
comtypes/server/connectionpoints.py,sha256=Tqk8K2FiJh9HFpUHDSzDc-y8UWdOqvoAHad7E2HMlso,6777
comtypes/server/inprocserver.py,sha256=4P0kTOXSuxRBBYEzBS1qkI_k8SzJhQNh98uUyDC6mrc,5003
comtypes/server/localserver.py,sha256=86iq1OsWGSUhP3_s9wkHkUiPvkyPxVwELojPJ5-RlEI,3201
comtypes/server/register.py,sha256=5dKRWZxG2_y0N2rKjFFnoOC09K1TLIlv_XP5OlMismg,17213
comtypes/server/w_getopt.py,sha256=zYjIsVqECvHGdiuwYEWNtzOFeuZSefZr_uyBYrXoij0,1571
comtypes/shelllink.py,sha256=6ZLkXAP-KVE7WBsZcd3yhhsIp01ygiv9DKKlo853pf0,10933
comtypes/stream.py,sha256=Qnxu1rVjn53zv5dTMzbFBHp1Se21bQrEQO-JWULJE0k,2573
comtypes/test/TestComServer.idl,sha256=72f38383VDU7opBvh6Mc_iE95QC5rQ-vR9kPX81n-9M,2487
comtypes/test/TestComServer.py,sha256=fqPrNf40ko61X3pxjp9qJHHoNihI_WO8EtirwOpGN-4,5161
comtypes/test/TestComServer.tlb,sha256=fdwAtGi-2skbrTHBEoOImEg48rxglmeY0H5IuD5_3Sk,3560
comtypes/test/TestDispServer.idl,sha256=SKdlb77Kj7pxkTX1BRYV04htcq0PDBq59ei2lMFuSI4,1835
comtypes/test/TestDispServer.py,sha256=-1B20b4RB2QBtj5v6VS59mzj20Xj8EszgpRw_KGgQsU,3776
comtypes/test/TestDispServer.tlb,sha256=eKi5NpCurHwgql_zGzc1SawWjSMbDojMRyrVj_CrRao,2992
comtypes/test/__init__.py,sha256=CqWdme72O-oNMqZT5oLm0dM-wvtZngGOHcD37YJbFwE,8162
comtypes/test/__pycache__/TestComServer.cpython-313.pyc,,
comtypes/test/__pycache__/TestDispServer.cpython-313.pyc,,
comtypes/test/__pycache__/__init__.cpython-313.pyc,,
comtypes/test/__pycache__/find_memleak.cpython-313.pyc,,
comtypes/test/__pycache__/runtests.cpython-313.pyc,,
comtypes/test/__pycache__/setup.cpython-313.pyc,,
comtypes/test/__pycache__/test_BSTR.cpython-313.pyc,,
comtypes/test/__pycache__/test_DISPPARAMS.cpython-313.pyc,,
comtypes/test/__pycache__/test_GUID.cpython-313.pyc,,
comtypes/test/__pycache__/test_QueryService.cpython-313.pyc,,
comtypes/test/__pycache__/test_agilent.cpython-313.pyc,,
comtypes/test/__pycache__/test_avmc.cpython-313.pyc,,
comtypes/test/__pycache__/test_basic.cpython-313.pyc,,
comtypes/test/__pycache__/test_casesensitivity.cpython-313.pyc,,
comtypes/test/__pycache__/test_clear_cache.cpython-313.pyc,,
comtypes/test/__pycache__/test_client.cpython-313.pyc,,
comtypes/test/__pycache__/test_client_dynamic.cpython-313.pyc,,
comtypes/test/__pycache__/test_client_regenerate_modules.cpython-313.pyc,,
comtypes/test/__pycache__/test_collections.cpython-313.pyc,,
comtypes/test/__pycache__/test_comobject.cpython-313.pyc,,
comtypes/test/__pycache__/test_comserver.cpython-313.pyc,,
comtypes/test/__pycache__/test_createwrappers.cpython-313.pyc,,
comtypes/test/__pycache__/test_dict.cpython-313.pyc,,
comtypes/test/__pycache__/test_dispifc_records.cpython-313.pyc,,
comtypes/test/__pycache__/test_dispifc_safearrays.cpython-313.pyc,,
comtypes/test/__pycache__/test_dispinterface.cpython-313.pyc,,
comtypes/test/__pycache__/test_dyndispatch.cpython-313.pyc,,
comtypes/test/__pycache__/test_errorinfo.cpython-313.pyc,,
comtypes/test/__pycache__/test_excel.cpython-313.pyc,,
comtypes/test/__pycache__/test_findgendir.cpython-313.pyc,,
comtypes/test/__pycache__/test_getactiveobj.cpython-313.pyc,,
comtypes/test/__pycache__/test_hresult.cpython-313.pyc,,
comtypes/test/__pycache__/test_ie.cpython-313.pyc,,
comtypes/test/__pycache__/test_ienum.cpython-313.pyc,,
comtypes/test/__pycache__/test_imfattributes.cpython-313.pyc,,
comtypes/test/__pycache__/test_inout_args.cpython-313.pyc,,
comtypes/test/__pycache__/test_messageloop.cpython-313.pyc,,
comtypes/test/__pycache__/test_midl_safearray_create.cpython-313.pyc,,
comtypes/test/__pycache__/test_monikers.cpython-313.pyc,,
comtypes/test/__pycache__/test_msscript.cpython-313.pyc,,
comtypes/test/__pycache__/test_npsupport.cpython-313.pyc,,
comtypes/test/__pycache__/test_outparam.cpython-313.pyc,,
comtypes/test/__pycache__/test_persist.cpython-313.pyc,,
comtypes/test/__pycache__/test_pump_events.cpython-313.pyc,,
comtypes/test/__pycache__/test_recordinfo.cpython-313.pyc,,
comtypes/test/__pycache__/test_safearray.cpython-313.pyc,,
comtypes/test/__pycache__/test_sapi.cpython-313.pyc,,
comtypes/test/__pycache__/test_server.cpython-313.pyc,,
comtypes/test/__pycache__/test_server_register.cpython-313.pyc,,
comtypes/test/__pycache__/test_shelllink.cpython-313.pyc,,
comtypes/test/__pycache__/test_showevents.cpython-313.pyc,,
comtypes/test/__pycache__/test_storage.cpython-313.pyc,,
comtypes/test/__pycache__/test_stream.cpython-313.pyc,,
comtypes/test/__pycache__/test_subinterface.cpython-313.pyc,,
comtypes/test/__pycache__/test_typeannotator.cpython-313.pyc,,
comtypes/test/__pycache__/test_typeinfo.cpython-313.pyc,,
comtypes/test/__pycache__/test_urlhistory.cpython-313.pyc,,
comtypes/test/__pycache__/test_util.cpython-313.pyc,,
comtypes/test/__pycache__/test_variant.cpython-313.pyc,,
comtypes/test/__pycache__/test_viewobject.cpython-313.pyc,,
comtypes/test/__pycache__/test_w_getopt.cpython-313.pyc,,
comtypes/test/__pycache__/test_win32com_interop.cpython-313.pyc,,
comtypes/test/__pycache__/test_wmi.cpython-313.pyc,,
comtypes/test/__pycache__/test_word.cpython-313.pyc,,
comtypes/test/find_memleak.py,sha256=qygFRGSLnhRz0blzQrFOkG2TJavH_VX7m6cK9tsaes0,2157
comtypes/test/mylib.idl,sha256=FVfMqxRSaLZCA3K3EFeNPibPv9KApSIPZnOowI2fVEM,1664
comtypes/test/mylib.tlb,sha256=Kvv8JW32DW7iCeBWiQo91D-21OYb7OMNuy5hYY2hPvg,3080
comtypes/test/mytypelib.idl,sha256=ghjqgtzlLYvsptCXq44AS7P85DPJwbLmqTBYJKBU62s,2590
comtypes/test/runtests.py,sha256=bI08Hq5XCaHM5NAVG1E3FSbMbE6td0-3z2kdhuZosWU,144
comtypes/test/setup.py,sha256=r28sSnwiFO4c6VTuxPHpEDSm9aHT9MKa_KktKzUGhOU,172
comtypes/test/test_BSTR.py,sha256=1hQ85usi6qm8exwPRWK6tJLBSVTQ9jlYpxjDjxtV0Nk,1208
comtypes/test/test_DISPPARAMS.py,sha256=P1VcwZ9k_p3JbK6oFn2Ld0AI413xiHDl3RD9Qjx9fe4,1156
comtypes/test/test_GUID.py,sha256=xnb4hywpPw1V_uc2vZoD7mKtlwb6iNqRIIS2MWa1WAc,1720
comtypes/test/test_QueryService.py,sha256=c4ulaokZtytySqBX7pygbReDemwRMWL3hlG5RGiXLOk,836
comtypes/test/test_agilent.py,sha256=9-rLu6IXQe0Ckb_ZCLxinMvLzlfZI9A3ckVgL5Z4l9k,4394
comtypes/test/test_avmc.py,sha256=EyuLdBGpIGvjqdgGoHAsaIPIVpNl7DlIxvUfX55_5ts,1368
comtypes/test/test_basic.py,sha256=t11A2I6iWWIqzggCjinWSPrJgBmGhi9OcnhTNkL28yg,4695
comtypes/test/test_casesensitivity.py,sha256=PshS9mu9gZ0YWwAWrEjDOJneou_DkwsKbdtVTNP6YMk,1366
comtypes/test/test_clear_cache.py,sha256=RnXcAlyhX3QrbPdV0D-LX4WL6jsr-PpVYt43VmdxR4M,818
comtypes/test/test_client.py,sha256=6jc_JfRop9Tj3lafmpoqY-QUOogAhrxVjcjydUZBUxw,13295
comtypes/test/test_client_dynamic.py,sha256=djUdmENQ-81yA3kX0BWKf82tpjH_C2DIg09-fZYlwwE,2900
comtypes/test/test_client_regenerate_modules.py,sha256=2Bvaov6Tmm9VFE4JtHqb61BaCTxuXnGXnIbUyaqAu5E,7515
comtypes/test/test_collections.py,sha256=QmhxggPXD5g4GOq8MfKa8TRPRo1w-OPa3Vlkm1s9140,5481
comtypes/test/test_comobject.py,sha256=89wXfMnMivVOav0zilNrgBsi_p3skl8D8b2F8rGRoM4,5779
comtypes/test/test_comserver.py,sha256=ikEwzRv5x-vATzL8i8vJncK7fxrKx-95LIMsJuINilk,12555
comtypes/test/test_createwrappers.py,sha256=QwD6T-pN9BG0VH2_3U-IaUCextqQyGQz919vUIudEgk,4093
comtypes/test/test_dict.py,sha256=eyecYBahyeoyk0R5gkP6Btql842ibXdAlmbr_t5lj54,3290
comtypes/test/test_dispifc_records.py,sha256=XqrPAsxDKxGtfeg0HtUt5SRNnWc5ShIclYslg0r8S-E,4258
comtypes/test/test_dispifc_safearrays.py,sha256=r0NSU51zMTUeXw9g9m88_D5-USgfu3uwfQraQilkaYo,3954
comtypes/test/test_dispinterface.py,sha256=xK8-tVFO-1KKKSpQ0f_r7txCoWoS-t5UkGcCLlmGCII,5104
comtypes/test/test_dyndispatch.py,sha256=TnITsx_-A0xtbaJtkqa8r3xCTTOVvcCty-zZtkVFS3U,3143
comtypes/test/test_errorinfo.py,sha256=MVi7Z7mXZgJmCXrlkvsV_57HQEH7Cx9AdsdIO1NGOgQ,4763
comtypes/test/test_excel.py,sha256=6iDI6H78RxuXH3mUvlk1QoCLcNG3BL6I5X2mNJ7Ojmg,5046
comtypes/test/test_findgendir.py,sha256=qXaZkWX7hLXuPNSZgyii1QadTXod23RSjPMDcNAuw9I,3032
comtypes/test/test_getactiveobj.py,sha256=El8scspM5ZtHU-tpEcEZq-aEOSkUj2YwO43h-vPscoQ,2776
comtypes/test/test_hresult.py,sha256=BRwJw3csFnOOIhQXZxDUva4oko2qkQK1X3ZdwvVNXc4,3149
comtypes/test/test_ie.py,sha256=qqE-_Y8ki488vak7cJC8RGoRq7Njr1NLDJRWKsL-ln8,3839
comtypes/test/test_ienum.py,sha256=sl31YDmv-tpTR2MzCm190CKLjt0bk9Fz-U7mjfC0Xvo,887
comtypes/test/test_imfattributes.py,sha256=x9fJG0E5l7qnpwEhmkbSB3gvzkakmEvMeP8oaB9yxCM,1411
comtypes/test/test_inout_args.py,sha256=ae-wyZ78xbA6zVN9bTW_PfZNfLgbT3hSkE5KQb7S9o4,21040
comtypes/test/test_jscript.js,sha256=53vJJO4fDUBxIg3-D8Wuldzeejp3_MvImpN6yedPMTI,404
comtypes/test/test_messageloop.py,sha256=FjV-8l2iiEaeYrPrA_lQNEUfq1EDMi2IxKlKGOWbTeU,4605
comtypes/test/test_midl_safearray_create.py,sha256=jsCmQoLRmq7YdtNdhg6XFxXZCex7r5IpOuyvXE4InEk,4122
comtypes/test/test_monikers.py,sha256=qkq6zK0xz3MZ2j4qOfGm6UjVjXwssJceZHYz22T3w0g,3218
comtypes/test/test_msscript.py,sha256=yPi7EuFqjCBwBMM1GBDWvJ95FJ-raVwzjT206vpRclc,2874
comtypes/test/test_npsupport.py,sha256=7dTrGOoaBZNsMTyl5JYz1rwFCSU79C3H9SJyi4b2mG4,12559
comtypes/test/test_outparam.py,sha256=Zbxg_Mv2L16ISwdavWnLU9qz7vc8vWuBFKbz0_dqorY,3031
comtypes/test/test_persist.py,sha256=TZXk2neGJHjTwwUAPAZN_uUd_nYMWFBXOeAlu7o3C_Q,2677
comtypes/test/test_pump_events.py,sha256=wcOhzSgNkTBRZV_oywfO_sQeI9N-u3Kf9oQHhBmR1Dg,379
comtypes/test/test_recordinfo.py,sha256=s4cP_nUWEPYx8ldlYwMxXHjOiCaSQNU0kYGMI8nGqc8,2704
comtypes/test/test_safearray.py,sha256=msMVveLT7rSzDOAU7l1j5Owc7ePR7xrJG74FpHkMHA0,6935
comtypes/test/test_sapi.py,sha256=qY8tpIOkD1WAgjkIk1CKHs_NT1-GTkxrFCCVZ6d79vI,1162
comtypes/test/test_server.py,sha256=CQQRZBhN6AT-yOEupuWhUEuq040KxXXr0WXu5ahQtuk,10717
comtypes/test/test_server_register.py,sha256=4Dm1RpRczFkW4Kog52f_xU3YOr5SrSHbnQNlwJJ6BMk,20039
comtypes/test/test_shelllink.py,sha256=HiNLstYY63ihmAHAReEy2fq1pIDaNgnksZnImDMq-DM,5113
comtypes/test/test_showevents.py,sha256=lXMiv9irnAKgGjXVYFWD1TsjjEyBhj8fB86f9T9lC0Y,1134
comtypes/test/test_storage.py,sha256=3yMGaUM_nuwioJq-Rf03qmeKNnIAnm-SP08XCE5xySc,5557
comtypes/test/test_stream.py,sha256=I7rfPuDTUE2oAGO1vk1LiiEzl8L-8dNDp2Fltr2n0uw,5928
comtypes/test/test_subinterface.py,sha256=9uXpVLmO07vWe98RGWHLPga6Du6prSAa9ffvVe5-ZEk,383
comtypes/test/test_typeannotator.py,sha256=CUW4M2dhIGfQMNB4mPMDjR0ErMIos1W1D6kgt0ur7RM,6674
comtypes/test/test_typeinfo.py,sha256=JVgmvg_ZlF_ny8Xon11k7RKbFt1-6_SwG27B-QmzSPc,4691
comtypes/test/test_urlhistory.py,sha256=GoNjFUiFNcNFxJtWeeXWde-XXdL6Z1v1vM3h-D40GEw,1846
comtypes/test/test_util.py,sha256=6eMFympzOkSRlTnYJRFbVrFuZMXnY2ed8-clst4JVmk,8520
comtypes/test/test_variant.py,sha256=n_KKJpmgYBQBvmba_N8vxcDCsDJFG2SlzTK_iTAvo6U,10764
comtypes/test/test_viewobject.py,sha256=mNmRuH1PaF74RlHq9JRBKJmOJLyE1RjZewIep1HWHpo,2842
comtypes/test/test_w_getopt.py,sha256=ubJA-Iow_DMz8Kpe3CIOnbxgq5bufSV45ul4KXTjJgc,1014
comtypes/test/test_win32com_interop.py,sha256=sBwnFaTj3y5vKhzpsQ_tCObIHP-dxH_O0ULesxXCvps,5853
comtypes/test/test_wmi.py,sha256=AjSH_ztPGoAxmycCWIvHF1PRUPNyXqBRBUOp4Nr7HpI,2314
comtypes/test/test_word.py,sha256=-w-wNEIAn1RD_8uqg7nTCozTTNsVKrOLg7rdCOZ-7kM,2364
comtypes/test/urlhist.tlb,sha256=u2RrUrgUD3R-sPRP8iBa6I3FcM4AyrbgCro64vSGZoU,6480
comtypes/tools/__init__.py,sha256=WgkiD-7kBZcJUSMiqdSjgeRiUgPK07vyu51IJ0mbfcA,30
comtypes/tools/__pycache__/__init__.cpython-313.pyc,,
comtypes/tools/__pycache__/tlbparser.cpython-313.pyc,,
comtypes/tools/__pycache__/typedesc.cpython-313.pyc,,
comtypes/tools/__pycache__/typedesc_base.cpython-313.pyc,,
comtypes/tools/codegenerator/__init__.py,sha256=gfLcwlpIxQhRAvzMdGLuO3Oljul65m4_YVEA3HRYEhg,207
comtypes/tools/codegenerator/__pycache__/__init__.cpython-313.pyc,,
comtypes/tools/codegenerator/__pycache__/codegenerator.cpython-313.pyc,,
comtypes/tools/codegenerator/__pycache__/comments.cpython-313.pyc,,
comtypes/tools/codegenerator/__pycache__/heads.cpython-313.pyc,,
comtypes/tools/codegenerator/__pycache__/helpers.cpython-313.pyc,,
comtypes/tools/codegenerator/__pycache__/modulenamer.cpython-313.pyc,,
comtypes/tools/codegenerator/__pycache__/namespaces.cpython-313.pyc,,
comtypes/tools/codegenerator/__pycache__/packing.cpython-313.pyc,,
comtypes/tools/codegenerator/__pycache__/typeannotator.cpython-313.pyc,,
comtypes/tools/codegenerator/codegenerator.py,sha256=2Rm8NSzzaZQAvlkGLdpoV_n2WF2xh4z_XOnvB3sQLME,30220
comtypes/tools/codegenerator/comments.py,sha256=fXsCMJ8PvtquYXpKX63z-QgUYz_qiQv90Gp9JArvshc,3615
comtypes/tools/codegenerator/heads.py,sha256=VeytLN3fP8eRDADl1o6aHOriVfZwoEu7XF64465AOi4,8387
comtypes/tools/codegenerator/helpers.py,sha256=3z2K1cSk-PUDPEkDymtJ6qRdrLXlvtU2wj4Q2KHOA5c,12866
comtypes/tools/codegenerator/modulenamer.py,sha256=PMvrGAL6U-BbkfvnbI1X_Vv4_Tj7S5TJRLKfDcKTCYs,762
comtypes/tools/codegenerator/namespaces.py,sha256=Wwk0DGYQ9YUoDcHrlmhkIaTGXcSeOg-zTvjkwrnp94E,10758
comtypes/tools/codegenerator/packing.py,sha256=ITo5YWkEQLgOcp5OdUCdSZj9AHl4Fq-pEf0tWddHxok,2458
comtypes/tools/codegenerator/typeannotator.py,sha256=BBLgSAksZyc_aR6znPe18AyBuCsTIjVGnJyUOiQFNT4,14440
comtypes/tools/tlbparser.py,sha256=_dGqBsLNoo7Jv5__FxN8-aIkeg8HDPkhsADCPsdIDK4,30800
comtypes/tools/typedesc.py,sha256=vAtrL7IH-oSjfTlEWfJaUbY-lB2axV1nqpI7Ji8lNrA,6815
comtypes/tools/typedesc_base.py,sha256=GU6zgvDDQF34VcHZZ5sL-WiHPF0o7KJ9I2X-n4EyVg8,6447
comtypes/typeinfo.py,sha256=qtY_VSHF1T4f8hMDAFRMyoIOptvMhHa_ZkhlcvPO4gI,45144
comtypes/util.py,sha256=-hV65ZAaBN_KXFR4h-bj2PMPkSqhtHfQ9pXK1nNFwDU,3766
comtypes/viewobject.py,sha256=LhV4hJUwPWxBX3N6qM1YzAKYArpFF7vxGV631zeGRcY,7044
