#!/usr/bin/env python3
"""
🎮 GAME MEMORY OFFSETS
=====================
Memory offsets for different games
These need to be updated regularly as games update
"""

from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class GameOffsets:
    """Game memory offsets structure"""
    # Process info
    process_name: str
    module_name: str
    
    # Base addresses
    player_base: int
    local_player: int
    entity_list: int
    
    # Player structure
    player_size: int
    max_players: int
    
    # Position offsets
    pos_x: int
    pos_y: int
    pos_z: int
    
    # Player data offsets
    health: int
    team: int
    dormant: int
    
    # View angles
    view_angles: int
    
    # Weapon data
    active_weapon: int
    weapon_id: int
    
    # Bone matrix (for head/body targeting)
    bone_matrix: int
    
    # Additional offsets
    crosshair_id: int
    flags: int

# Game offset configurations
GAME_OFFSETS: Dict[str, GameOffsets] = {
    
    # Counter-Strike: Global Offensive (Example - these are outdated)
    "csgo.exe": GameOffsets(
        process_name="csgo.exe",
        module_name="client.dll",
        
        # Base addresses (these are examples and will be outdated)
        player_base=0x10F4F04,
        local_player=0xDC14CC,
        entity_list=0x4DFFF14,
        
        # Player structure
        player_size=0x10,
        max_players=64,
        
        # Position offsets
        pos_x=0x138,
        pos_y=0x13C,
        pos_z=0x140,
        
        # Player data
        health=0x100,
        team=0xF4,
        dormant=0xED,
        
        # View angles
        view_angles=0x4D88,
        
        # Weapon
        active_weapon=0x2EF8,
        weapon_id=0x2FAA,
        
        # Bone matrix
        bone_matrix=0x26A8,
        
        # Additional
        crosshair_id=0x11838,
        flags=0x104
    ),
    
    # Valorant (Example - these are placeholders)
    "VALORANT-Win64-Shipping.exe": GameOffsets(
        process_name="VALORANT-Win64-Shipping.exe",
        module_name="VALORANT-Win64-Shipping.exe",
        
        # Base addresses (placeholders)
        player_base=0x20000000,
        local_player=0x21000000,
        entity_list=0x22000000,
        
        # Player structure
        player_size=0x200,
        max_players=10,
        
        # Position offsets
        pos_x=0x164,
        pos_y=0x168,
        pos_z=0x16C,
        
        # Player data
        health=0x170,
        team=0x174,
        dormant=0x178,
        
        # View angles
        view_angles=0x4000,
        
        # Weapon
        active_weapon=0x4100,
        weapon_id=0x4104,
        
        # Bone matrix
        bone_matrix=0x4200,
        
        # Additional
        crosshair_id=0x4300,
        flags=0x180
    ),
    
    # Apex Legends (Example - these are placeholders)
    "r5apex.exe": GameOffsets(
        process_name="r5apex.exe",
        module_name="r5apex.exe",
        
        # Base addresses (placeholders)
        player_base=0x30000000,
        local_player=0x31000000,
        entity_list=0x32000000,
        
        # Player structure
        player_size=0x300,
        max_players=60,
        
        # Position offsets
        pos_x=0x14C,
        pos_y=0x150,
        pos_z=0x154,
        
        # Player data
        health=0x440,
        team=0x448,
        dormant=0x450,
        
        # View angles
        view_angles=0x3F44,
        
        # Weapon
        active_weapon=0x1A1C,
        weapon_id=0x1A20,
        
        # Bone matrix
        bone_matrix=0xF38,
        
        # Additional
        crosshair_id=0x1A24,
        flags=0x460
    ),
    
    # Call of Duty: Warzone (Example - these are placeholders)
    "ModernWarfare.exe": GameOffsets(
        process_name="ModernWarfare.exe",
        module_name="ModernWarfare.exe",
        
        # Base addresses (placeholders)
        player_base=0x40000000,
        local_player=0x41000000,
        entity_list=0x42000000,
        
        # Player structure
        player_size=0x400,
        max_players=150,
        
        # Position offsets
        pos_x=0x1A8,
        pos_y=0x1AC,
        pos_z=0x1B0,
        
        # Player data
        health=0x1B4,
        team=0x1B8,
        dormant=0x1BC,
        
        # View angles
        view_angles=0x5000,
        
        # Weapon
        active_weapon=0x5100,
        weapon_id=0x5104,
        
        # Bone matrix
        bone_matrix=0x5200,
        
        # Additional
        crosshair_id=0x5300,
        flags=0x1C0
    ),
    
    # Generic template for unknown games
    "unknown.exe": GameOffsets(
        process_name="unknown.exe",
        module_name="unknown.exe",
        
        # Base addresses (need to be found)
        player_base=0x0,
        local_player=0x0,
        entity_list=0x0,
        
        # Player structure
        player_size=0x200,
        max_players=64,
        
        # Position offsets (common patterns)
        pos_x=0x134,
        pos_y=0x138,
        pos_z=0x13C,
        
        # Player data
        health=0x100,
        team=0x104,
        dormant=0x108,
        
        # View angles
        view_angles=0x4D88,
        
        # Weapon
        active_weapon=0x2000,
        weapon_id=0x2004,
        
        # Bone matrix
        bone_matrix=0x3000,
        
        # Additional
        crosshair_id=0x4000,
        flags=0x10C
    )
}

def get_game_offsets(process_name: str) -> GameOffsets:
    """Get offsets for a specific game"""
    return GAME_OFFSETS.get(process_name.lower(), GAME_OFFSETS["unknown.exe"])

def list_supported_games() -> list:
    """List all supported games"""
    return [offsets.process_name for offsets in GAME_OFFSETS.values() if offsets.process_name != "unknown.exe"]

def add_custom_offsets(process_name: str, offsets: GameOffsets) -> None:
    """Add custom offsets for a game"""
    GAME_OFFSETS[process_name.lower()] = offsets

# Bone IDs for different body parts (game-specific)
BONE_IDS = {
    "csgo": {
        "head": 8,
        "neck": 7,
        "chest": 6,
        "stomach": 5,
        "pelvis": 0
    },
    "valorant": {
        "head": 12,
        "neck": 11,
        "chest": 10,
        "stomach": 9,
        "pelvis": 0
    },
    "apex": {
        "head": 2,
        "neck": 1,
        "chest": 0,
        "stomach": 4,
        "pelvis": 5
    }
}

def get_bone_id(game: str, bone_name: str) -> int:
    """Get bone ID for a specific game and bone"""
    game_bones = BONE_IDS.get(game.lower(), BONE_IDS["csgo"])
    return game_bones.get(bone_name.lower(), 8)  # Default to head

if __name__ == "__main__":
    print("🎮 GAME MEMORY OFFSETS")
    print("======================")
    print("Supported games:")
    
    for game in list_supported_games():
        offsets = get_game_offsets(game)
        print(f"• {game} - Module: {offsets.module_name}")
    
    print("\n⚠️ WARNING:")
    print("These offsets are examples and will be outdated!")
    print("You need to find current offsets for each game.")
    print("Use tools like Cheat Engine to find memory addresses.")
