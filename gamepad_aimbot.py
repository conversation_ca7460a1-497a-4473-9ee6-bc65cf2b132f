#!/usr/bin/env python3
"""
🎮 REMOTE GAMEPAD AIMBOT
=======================
Uses virtual Xbox controller for remote gamepad input
Perfect for console-style gaming with controller support
"""

import time
import threading
import keyboard
import numpy as np
import cv2
import mss
import torch
from ultralytics import YOLO
import os

try:
    import vgamepad as vg
    VGAMEPAD_AVAILABLE = True
except ImportError:
    VGAMEPAD_AVAILABLE = False
    print("❌ vgamepad not available. Install with: pip install vgamepad")

class GamepadAimbot:
    def __init__(self):
        # Core settings
        self.running = False
        self.aimbot_active = False
        self.fov_size = 400
        self.sensitivity = 0.8
        self.confidence_threshold = 0.6
        self.smoothing = 0.8
        
        # Virtual gamepad
        self.gamepad = None
        self.gamepad_initialized = False
        
        # YOLO model
        self.model = None
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # Screen capture (will be initialized in thread)
        self.sct = None
        self.screen_width = 1920
        self.screen_height = 1080
        
        # Performance tracking
        self.fps = 0
        self.detections = 0
        self.aim_accumulator_x = 0.0
        self.aim_accumulator_y = 0.0
        
        print("🎮 Remote Gamepad Aimbot initializing...")
        self.initialize_gamepad()
        self.load_model()
        
    def initialize_gamepad(self):
        """Initialize virtual Xbox 360 gamepad"""
        if not VGAMEPAD_AVAILABLE:
            print("❌ vgamepad library not available!")
            return False
            
        try:
            print("🎮 Creating virtual Xbox 360 gamepad...")
            self.gamepad = vg.VX360Gamepad()
            time.sleep(0.5)  # Give time for gamepad to register
            
            # Test gamepad by pressing and releasing a button
            self.gamepad.press_button(button=vg.XUSB_BUTTON.XUSB_GAMEPAD_A)
            self.gamepad.update()
            time.sleep(0.1)
            self.gamepad.release_button(button=vg.XUSB_BUTTON.XUSB_GAMEPAD_A)
            self.gamepad.update()
            
            self.gamepad_initialized = True
            print("✅ Virtual Xbox 360 gamepad initialized successfully!")
            print("🎮 Your system now sees a virtual Xbox controller")
            return True
            
        except Exception as e:
            print(f"❌ Gamepad initialization failed: {e}")
            print("💡 Make sure ViGEmBus driver is installed")
            return False
    
    def load_model(self):
        """Load YOLO model"""
        try:
            model_paths = [
                "AI-Aimbot-main/lib/best.pt",
                "lib/best.pt",
                "AI-Aimbot-main/lib/yolov11.pt",
                "lib/yolov11.pt"
            ]
            
            model_path = None
            for path in model_paths:
                if os.path.exists(path):
                    model_path = path
                    break
                    
            if not model_path:
                print("❌ YOLO model not found!")
                return False
                
            print(f"🧠 Loading YOLO model from: {model_path}")
            self.model = YOLO(model_path)
            self.model.to(self.device)
            print(f"✅ YOLO model loaded on {self.device}")
            return True
            
        except Exception as e:
            print(f"❌ Model loading failed: {e}")
            return False
    
    def move_right_stick(self, x, y):
        """Move right analog stick (aim stick) on virtual gamepad"""
        if not self.gamepad_initialized or not self.gamepad:
            return False
            
        try:
            # Apply smoothing and accumulation
            self.aim_accumulator_x += x * self.smoothing
            self.aim_accumulator_y += y * self.smoothing
            
            # Clamp values to valid range (-1.0 to 1.0)
            stick_x = max(-1.0, min(1.0, self.aim_accumulator_x))
            stick_y = max(-1.0, min(1.0, self.aim_accumulator_y))
            
            # Apply decay to accumulator for natural feel
            self.aim_accumulator_x *= 0.95
            self.aim_accumulator_y *= 0.95
            
            # Move right stick (aiming stick)
            self.gamepad.right_joystick_float(x_value_float=stick_x, y_value_float=-stick_y)
            self.gamepad.update()
            
            return True
            
        except Exception as e:
            print(f"❌ Gamepad stick movement failed: {e}")
            return False
    
    def press_trigger(self, trigger='right', value=1.0):
        """Press left or right trigger"""
        if not self.gamepad_initialized or not self.gamepad:
            return False
            
        try:
            if trigger == 'right':
                self.gamepad.right_trigger_float(value_float=value)
            else:
                self.gamepad.left_trigger_float(value_float=value)
            self.gamepad.update()
            return True
        except Exception as e:
            print(f"❌ Trigger press failed: {e}")
            return False
    
    def release_trigger(self, trigger='right'):
        """Release left or right trigger"""
        return self.press_trigger(trigger, 0.0)
    
    def get_screen_region(self):
        """Capture screen region around crosshair"""
        # Initialize mss in thread if not already done
        if self.sct is None:
            self.sct = mss.mss()

        center_x = self.screen_width // 2
        center_y = self.screen_height // 2

        # Define FOV region
        left = center_x - self.fov_size // 2
        top = center_y - self.fov_size // 2
        width = self.fov_size
        height = self.fov_size

        # Capture screen region
        region = {
            'left': left,
            'top': top,
            'width': width,
            'height': height
        }

        screenshot = self.sct.grab(region)
        frame = np.array(screenshot)
        frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2RGB)

        return frame, left, top
    
    def detect_targets(self, frame):
        """Detect targets using YOLO"""
        if not self.model:
            return []
            
        try:
            results = self.model(frame, verbose=False)
            targets = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        confidence = box.conf[0].item()
                        if confidence >= self.confidence_threshold:
                            x1, y1, x2, y2 = box.xyxy[0].tolist()
                            center_x = (x1 + x2) / 2
                            center_y = (y1 + y2) / 2
                            targets.append({
                                'x': center_x,
                                'y': center_y,
                                'confidence': confidence,
                                'bbox': [x1, y1, x2, y2]
                            })
            
            self.detections = len(targets)
            return targets
            
        except Exception as e:
            print(f"❌ Detection failed: {e}")
            return []
    
    def find_best_target(self, targets):
        """Find the best target to aim at"""
        if not targets:
            return None
            
        # Find closest target to center
        center_x = self.fov_size / 2
        center_y = self.fov_size / 2
        
        best_target = None
        min_distance = float('inf')
        
        for target in targets:
            dx = target['x'] - center_x
            dy = target['y'] - center_y
            distance = np.sqrt(dx*dx + dy*dy)
            
            if distance < min_distance:
                min_distance = distance
                best_target = target
                
        return best_target
    
    def aim_at_target(self, target, region_left, region_top):
        """Aim at target using gamepad right stick"""
        if not target or not self.aimbot_active:
            return
            
        # Calculate aim offset
        target_x = target['x'] + region_left
        target_y = target['y'] + region_top
        
        screen_center_x = self.screen_width / 2
        screen_center_y = self.screen_height / 2
        
        dx = target_x - screen_center_x
        dy = target_y - screen_center_y
        
        # Convert to gamepad stick values (-1.0 to 1.0)
        max_movement = 200.0  # Maximum pixel distance for full stick deflection
        
        stick_x = max(-1.0, min(1.0, dx / max_movement))
        stick_y = max(-1.0, min(1.0, dy / max_movement))
        
        # Apply sensitivity
        stick_x *= self.sensitivity
        stick_y *= self.sensitivity
        
        # Move right stick if significant movement needed
        if abs(stick_x) > 0.05 or abs(stick_y) > 0.05:
            self.move_right_stick(stick_x, stick_y)
    
    def aimbot_loop(self):
        """Main aimbot loop"""
        print("🎮 Gamepad Aimbot loop started")
        frame_count = 0
        start_time = time.time()
        error_count = 0

        while self.running:
            try:
                # Capture screen
                frame, region_left, region_top = self.get_screen_region()

                # Detect targets
                targets = self.detect_targets(frame)

                # Aim at best target
                if targets and self.aimbot_active:
                    best_target = self.find_best_target(targets)
                    if best_target:
                        self.aim_at_target(best_target, region_left, region_top)

                # Calculate FPS
                frame_count += 1
                if frame_count % 30 == 0:
                    elapsed = time.time() - start_time
                    self.fps = 30 / elapsed if elapsed > 0 else 0
                    start_time = time.time()
                    print(f"🎮 FPS: {self.fps:.1f} | Targets: {self.detections} | Active: {self.aimbot_active}")

                # Reset error count on successful iteration
                error_count = 0
                time.sleep(0.001)  # Small delay

            except Exception as e:
                error_count += 1
                if error_count <= 3:  # Only show first few errors
                    print(f"❌ Aimbot loop error: {e}")
                elif error_count == 4:
                    print("❌ Multiple errors detected, suppressing further error messages...")

                # Reinitialize screen capture on error
                self.sct = None
                time.sleep(0.1)
    
    def toggle_aimbot(self):
        """Toggle aimbot activation"""
        self.aimbot_active = not self.aimbot_active
        status = "🔥 ACTIVE" if self.aimbot_active else "💤 INACTIVE"
        print(f"🎮 GAMEPAD AIMBOT {status}")
        
        if self.aimbot_active:
            print("🎮 Aimbot will now move the right stick to aim at targets!")
        else:
            print("🕹️  Right stick control returned to normal!")
    
    def start(self):
        """Start the gamepad aimbot"""
        if not self.gamepad_initialized:
            print("❌ Cannot start - Virtual gamepad not initialized")
            return
            
        if not self.model:
            print("❌ Cannot start - YOLO model not loaded")
            return
            
        print("🚀 Starting Remote Gamepad Aimbot...")
        print("🎮 Controls:")
        print("   F1: Toggle aimbot ON/OFF")
        print("   F2: Quit")
        print("   F3: Test right trigger")
        print("⚠️  AIMBOT STARTS INACTIVE - Press F1 to activate!")
        print("🎮 Your game should now detect an Xbox controller")
        
        self.running = True
        
        # Start aimbot thread
        aimbot_thread = threading.Thread(target=self.aimbot_loop, daemon=True)
        aimbot_thread.start()
        
        # Hotkey handlers
        keyboard.add_hotkey('f1', self.toggle_aimbot)
        keyboard.add_hotkey('f2', self.stop)
        keyboard.add_hotkey('f3', self.test_trigger)
        
        try:
            keyboard.wait('f2')  # Wait for F2 to quit
        except KeyboardInterrupt:
            self.stop()
    
    def test_trigger(self):
        """Test right trigger press"""
        print("🎮 Testing right trigger...")
        self.press_trigger('right', 1.0)
        time.sleep(0.1)
        self.release_trigger('right')
        print("✅ Right trigger test complete")
    
    def stop(self):
        """Stop the gamepad aimbot"""
        print("🛑 Stopping Remote Gamepad Aimbot...")
        self.running = False
        self.aimbot_active = False

        # Reset gamepad to neutral state
        if self.gamepad:
            try:
                self.gamepad.reset()
                self.gamepad.update()
                print("🎮 Virtual gamepad reset to neutral state")
            except:
                pass

        # Clean up screen capture
        if self.sct:
            try:
                self.sct.close()
                self.sct = None
            except:
                pass

if __name__ == "__main__":
    print("🎮 REMOTE GAMEPAD AIMBOT")
    print("========================")
    print("Uses virtual Xbox controller for console-style aiming")
    print("Perfect for games that support gamepad input")
    print()
    
    aimbot = GamepadAimbot()
    aimbot.start()
