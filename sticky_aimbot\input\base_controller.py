#!/usr/bin/env python3
"""
🎮 BASE CONTROLLER
==================
Base class and data structures for input control systems.
"""

import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class AimAdjustment:
    """Aim adjustment data structure"""
    delta_x: float
    delta_y: float
    confidence: float = 1.0
    timestamp: float = 0.0
    
    def __post_init__(self):
        if self.timestamp == 0.0:
            self.timestamp = time.time()

class BaseController(ABC):
    """Base class for all input controllers"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the controller"""
        self.config = config or {}
        self.enabled = True
        self.last_action_time = 0.0
        self.action_count = 0
    
    @abstractmethod
    def apply_aim_adjustment(self, adjustment: AimAdjustment) -> bool:
        """Apply aim adjustment"""
        pass
    
    def is_enabled(self) -> bool:
        """Check if controller is enabled"""
        return self.enabled
    
    def enable(self):
        """Enable the controller"""
        self.enabled = True
    
    def disable(self):
        """Disable the controller"""
        self.enabled = False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get controller statistics"""
        return {
            'enabled': self.enabled,
            'action_count': self.action_count,
            'last_action_time': self.last_action_time
        }
    
    def reset(self):
        """Reset controller state"""
        pass
    
    def cleanup(self):
        """Cleanup controller resources"""
        pass
