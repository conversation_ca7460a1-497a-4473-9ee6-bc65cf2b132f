#!/usr/bin/env python3
"""
📊 PERFORMANCE MONITOR
======================
Performance monitoring and optimization for the aimbot system.
"""

import time
import psutil
from typing import Dict, Any, List
from collections import deque

class PerformanceMonitor:
    """Monitors and tracks aimbot performance metrics"""
    
    def __init__(self):
        """Initialize performance monitor"""
        self.frame_times = deque(maxlen=100)
        self.cpu_usage = deque(maxlen=50)
        self.memory_usage = deque(maxlen=50)
        
        self.start_time = time.time()
        self.frame_count = 0
        self.last_update = time.time()
    
    def record_frame_time(self, frame_time: float):
        """Record frame processing time"""
        self.frame_times.append(frame_time)
        self.frame_count += 1
    
    def update_system_metrics(self):
        """Update system performance metrics"""
        current_time = time.time()
        
        # Update every second
        if current_time - self.last_update >= 1.0:
            # CPU usage
            cpu_percent = psutil.cpu_percent()
            self.cpu_usage.append(cpu_percent)
            
            # Memory usage
            memory_info = psutil.virtual_memory()
            self.memory_usage.append(memory_info.percent)
            
            self.last_update = current_time
    
    def get_fps(self) -> float:
        """Get current FPS"""
        if len(self.frame_times) < 10:
            return 0.0
        
        recent_times = list(self.frame_times)[-10:]
        avg_time = sum(recent_times) / len(recent_times)
        return 1.0 / avg_time if avg_time > 0 else 0.0
    
    def get_avg_frame_time(self) -> float:
        """Get average frame time"""
        if not self.frame_times:
            return 0.0
        return sum(self.frame_times) / len(self.frame_times)
    
    def get_cpu_usage(self) -> float:
        """Get current CPU usage"""
        return self.cpu_usage[-1] if self.cpu_usage else 0.0
    
    def get_memory_usage(self) -> float:
        """Get current memory usage"""
        return self.memory_usage[-1] if self.memory_usage else 0.0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        return {
            'fps': self.get_fps(),
            'avg_frame_time': self.get_avg_frame_time(),
            'cpu_usage': self.get_cpu_usage(),
            'memory_usage': self.get_memory_usage(),
            'total_frames': self.frame_count,
            'uptime': time.time() - self.start_time
        }
    
    def cleanup(self):
        """Cleanup performance monitor"""
        pass
