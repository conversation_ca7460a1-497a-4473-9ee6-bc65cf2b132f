#!/usr/bin/env python3
"""
🎯 BACK 4 BLOOD AIMBOT SUITE - COMPLETE SYSTEM
==============================================
Advanced aimbot suite specifically designed for Back 4 Blood with full memory access,
multiple aim modes, ESP, trigger bot, and comprehensive anti-detection features.

⚠️  EDUCATIONAL PURPOSE ONLY ⚠️
This software is for educational and research purposes only.
"""

import sys
import os
import time
import math
import random
import threading
import logging
import struct
import ctypes
from ctypes import wintypes
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from collections import deque
from enum import Enum
import json

# GUI imports
import tkinter as tk
from tkinter import ttk, messagebox, filedialog

# Computer Vision imports
try:
    import cv2
    import numpy as np
    CV_AVAILABLE = True
except ImportError:
    CV_AVAILABLE = False
    print("⚠️ OpenCV not available - installing...")
    os.system("pip install opencv-python")

# AI Detection imports
try:
    from ultralytics import YOLO
    import torch
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    print("⚠️ AI libraries not available - installing...")
    os.system("pip install ultralytics torch")

# System imports
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("⚠️ psutil not available - installing...")
    os.system("pip install psutil")

# Windows API imports
try:
    import win32api
    import win32con
    import win32gui
    import win32process
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("⚠️ Win32 API not available - installing...")
    os.system("pip install pywin32")

# Educational warning
EDUCATIONAL_WARNING = """
🎯 BACK 4 BLOOD AIMBOT SUITE - EDUCATIONAL SOFTWARE
===================================================

⚠️  CRITICAL EDUCATIONAL NOTICE ⚠️

This comprehensive aimbot suite is provided for EDUCATIONAL and RESEARCH purposes only.

FEATURES INCLUDED FOR BACK 4 BLOOD:
🎯 Advanced Sticky Aim with Zombie Tracking
🧟 Zombie-Specific Detection & Prioritization
💾 Memory Reading & Injection for B4B
🔍 Multi-Method Detection (CV, AI, Memory, Hybrid)
🎮 Advanced Input Simulation & Humanization
🛡️ Comprehensive Anti-Detection System
📊 Real-Time Performance Monitoring
⚙️ Extensive Configuration Options
🎨 ESP Overlay with Zombie Information
🔧 Game-Specific Optimizations
📈 Advanced Analytics & Statistics
🔫 Intelligent Trigger Bot
🎪 Multiple Aim Modes (Sticky, Snap, Smooth, Rage, Legit)

BACK 4 BLOOD SPECIFIC CAPABILITIES:
🧟 Zombie Detection & Classification
🎯 Weak Point Targeting (Head, Heart, etc.)
🔥 Special Infected Priority System
💀 Boss Enemy Tracking
🏃 Movement Prediction for Fast Zombies
🎪 Horde Mode Optimization
🔫 Weapon-Specific Recoil Patterns
🛡️ Team Damage Prevention
📍 Objective-Based Targeting
🎮 Campaign & Swarm Mode Support

PERMITTED USES:
✅ Learning about computer vision, AI, and memory manipulation
✅ Understanding game mechanics and reverse engineering
✅ Research into human-computer interaction
✅ Educational demonstrations and tutorials
✅ Offline testing and experimentation
✅ Security research and vulnerability assessment

PROHIBITED USES:
❌ Online competitive gaming
❌ Violating game Terms of Service
❌ Gaining unfair advantages in multiplayer games
❌ Commercial use without proper licensing
❌ Malicious or harmful activities
❌ Circumventing anti-cheat systems

LEGAL DISCLAIMER:
The authors and contributors are not responsible for any misuse
of this software. Users are solely responsible for ensuring their
use complies with applicable laws and regulations.

By using this software, you acknowledge that you understand and
agree to these terms and will use it responsibly for educational purposes only.
"""

class AimMode(Enum):
    """Different aimbot modes optimized for Back 4 Blood"""
    STICKY = "sticky"           # Locks onto zombies until eliminated
    SNAP = "snap"              # Instant snap to nearest zombie
    SMOOTH = "smooth"          # Smooth tracking for natural movement
    RAGE = "rage"              # Maximum aggression for horde mode
    LEGIT = "legit"            # Human-like aiming
    TRIGGER = "trigger"        # Auto-fire when on zombie
    FLICK = "flick"            # Quick flick shots
    PREDICTION = "prediction"   # Predictive aiming for moving zombies
    HORDE = "horde"            # Optimized for horde encounters
    BOSS = "boss"              # Specialized for boss enemies

class ZombieType(Enum):
    """Back 4 Blood zombie types"""
    COMMON = "common"          # Regular zombies
    RIDDEN = "ridden"          # Special infected
    TALLBOY = "tallboy"        # Tallboy variants
    STINGER = "stinger"        # Stinger variants
    REEKER = "reeker"          # Reeker variants
    OGRE = "ogre"              # Boss: Ogre
    HOCKERS = "hockers"        # Hocker variants
    SLEEPER = "sleeper"        # Sleeper zombies
    BREAKER = "breaker"        # Breaker boss
    ABOMINATION = "abomination" # Abomination boss

@dataclass
class ZombieTarget:
    """Enhanced zombie target data structure"""
    id: str
    x: float
    y: float
    z: float = 0.0
    width: float = 50.0
    height: float = 50.0
    confidence: float = 1.0
    detection_method: str = "unknown"
    zombie_type: ZombieType = ZombieType.COMMON
    timestamp: float = field(default_factory=time.time)

    # Zombie-specific data
    health: float = 100.0
    armor: float = 0.0
    is_special: bool = False
    is_boss: bool = False
    threat_level: float = 1.0
    distance: float = 0.0
    angle: float = 0.0

    # Movement data
    velocity_x: float = 0.0
    velocity_y: float = 0.0
    velocity_z: float = 0.0
    acceleration_x: float = 0.0
    acceleration_y: float = 0.0
    acceleration_z: float = 0.0

    # Tracking data
    lock_priority: float = 0.0
    lock_duration: float = 0.0
    hit_probability: float = 0.0
    weak_points: List[Tuple[float, float, float]] = field(default_factory=list)

    # Bone positions for precise targeting
    head_pos: Optional[Tuple[float, float, float]] = None
    chest_pos: Optional[Tuple[float, float, float]] = None
    heart_pos: Optional[Tuple[float, float, float]] = None
    weak_spot_pos: Optional[Tuple[float, float, float]] = None

class Back4BloodMemoryManager:
    """Advanced memory manager specifically for Back 4 Blood"""

    def __init__(self):
        """Initialize Back 4 Blood memory manager"""
        self.process_handle = None
        self.process_id = None
        self.base_address = None
        self.module_addresses = {}

        # Back 4 Blood specific offsets (these would be found through reverse engineering)
        self.offsets = {
            # Player data
            'local_player': 0x2A5B8C0,
            'player_position': 0x1A0,
            'player_angles': 0x1B4,
            'player_health': 0x1C8,
            'player_team': 0x1DC,

            # Zombie data
            'zombie_list': 0x2B7F4A0,
            'zombie_count': 0x2B7F4A4,
            'zombie_size': 0x3F8,
            'zombie_position': 0x164,
            'zombie_health': 0x178,
            'zombie_type': 0x18C,
            'zombie_state': 0x1A0,

            # Weapon data
            'current_weapon': 0x2C8,
            'weapon_recoil': 0x2DC,
            'ammo_count': 0x2F0,

            # Game state
            'game_mode': 0x3A5C7B0,
            'round_state': 0x3A5C7B4,
            'horde_active': 0x3A5C7B8,

            # View matrix for world-to-screen conversion
            'view_matrix': 0x3B8F2C0
        }

        # Windows API setup
        if WIN32_AVAILABLE:
            self.kernel32 = ctypes.windll.kernel32
            self.user32 = ctypes.windll.user32

            # Process access rights
            self.PROCESS_ALL_ACCESS = 0x1F0FFF
            self.PROCESS_VM_READ = 0x0010
            self.PROCESS_VM_WRITE = 0x0020
            self.PROCESS_VM_OPERATION = 0x0008

    def attach_to_back4blood(self) -> bool:
        """Attach to Back 4 Blood process"""
        if not WIN32_AVAILABLE:
            print("⚠️ Win32 API not available - memory access disabled")
            return False

        try:
            # Find Back 4 Blood process
            process_names = ['Back4Blood-Win64-Shipping.exe', 'b4b.exe', 'Back4Blood.exe']

            for proc_name in process_names:
                for proc in psutil.process_iter(['pid', 'name']):
                    if proc.info['name'].lower() == proc_name.lower():
                        self.process_id = proc.info['pid']
                        break
                if self.process_id:
                    break

            if not self.process_id:
                print("❌ Back 4 Blood process not found. Make sure the game is running.")
                return False

            # Open process handle
            self.process_handle = self.kernel32.OpenProcess(
                self.PROCESS_ALL_ACCESS, False, self.process_id
            )

            if not self.process_handle:
                print("❌ Failed to open Back 4 Blood process handle")
                return False

            # Get base address
            self.base_address = self._get_module_base_address('Back4Blood-Win64-Shipping.exe')

            if not self.base_address:
                print("❌ Failed to get Back 4 Blood base address")
                return False

            print(f"✅ Successfully attached to Back 4 Blood (PID: {self.process_id})")
            print(f"📍 Base Address: 0x{self.base_address:X}")
            return True

        except Exception as e:
            print(f"❌ Failed to attach to Back 4 Blood: {e}")
            return False

    def _get_module_base_address(self, module_name: str) -> Optional[int]:
        """Get base address of Back 4 Blood module"""
        try:
            # Get process modules
            hModuleSnap = self.kernel32.CreateToolhelp32Snapshot(0x00000008, self.process_id)
            if hModuleSnap == -1:
                return None

            # Module entry structure
            class MODULEENTRY32(ctypes.Structure):
                _fields_ = [
                    ("dwSize", wintypes.DWORD),
                    ("th32ModuleID", wintypes.DWORD),
                    ("th32ProcessID", wintypes.DWORD),
                    ("GlblcntUsage", wintypes.DWORD),
                    ("ProccntUsage", wintypes.DWORD),
                    ("modBaseAddr", ctypes.POINTER(wintypes.BYTE)),
                    ("modBaseSize", wintypes.DWORD),
                    ("hModule", wintypes.HMODULE),
                    ("szModule", ctypes.c_char * 256),
                    ("szExePath", ctypes.c_char * 260)
                ]

            me32 = MODULEENTRY32()
            me32.dwSize = ctypes.sizeof(MODULEENTRY32)

            # Get first module
            if self.kernel32.Module32First(hModuleSnap, ctypes.byref(me32)):
                while True:
                    module_name_decoded = me32.szModule.decode('utf-8', errors='ignore')
                    if module_name.lower() in module_name_decoded.lower():
                        self.kernel32.CloseHandle(hModuleSnap)
                        return ctypes.cast(me32.modBaseAddr, ctypes.c_void_p).value

                    if not self.kernel32.Module32Next(hModuleSnap, ctypes.byref(me32)):
                        break

            self.kernel32.CloseHandle(hModuleSnap)
            return None

        except Exception as e:
            print(f"❌ Failed to get module base address: {e}")
            return None

    def read_memory(self, address: int, size: int) -> Optional[bytes]:
        """Read memory from Back 4 Blood process"""
        if not self.process_handle:
            return None

        try:
            buffer = ctypes.create_string_buffer(size)
            bytes_read = ctypes.c_size_t()

            success = self.kernel32.ReadProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                buffer,
                size,
                ctypes.byref(bytes_read)
            )

            if success and bytes_read.value == size:
                return buffer.raw

            return None

        except Exception as e:
            print(f"❌ Memory read error: {e}")
            return None

    def write_memory(self, address: int, data: bytes) -> bool:
        """Write memory to Back 4 Blood process"""
        if not self.process_handle:
            return False

        try:
            bytes_written = ctypes.c_size_t()

            success = self.kernel32.WriteProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                data,
                len(data),
                ctypes.byref(bytes_written)
            )

            return success and bytes_written.value == len(data)

        except Exception as e:
            print(f"❌ Memory write error: {e}")
            return False

    def read_int(self, address: int) -> Optional[int]:
        """Read 32-bit integer"""
        data = self.read_memory(address, 4)
        return struct.unpack('<I', data)[0] if data else None

    def read_float(self, address: int) -> Optional[float]:
        """Read 32-bit float"""
        data = self.read_memory(address, 4)
        return struct.unpack('<f', data)[0] if data else None

    def read_vector3(self, address: int) -> Optional[Tuple[float, float, float]]:
        """Read 3D vector (3 floats)"""
        data = self.read_memory(address, 12)
        if data:
            x, y, z = struct.unpack('<fff', data)
            return (x, y, z)
        return None

    def write_float(self, address: int, value: float) -> bool:
        """Write 32-bit float"""
        data = struct.pack('<f', value)
        return self.write_memory(address, data)

    def write_vector3(self, address: int, x: float, y: float, z: float) -> bool:
        """Write 3D vector"""
        data = struct.pack('<fff', x, y, z)
        return self.write_memory(address, data)

    def get_local_player_data(self) -> Dict[str, Any]:
        """Get local player data from memory"""
        if not self.base_address:
            return {}

        try:
            local_player_addr = self.base_address + self.offsets['local_player']

            # Read player data
            position = self.read_vector3(local_player_addr + self.offsets['player_position'])
            angles = self.read_vector3(local_player_addr + self.offsets['player_angles'])
            health = self.read_float(local_player_addr + self.offsets['player_health'])
            team = self.read_int(local_player_addr + self.offsets['player_team'])

            return {
                'position': position or (0, 0, 0),
                'angles': angles or (0, 0, 0),
                'health': health or 100.0,
                'team': team or 0,
                'timestamp': time.time()
            }

        except Exception as e:
            print(f"❌ Failed to read local player data: {e}")
            return {}

    def get_zombie_list(self) -> List[ZombieTarget]:
        """Get list of zombies from memory"""
        if not self.base_address:
            return []

        try:
            zombies = []
            zombie_list_addr = self.base_address + self.offsets['zombie_list']
            zombie_count = self.read_int(self.base_address + self.offsets['zombie_count'])

            if not zombie_count or zombie_count > 200:  # Sanity check
                return []

            for i in range(min(zombie_count, 100)):  # Limit to 100 zombies
                zombie_addr = zombie_list_addr + (i * self.offsets['zombie_size'])

                # Read zombie data
                position = self.read_vector3(zombie_addr + self.offsets['zombie_position'])
                health = self.read_float(zombie_addr + self.offsets['zombie_health'])
                zombie_type_id = self.read_int(zombie_addr + self.offsets['zombie_type'])
                zombie_state = self.read_int(zombie_addr + self.offsets['zombie_state'])

                if not position or not health or health <= 0:
                    continue

                # Determine zombie type
                zombie_type = self._get_zombie_type(zombie_type_id)

                # Calculate threat level
                threat_level = self._calculate_threat_level(zombie_type, health)

                # Create zombie target
                zombie = ZombieTarget(
                    id=f"zombie_{i}",
                    x=position[0],
                    y=position[1],
                    z=position[2],
                    health=health,
                    zombie_type=zombie_type,
                    is_special=zombie_type != ZombieType.COMMON,
                    is_boss=zombie_type in [ZombieType.OGRE, ZombieType.BREAKER, ZombieType.ABOMINATION],
                    threat_level=threat_level,
                    detection_method='memory'
                )

                zombies.append(zombie)

            return zombies

        except Exception as e:
            print(f"❌ Failed to read zombie list: {e}")
            return []

    def _get_zombie_type(self, type_id: int) -> ZombieType:
        """Convert zombie type ID to ZombieType enum"""
        zombie_type_map = {
            0: ZombieType.COMMON,
            1: ZombieType.RIDDEN,
            2: ZombieType.TALLBOY,
            3: ZombieType.STINGER,
            4: ZombieType.REEKER,
            5: ZombieType.OGRE,
            6: ZombieType.HOCKERS,
            7: ZombieType.SLEEPER,
            8: ZombieType.BREAKER,
            9: ZombieType.ABOMINATION
        }
        return zombie_type_map.get(type_id, ZombieType.COMMON)

    def _calculate_threat_level(self, zombie_type: ZombieType, health: float) -> float:
        """Calculate threat level based on zombie type and health"""
        base_threat = {
            ZombieType.COMMON: 1.0,
            ZombieType.RIDDEN: 2.0,
            ZombieType.TALLBOY: 3.0,
            ZombieType.STINGER: 2.5,
            ZombieType.REEKER: 2.5,
            ZombieType.OGRE: 5.0,
            ZombieType.HOCKERS: 2.0,
            ZombieType.SLEEPER: 1.5,
            ZombieType.BREAKER: 5.0,
            ZombieType.ABOMINATION: 6.0
        }

        threat = base_threat.get(zombie_type, 1.0)

        # Adjust for health
        if health > 200:
            threat *= 1.5
        elif health > 100:
            threat *= 1.2

        return threat

    def inject_aim_angles(self, pitch: float, yaw: float) -> bool:
        """Inject aim angles into Back 4 Blood"""
        if not self.base_address:
            return False

        try:
            local_player_addr = self.base_address + self.offsets['local_player']
            angles_addr = local_player_addr + self.offsets['player_angles']

            # Clamp angles
            pitch = max(-89, min(89, pitch))
            yaw = self._normalize_angle(yaw)

            # Write new angles (keep roll unchanged)
            current_angles = self.read_vector3(angles_addr)
            if current_angles:
                roll = current_angles[2]
                return self.write_vector3(angles_addr, pitch, yaw, roll)

            return False

        except Exception as e:
            print(f"❌ Aim injection error: {e}")
            return False

    def _normalize_angle(self, angle: float) -> float:
        """Normalize angle to [-180, 180] range"""
        while angle > 180:
            angle -= 360
        while angle < -180:
            angle += 360
        return angle

    def cleanup(self):
        """Cleanup memory manager"""
        if self.process_handle and WIN32_AVAILABLE:
            self.kernel32.CloseHandle(self.process_handle)
            self.process_handle = None
            print("🧹 Back 4 Blood memory manager cleaned up")

class Back4BloodAimEngine:
    """Advanced aimbot engine specifically designed for Back 4 Blood"""

    def __init__(self, memory_manager: Back4BloodMemoryManager):
        """Initialize Back 4 Blood aim engine"""
        self.memory = memory_manager

        # Aim configuration
        self.aim_mode = AimMode.SMOOTH
        self.target_bone = "head"
        self.fov = 60.0  # Larger FOV for zombie hordes
        self.smoothing = 3.0  # Faster for zombie combat
        self.sensitivity = 1.2

        # Back 4 Blood specific settings
        self.zombie_priority = {
            ZombieType.ABOMINATION: 10.0,
            ZombieType.BREAKER: 9.0,
            ZombieType.OGRE: 8.0,
            ZombieType.TALLBOY: 7.0,
            ZombieType.STINGER: 6.0,
            ZombieType.REEKER: 6.0,
            ZombieType.HOCKERS: 5.0,
            ZombieType.RIDDEN: 4.0,
            ZombieType.SLEEPER: 3.0,
            ZombieType.COMMON: 1.0
        }

        # Advanced features
        self.weak_point_targeting = True
        self.horde_mode_optimization = True
        self.boss_mode_enabled = True
        self.team_damage_prevention = True
        self.prediction_enabled = True
        self.humanization_enabled = True

        # State tracking
        self.current_target = None
        self.lock_start_time = 0.0
        self.zombies_killed = 0
        self.shots_fired = 0
        self.hits_landed = 0
        self.headshots = 0

        # Performance tracking
        self.aim_adjustments = 0
        self.successful_locks = 0
        self.average_lock_time = 0.0
        self.horde_encounters = 0

        print("🧟 Back 4 Blood Aim Engine initialized")

    def update(self) -> Optional[Dict[str, Any]]:
        """Main update function for Back 4 Blood aimbot"""
        try:
            # Get game data
            player_data = self.memory.get_local_player_data()
            zombies = self.memory.get_zombie_list()

            if not player_data or not zombies:
                return None

            # Filter valid targets
            valid_zombies = self._filter_valid_targets(zombies, player_data)

            if not valid_zombies:
                if self.current_target:
                    self._release_target()
                return None

            # Select target based on aim mode and priorities
            target = self._select_optimal_target(valid_zombies, player_data)

            if not target:
                return None

            # Calculate aim adjustment
            adjustment = self._calculate_aim_adjustment(target, player_data)

            if adjustment:
                # Apply Back 4 Blood specific modifications
                adjustment = self._apply_b4b_modifications(adjustment, target)

                # Inject aim if enabled
                if self._inject_aim(adjustment, player_data):
                    self.aim_adjustments += 1

                    return {
                        'target': target,
                        'adjustment': adjustment,
                        'player_data': player_data,
                        'zombie_count': len(zombies),
                        'valid_targets': len(valid_zombies)
                    }

            return None

        except Exception as e:
            print(f"❌ Aim engine update error: {e}")
            return None

    def _filter_valid_targets(self, zombies: List[ZombieTarget],
                            player_data: Dict[str, Any]) -> List[ZombieTarget]:
        """Filter zombies to valid targets"""
        valid_targets = []
        player_pos = player_data.get('position', (0, 0, 0))

        for zombie in zombies:
            # Skip dead zombies
            if zombie.health <= 0:
                continue

            # Calculate distance
            distance = math.sqrt(
                (zombie.x - player_pos[0])**2 +
                (zombie.y - player_pos[1])**2 +
                (zombie.z - player_pos[2])**2
            )
            zombie.distance = distance

            # Skip zombies too far away
            max_distance = 1000.0 if zombie.is_boss else 500.0
            if distance > max_distance:
                continue

            # Calculate angle to zombie
            dx = zombie.x - player_pos[0]
            dy = zombie.y - player_pos[1]
            angle = math.degrees(math.atan2(dy, dx))
            zombie.angle = angle

            # Check FOV
            player_angles = player_data.get('angles', (0, 0, 0))
            angle_diff = abs(self._normalize_angle(angle - player_angles[1]))

            fov_limit = self.fov / 2
            if zombie.is_boss:
                fov_limit *= 1.5  # Larger FOV for bosses

            if angle_diff <= fov_limit:
                valid_targets.append(zombie)

        return valid_targets

    def _select_optimal_target(self, zombies: List[ZombieTarget],
                             player_data: Dict[str, Any]) -> Optional[ZombieTarget]:
        """Select optimal zombie target based on mode and priorities"""
        if not zombies:
            return None

        # Calculate priorities for all zombies
        for zombie in zombies:
            zombie.lock_priority = self._calculate_zombie_priority(zombie, player_data)

        # Select based on aim mode
        if self.aim_mode == AimMode.STICKY:
            return self._select_sticky_target(zombies)
        elif self.aim_mode == AimMode.HORDE:
            return self._select_horde_target(zombies)
        elif self.aim_mode == AimMode.BOSS:
            return self._select_boss_target(zombies)
        elif self.aim_mode == AimMode.RAGE:
            return self._select_rage_target(zombies)
        else:
            return self._select_highest_priority_target(zombies)

    def _calculate_zombie_priority(self, zombie: ZombieTarget,
                                 player_data: Dict[str, Any]) -> float:
        """Calculate priority score for zombie target"""
        priority = 0.0

        # Base priority by zombie type
        priority += self.zombie_priority.get(zombie.zombie_type, 1.0) * 2.0

        # Distance factor (closer = higher priority)
        if zombie.distance > 0:
            distance_factor = max(0.1, 1.0 - (zombie.distance / 500.0))
            priority += distance_factor * 1.5

        # Health factor (lower health = higher priority for finishing)
        if zombie.health > 0:
            health_factor = max(0.1, 1.0 - (zombie.health / 200.0))
            priority += health_factor * 1.0

        # Threat level
        priority += zombie.threat_level * 1.5

        # Special infected bonus
        if zombie.is_special:
            priority *= 1.3

        # Boss bonus
        if zombie.is_boss:
            priority *= 2.0

        # Current target bonus (sticky behavior)
        if self.current_target and zombie.id == self.current_target.id:
            priority *= 1.4

        # Angle factor (closer to crosshair = higher priority)
        player_angles = player_data.get('angles', (0, 0, 0))
        angle_diff = abs(self._normalize_angle(zombie.angle - player_angles[1]))
        angle_factor = max(0.1, 1.0 - (angle_diff / (self.fov / 2)))
        priority += angle_factor * 1.0

        return priority

    def _select_sticky_target(self, zombies: List[ZombieTarget]) -> Optional[ZombieTarget]:
        """Select target for sticky aim mode"""
        # Prefer current target if still valid and high priority
        if self.current_target:
            for zombie in zombies:
                if zombie.id == self.current_target.id and zombie.lock_priority > 3.0:
                    return zombie

        # Select highest priority target
        return max(zombies, key=lambda z: z.lock_priority)

    def _select_horde_target(self, zombies: List[ZombieTarget]) -> Optional[ZombieTarget]:
        """Select target optimized for horde encounters"""
        # Prioritize special infected in hordes
        special_zombies = [z for z in zombies if z.is_special]
        if special_zombies:
            return max(special_zombies, key=lambda z: z.lock_priority)

        # Otherwise select closest common zombie
        return min(zombies, key=lambda z: z.distance)

    def _select_boss_target(self, zombies: List[ZombieTarget]) -> Optional[ZombieTarget]:
        """Select target optimized for boss encounters"""
        # Always prioritize boss enemies
        boss_zombies = [z for z in zombies if z.is_boss]
        if boss_zombies:
            return max(boss_zombies, key=lambda z: z.lock_priority)

        # Fallback to highest priority
        return max(zombies, key=lambda z: z.lock_priority)

    def _select_rage_target(self, zombies: List[ZombieTarget]) -> Optional[ZombieTarget]:
        """Select target for rage mode (maximum aggression)"""
        # Prioritize by threat level and proximity
        return max(zombies, key=lambda z: z.threat_level * (100 / max(z.distance, 1)))

    def _select_highest_priority_target(self, zombies: List[ZombieTarget]) -> Optional[ZombieTarget]:
        """Select highest priority target"""
        return max(zombies, key=lambda z: z.lock_priority)

    def _calculate_aim_adjustment(self, target: ZombieTarget,
                                player_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Calculate aim adjustment for zombie target"""
        player_pos = player_data.get('position', (0, 0, 0))
        player_angles = player_data.get('angles', (0, 0, 0))

        # Get target position (with weak point targeting)
        target_pos = self._get_optimal_target_position(target)

        # Apply prediction if enabled
        if self.prediction_enabled:
            target_pos = self._apply_movement_prediction(target, target_pos)

        # Calculate required angles
        required_angles = self._calculate_angles_to_target(player_pos, target_pos)

        # Calculate delta
        delta_pitch = required_angles[0] - player_angles[0]
        delta_yaw = required_angles[1] - player_angles[1]

        # Normalize angles
        delta_yaw = self._normalize_angle(delta_yaw)
        delta_pitch = self._normalize_angle(delta_pitch)

        # Apply smoothing based on mode
        smoothing = self._get_mode_smoothing()
        delta_pitch /= smoothing
        delta_yaw /= smoothing

        return {
            'delta_pitch': delta_pitch,
            'delta_yaw': delta_yaw,
            'target_pos': target_pos,
            'confidence': target.confidence,
            'aim_mode': self.aim_mode,
            'target_bone': self.target_bone
        }

    def _get_optimal_target_position(self, target: ZombieTarget) -> Tuple[float, float, float]:
        """Get optimal target position based on weak point targeting"""
        if not self.weak_point_targeting:
            return (target.x, target.y, target.z)

        # Prioritize weak points based on zombie type
        if target.zombie_type == ZombieType.TALLBOY and target.weak_spot_pos:
            return target.weak_spot_pos  # Tallboy weak spot on arm
        elif target.zombie_type == ZombieType.STINGER and target.head_pos:
            return target.head_pos  # Stinger head
        elif target.zombie_type == ZombieType.REEKER and target.chest_pos:
            return target.chest_pos  # Reeker chest weak spot
        elif target.is_boss and target.weak_spot_pos:
            return target.weak_spot_pos  # Boss weak spots
        elif target.head_pos and self.target_bone == "head":
            return target.head_pos  # General headshots
        elif target.chest_pos and self.target_bone == "chest":
            return target.chest_pos  # Chest shots
        else:
            # Default to center mass
            return (target.x, target.y, target.z + 20)  # Slightly higher for better hit chance