#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

bugreport:
{
	btn_continue = "Przejdź do zasobów online w celu odnalezienia rozwiązania i opuść grę.";
	btn_exit = "Opuść grę.";
	btn_hidedetails = "Ukryj szczegóły";
	btn_showdetails = "Po<PERSON>ż szczegóły";
	chk_sendreport = "Wyślij raport o błędzie";
	error_code = "Kod błędu:";
	lbl_body1 = "Nieste<PERSON>, nie mogliśmy uruchomić gry.";
	lbl_body2 = "Pomóż nam, zgłaszając ten problem.";
	lbl_body3 = "Usługa Easy Anti-Cheat może sprawdzić zasoby online w poszukiwaniu rozwiązania problemu, a następnie podjąć próbę naprawy błędu.";
	lbl_header = "Nie uruchomiono gry";
	title = "Błąd uruchomienia";
};
game_error:
{
	error_catalogue_corrupted = "Uszkodzony katalog skrótów hash Easy Anti-Cheat";
	error_catalogue_not_found = "Nie odnaleziono indeksu EAC";
	error_certificate_revoked = "Unieważniono certyfikat indeksu EAC";
	error_corrupted_memory = "Uszkodzona pamięć";
	error_corrupted_network = "Uszkodzony przepływ pakietów";
	error_file_forbidden = "Nieznany plik gry";
	error_file_not_found = "Brak wymaganego pliku";
	error_file_version = "Nieznana wersja pliku";
	error_module_forbidden = "Zabroniony moduł";
	error_system_configuration = "Zabroniona konfiguracja systemu";
	error_system_version = "Nieznany plik systemowy";
	error_tool_forbidden = "Zabronione narzędzie";
	error_violation = "Wewnętrzny błąd systemu zapobiegania oszustwom";
	error_virtual = "Nie można uruchomić z maszyny wirtualnej";
	peer_client_banned = "Nałożono blokadę na hosta równorzędnego w celu ochrony przed oszustwami.";
	peer_heartbeat_rejected = "Odrzucono hosta równorzędnego w celu ochrony przed oszustwami.";
	peer_validated = "Ukończono test poprawności hosta równorzędnego zapobiegający oszustwom.";
	peer_validation_failed = "Test poprawności hosta równorzędnego zapobiegający oszustwom nie powiódł się.";
	executable_not_hashed = "Nie udało się zlokalizować w katalogu wpisu wykonywalnego gry.";
};
launcher:
{
	btn_cancel = "Anuluj";
	btn_exit = "Wyjdź";
	error_cancel = "Anulowano uruchamianie";
	error_filenotfound = "Nie znaleziono pliku";
	error_init = "Błąd inicjalizacji";
	error_install = "Błąd instalacji";
	error_launch = "Błąd uruchomienia";
	error_nolib = "Nie wczytano biblioteki Easy Anti-Cheat";
	loading = "WCZYTYWANIE";
	wait = "Poczekaj";
	initializing = "INICJOWANIE";
	success_waiting_for_game = "OCZEKIWANIE NA GRĘ";
	success_closing = "Sukces";
	network_error = "Błąd sieci";
	error_no_settings_file = "{0} – nie znaleziono";
	error_invalid_settings_format = "{0} nie ma prawidłowego formatu JSON";
	error_missing_required_field = "{0} – nie wypełniono wymaganego pola ({1})";
	error_invalid_eos_identifier = "{0} zawiera niepoprawny identyfikator EOS: ({1})";
	download_progress = "Postępy pobierania: {0}";
};
launcher_error:
{
	error_already_running = "aplikacja używająca usługi Easy Anti-Cheat już działa! {0}";
	error_application = "Klient gry napotkał błąd aplikacji. Kod błędu: {0}";
	error_bad_exe_format = "Wymagana 64-bitowa wersja systemu operacyjnego";
	error_bitset_32 = "Użyj gry w wersji 32-bit";
	error_bitset_64 = "Użyj gry w wersji 64-bit";
	error_cancelled = "Operacja anulowana przez użytkownika";
	error_certificate_validation = "Błąd sprawdzania poprawności certyfikatu podpisywania kodu usługi Easy Anti-Cheat";
	error_connection = "Nie połączono z usługą Content Distribution Network!";
	error_debugger = "Wykryto debuger. Zwolnij go z pamięci i spróbuj ponownie";
	error_disk_space = "Za mało miejsca na dysku.";
	error_dns = "Niepowodzenie identyfikacji adresu DNS w usłudze Content Distribution Network!";
	error_dotlocal = "Wykryto przekierowanie biblioteki DLL DotLocal.";
	error_dotlocal_instructions = "Usuń następujący plik";
	error_file_not_found = "Nie znaleziono pliku:";
	error_forbidden_tool = "Zamknij narzędzie {0}, zanim rozpoczniesz grę";
	error_forbidden_driver = "Przed rozpoczęciem gry rozładuj {0}";
	error_generic = "Nieoczekiwany błąd.";
	error_kernel_debug = "Usługa Easy Anti-Cheat nie może działać, jeżeli jest uruchomione debugowanie jądra";
	error_kernel_dse = "Usługa Easy Anti-Cheat nie może działać, jeżeli wyłączono wymuszanie podpisów sterowników";
	error_kernel_modified = "Wykryto zabronioną modyfikację jądra systemu Windows";
	error_library_load = "Nie wczytano biblioteki Easy Anti-Cheat";
	error_memory = "Nie uruchomiono gry z powodu niewystarczającej pamięci";
	error_module_load = "Nie wczytano modułu zapobiegającego oszustwom";
	error_patched = "Wykryto poprawiony moduł ładujący rozruchu systemu Windows";
	error_process = "Postęp niemożliwy";
	error_process_crash = "Nastąpiło nagłe zakończenie procesu";
	error_safe_mode = "Usługa Easy Anti-Cheat nie może działać w trybie awaryjnym systemu Windows.";
	error_socket = "Coś uniemożliwia aplikacji uzyskanie dostępu do Internetu!";
	error_ssl = "Błąd podczas nawiązywania połączenia SSL z usługą CDN!";
	error_start = "Nie uruchomiono gry";
	error_uncpath_forbidden = "Nie można uruchomić gry przez udostępnianie sieciowe (ścieżka UNC)";
	error_connection_failed = "Połączenie nieudane: ";
	error_missing_game_id = "Brak identyfikatora gry";
	error_dns_resolve_failed = "Zapytanie DNS do serwera proxy nieudane";
	error_dns_connection_failed = "Połączenie z siecią dystrybucji treści (CDN) nie powiodło się! Kod cURL: {0}!";
	error_http_response = "Kod odpowiedzi HTTP: {0} Kod cURL: {1}";
	error_driver_handle = "Nieoczekiwany błąd. (Nie udało się otworzyć identyfikatora sterownika)";
	error_incompatible_service = "Niekompatybilna usługa Easy Anti-Cheat jest już uruchomiona. Wyłącz wszystkie pozostałe uruchomione gry lub zrestartuj komputer.";
	error_incompatible_driver_version = "Niekompatybilna wersja sterownika Easy Anti-Cheat jest już uruchomiona. Wyłącz wszystkie pozostałe uruchomione gry lub zrestartuj komputer.";
	error_another_launcher = "Nieoczekiwany błąd. (Włączony jest już inny program uruchamiający)";
	error_game_running = "Nieoczekiwany błąd. (Gra jest już włączona)";
	error_patched_boot_loader = "Wykryto zaktualizowany program rozruchowy Windows. (Ochrona aktualizacji jądra systemu wyłączona)";
	error_unknown_process = "Nie rozpoznano klienta gry. Nie można kontynuować.";
	error_unknown_game = "Nieskonfigurowana gra. Nie można kontynuować.";
	error_win7_required = "Wymagany system Windows 7 lub nowszy.";
	success_initialized = "Zainicjowano usługę Easy Anti-Cheat";
	success_loaded = "Wczytano usługę Easy Anti-Cheat w grze";
	error_create_process = "Nie udało się utworzyć procesu gry: {0}";
	error_create_thread = "Nie udało się utworzyć wątku w tle!";
	error_disallowed_cdn_path = "Niespodziewany błąd. (Niepoprawny adres URL CDN)";
	error_empty_executable_field = "Ścieżka do pliku binarnego gry nie została podana.";
	error_failed_path_query = "Uzyskanie ścieżki procesu nie powiodło się.";
	error_failed_to_execute = "Wykonanie procesu gry nie powiodło się.";
	error_game_binary_is_directory = "Docelowy plik wykonywalny to katalog!";
	error_game_binary_is_dot_app = "Docelowy plik wykonywalny to katalog, należy wybrać plik binarny w .app!";
	error_game_binary_not_found = "Nie można zlokalizować pliku binarnego gry „{0}”";
	error_game_binary_not_found_wine = "Nie można zlokalizować pliku binarnego gry (Wine)";
	error_game_security_violation = "Naruszenie bezpieczeństwa gry {0}";
	error_generic_ex = "Niespodziewany błąd. {0}";
	error_instance_count_limit = "Osiągnięto maksymalną liczbę instancji gry!";
	error_internal = "Błąd wewnętrzny!";
	error_invalid_executable_path = "Nieprawidłowa ścieżka do pliku wykonywalnego gry!";
	error_memory_ex = "Niewystarczająca ilość pamięci do rozpoczęcia gry {0}";
	error_missing_binary_path = "Brakująca ścieżka do pliku wykonywalnego gry.";
	error_missing_directory_path = "Brakująca ścieżka do katalogu bieżącego.";
	error_module_initialize = "Inicjalizacja modułu z {0} zakończona niepowodzeniem";
	error_module_loading = "Wgranie modułu utrudniającego oszukiwanie nie powiodło się.";
	error_set_environment_variables = "Ustawienie zmiennych środowiska w procesie gry nie powiodło się.";
	error_unrecognized_blacklisted_driver = "Wykryto N/A. Rozładuj i spróbuj ponownie.";
	error_unsupported_machine_arch = "Nieobsługiwana architektura urządzenia hosta. ({0})";
	error_working_directory_not_found = "Wybrany katalog bieżący nie istnieje.";
	error_x8664_required = "Nieobsługiwany system operacyjny. Wymagana jest wersja 64-bitowa (x86–64) Windows.";
	warn_module_download_size = "Rozmiar odpowiedzi HTTP: {0}. Start w trybie klienta zerowego.";
	warn_vista_deprecation = "Easy Anti-Cheat musi zakończyć obsługę Windows Vista w październiku 2020 r., ponieważ tworzenie kompatybilnych podpisów w postaci kodu nie będzie już możliwe. Więcej informacji znajduje się na stronie https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "Nie można zweryfikować konfiguracji Anti-Cheat.";
	warn_win7_update_required = "Zaktualizuj Windows. Twój system nie obsługuje niezbędnego podpisu w formie kodu SHA-2 wymaganego przed październikiem 2020 r. Więcej informacji znajduje się na stronie https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "Aktualizacja usługi Easy Anti-Cheat – upłynął limit czasu."
	error_launch_ex = "Błąd uruchomienia: {0}";
};
setup:
{
	btn_finish = "Zakończ";
	btn_install = "Zainstaluj teraz";
	btn_repair = "Napraw usługę";
	btn_uninstall = "Odinstaluj";
	epic_link = "© Epic Games, Inc";
	install_progress = "Instalowanie...";
	install_success = "Zainstalowano";
	licenses_link = "Licencje";
	privacy_link = "Prywatność";
	repair_progress = "Naprawianie...";
	title = "Easy Anti-Cheat Service Setup";
	uninstall_progress = "Trwa dezinstalacja...";
	uninstall_success = "Odinstalowano";
};
setup_error:
{
	error_cancelled = "Operacja anulowana przez użytkownika";
	error_encrypted = "Folder instalacyjny Easy Anti-Cheat został zaszyfrowany";
	error_intro = "Niepowodzenie instalacji Easy Anti-Cheat";
	error_not_installed = "Nie zainstalowano Easy Anti-Cheat ";
	error_registry = "Plik wykonywalny usługi: niepowodzenie";
	error_rights = "Niewystarczające przywileje";
	error_service = "Nie można stworzyć usługi";
	error_service_ex = "Nie można stworzyć usługi {0}";
	error_system = "Odmowa dostępu do katalogu System32";
};