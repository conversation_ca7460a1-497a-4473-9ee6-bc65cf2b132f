#!/usr/bin/env python3
"""
🧪 TEST GUI BUTTONS
===================
Quick test to show the new aimbot activation buttons
"""

from aimbot_settings_gui import AimbotSettingsGUI

if __name__ == "__main__":
    print("🎛️ Launching Aimbot Settings GUI with new activation buttons...")
    print("✅ New features added:")
    print("   • 🚀 START AIMBOT button")
    print("   • ⏹️ STOP AIMBOT button") 
    print("   • 🔄 TOGGLE (F1) button")
    print("   • Automatic aimbot launching if not connected")
    print()
    
    gui = AimbotSettingsGUI()
    gui.run()
