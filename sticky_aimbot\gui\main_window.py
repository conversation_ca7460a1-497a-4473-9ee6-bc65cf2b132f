#!/usr/bin/env python3
"""
🖥️ MAIN WINDOW
==============
Main GUI window for the sticky aimbot system.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from typing import Optional

class AimbotGUI:
    """Main GUI window for the sticky aimbot system"""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the GUI"""
        self.config_path = config_path
        self.root = tk.Tk()
        self.root.title("🎯 Sticky Aim Aimbot - Educational System")
        self.root.geometry("800x600")
        self.root.configure(bg='#1a0d1a')
        
        # Aimbot engine (will be initialized later)
        self.aimbot_engine = None
        self.running = False
        
        # Create GUI
        self.create_widgets()
        
        # Show educational warning
        self.show_educational_warning()
    
    def show_educational_warning(self):
        """Show educational warning dialog"""
        warning_text = """
🎯 STICKY AIM AIMBOT - EDUCATIONAL SOFTWARE

⚠️  IMPORTANT NOTICE ⚠️

This software is provided for EDUCATIONAL and RESEARCH purposes only.

PERMITTED USES:
✅ Learning about computer vision and AI
✅ Understanding game mechanics and memory structures
✅ Research into human-computer interaction
✅ Educational demonstrations and tutorials
✅ Offline testing and experimentation

PROHIBITED USES:
❌ Online competitive gaming
❌ Violating game Terms of Service
❌ Gaining unfair advantages in multiplayer games
❌ Commercial use without proper licensing
❌ Malicious or harmful activities

By clicking 'I Understand', you acknowledge that you understand
and agree to these terms.
        """
        
        result = messagebox.askokcancel("Educational Notice", warning_text)
        if not result:
            self.root.destroy()
            return
    
    def create_widgets(self):
        """Create GUI widgets"""
        # Title
        title_label = tk.Label(
            self.root, 
            text="🎯 STICKY AIM AIMBOT", 
            font=('Arial', 20, 'bold'), 
            bg='#1a0d1a', 
            fg='white'
        )
        title_label.pack(pady=20)
        
        # Status frame
        status_frame = tk.Frame(self.root, bg='#1a0d1a')
        status_frame.pack(pady=10)
        
        tk.Label(status_frame, text="Status:", font=('Arial', 12, 'bold'), 
                bg='#1a0d1a', fg='white').pack(side='left')
        
        self.status_label = tk.Label(
            status_frame, 
            text="STOPPED", 
            font=('Arial', 12, 'bold'), 
            bg='#1a0d1a', 
            fg='red'
        )
        self.status_label.pack(side='left', padx=10)
        
        # Control buttons
        button_frame = tk.Frame(self.root, bg='#1a0d1a')
        button_frame.pack(pady=20)
        
        self.start_button = tk.Button(
            button_frame,
            text="🚀 Start Aimbot",
            font=('Arial', 14, 'bold'),
            bg='#4CAF50',
            fg='white',
            width=15,
            height=2,
            command=self.start_aimbot
        )
        self.start_button.pack(side='left', padx=10)
        
        self.stop_button = tk.Button(
            button_frame,
            text="🛑 Stop Aimbot",
            font=('Arial', 14, 'bold'),
            bg='#f44336',
            fg='white',
            width=15,
            height=2,
            command=self.stop_aimbot,
            state='disabled'
        )
        self.stop_button.pack(side='left', padx=10)
        
        # Configuration frame
        config_frame = tk.LabelFrame(
            self.root, 
            text="Configuration", 
            font=('Arial', 12, 'bold'),
            bg='#1a0d1a', 
            fg='white'
        )
        config_frame.pack(pady=20, padx=20, fill='x')
        
        # Detection method
        tk.Label(config_frame, text="Detection Method:", 
                bg='#1a0d1a', fg='white').grid(row=0, column=0, sticky='w', padx=10, pady=5)
        
        self.detection_var = tk.StringVar(value="cv")
        detection_combo = ttk.Combobox(
            config_frame, 
            textvariable=self.detection_var,
            values=['cv', 'memory', 'hybrid'],
            state='readonly'
        )
        detection_combo.grid(row=0, column=1, padx=10, pady=5)
        
        # Lock strength
        tk.Label(config_frame, text="Lock Strength:", 
                bg='#1a0d1a', fg='white').grid(row=1, column=0, sticky='w', padx=10, pady=5)
        
        self.lock_strength_var = tk.DoubleVar(value=0.8)
        lock_strength_scale = tk.Scale(
            config_frame,
            from_=0.1,
            to=1.0,
            resolution=0.1,
            orient='horizontal',
            variable=self.lock_strength_var,
            bg='#1a0d1a',
            fg='white'
        )
        lock_strength_scale.grid(row=1, column=1, padx=10, pady=5)
        
        # Smoothing factor
        tk.Label(config_frame, text="Smoothing Factor:", 
                bg='#1a0d1a', fg='white').grid(row=2, column=0, sticky='w', padx=10, pady=5)
        
        self.smoothing_var = tk.DoubleVar(value=4.0)
        smoothing_scale = tk.Scale(
            config_frame,
            from_=1.0,
            to=10.0,
            resolution=0.5,
            orient='horizontal',
            variable=self.smoothing_var,
            bg='#1a0d1a',
            fg='white'
        )
        smoothing_scale.grid(row=2, column=1, padx=10, pady=5)
        
        # Statistics frame
        stats_frame = tk.LabelFrame(
            self.root, 
            text="Statistics", 
            font=('Arial', 12, 'bold'),
            bg='#1a0d1a', 
            fg='white'
        )
        stats_frame.pack(pady=20, padx=20, fill='both', expand=True)
        
        # Stats text
        self.stats_text = tk.Text(
            stats_frame,
            height=10,
            bg='#2a1a2a',
            fg='white',
            font=('Courier', 10)
        )
        self.stats_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Update stats initially
        self.update_stats()
        
        # Start stats update thread
        self.stats_thread = threading.Thread(target=self.stats_update_loop, daemon=True)
        self.stats_thread.start()
    
    def start_aimbot(self):
        """Start the aimbot"""
        try:
            # Import here to avoid circular imports
            from ..core.aimbot_engine import AimbotEngine
            
            # Create aimbot engine if not exists
            if not self.aimbot_engine:
                self.aimbot_engine = AimbotEngine(self.config_path)
                
                # Update config from GUI
                config = self.aimbot_engine.config
                config['detection']['method'] = self.detection_var.get()
                config['tracking']['lock_strength'] = self.lock_strength_var.get()
                config['tracking']['smoothing_factor'] = self.smoothing_var.get()
                self.aimbot_engine.update_config(config)
            
            # Initialize components
            if not self.aimbot_engine.initialize_components():
                messagebox.showerror("Error", "Failed to initialize aimbot components")
                return
            
            # Start aimbot
            if self.aimbot_engine.start():
                self.running = True
                self.status_label.config(text="RUNNING", fg='green')
                self.start_button.config(state='disabled')
                self.stop_button.config(state='normal')
                messagebox.showinfo("Success", "🎯 Aimbot started successfully!")
            else:
                messagebox.showerror("Error", "Failed to start aimbot")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start aimbot:\n{str(e)}")
    
    def stop_aimbot(self):
        """Stop the aimbot"""
        try:
            if self.aimbot_engine and self.aimbot_engine.is_running():
                if self.aimbot_engine.stop():
                    self.running = False
                    self.status_label.config(text="STOPPED", fg='red')
                    self.start_button.config(state='normal')
                    self.stop_button.config(state='disabled')
                    messagebox.showinfo("Success", "🛑 Aimbot stopped successfully!")
                else:
                    messagebox.showerror("Error", "Failed to stop aimbot")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop aimbot:\n{str(e)}")
    
    def update_stats(self):
        """Update statistics display"""
        try:
            stats_text = "🎯 STICKY AIMBOT STATISTICS\n"
            stats_text += "=" * 40 + "\n\n"
            
            if self.aimbot_engine and self.running:
                stats = self.aimbot_engine.get_stats()
                stats_text += f"Status: RUNNING\n"
                stats_text += f"FPS: {stats.fps:.1f}\n"
                stats_text += f"Targets Detected: {stats.targets_detected}\n"
                stats_text += f"Targets Locked: {stats.targets_locked}\n"
                stats_text += f"Runtime: {stats.total_runtime:.1f}s\n"
                stats_text += f"Accuracy: {stats.accuracy_percentage:.1f}%\n"
            else:
                stats_text += "Status: STOPPED\n"
                stats_text += "No statistics available\n"
            
            stats_text += "\n" + "=" * 40 + "\n"
            stats_text += "Configuration:\n"
            stats_text += f"Detection Method: {self.detection_var.get()}\n"
            stats_text += f"Lock Strength: {self.lock_strength_var.get():.1f}\n"
            stats_text += f"Smoothing Factor: {self.smoothing_var.get():.1f}\n"
            
            # Update text widget
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
            
        except Exception as e:
            print(f"❌ Stats update error: {e}")
    
    def stats_update_loop(self):
        """Background thread for updating statistics"""
        while True:
            try:
                self.update_stats()
                time.sleep(1.0)  # Update every second
            except Exception as e:
                print(f"❌ Stats loop error: {e}")
                time.sleep(5.0)  # Wait longer on error
    
    def close(self):
        """Close the GUI"""
        if self.aimbot_engine and self.aimbot_engine.is_running():
            self.aimbot_engine.stop()
        self.root.destroy()
    
    def run(self):
        """Run the GUI"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.close)
            self.root.mainloop()
        except Exception as e:
            print(f"❌ GUI error: {e}")
            messagebox.showerror("Error", f"GUI error: {str(e)}")
