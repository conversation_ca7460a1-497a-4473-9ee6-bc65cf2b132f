#!/usr/bin/env python3
"""
🎯 STICKY AIM AIMBOT - SETUP SCRIPT
===================================
Installation and dependency management for the complete aimbot project
"""

from setuptools import setup, find_packages
import os
import sys

# Ensure Python 3.9+
if sys.version_info < (3, 9):
    print("❌ Python 3.9 or higher is required")
    sys.exit(1)

# Read README for long description
def read_readme():
    try:
        with open("README.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return "Complete Sticky Aim Aimbot System for Educational Purposes"

# Core dependencies
CORE_REQUIREMENTS = [
    # Computer Vision & AI
    "opencv-python>=4.8.0",
    "ultralytics>=8.0.0",
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "numpy>=1.24.0",
    "pillow>=10.0.0",
    
    # System & Process Management
    "psutil>=5.9.0",
    "pywin32>=306; sys_platform=='win32'",
    "keyboard>=0.13.5",
    "mouse>=0.7.1",
    
    # GUI & Visualization
    "matplotlib>=3.7.0",
    "tkinter-tooltip>=2.0.0",
    
    # Performance & Utilities
    "numba>=0.57.0",
    "scipy>=1.10.0",
    "requests>=2.31.0",
    
    # Configuration & Logging
    "pyyaml>=6.0",
    "colorlog>=6.7.0",
    "tqdm>=4.65.0"
]

# Development dependencies
DEV_REQUIREMENTS = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "sphinx>=7.1.0",
    "pre-commit>=3.3.0"
]

# Optional dependencies for advanced features
OPTIONAL_REQUIREMENTS = {
    "gpu": ["cupy-cuda11x>=12.0.0"],
    "advanced_ai": ["transformers>=4.30.0", "timm>=0.9.0"],
    "profiling": ["py-spy>=0.3.14", "memory-profiler>=0.61.0"],
    "docs": ["sphinx-rtd-theme>=1.3.0", "myst-parser>=2.0.0"]
}

setup(
    name="sticky-aim-aimbot",
    version="1.0.0",
    author="Educational Research Project",
    author_email="<EMAIL>",
    description="Complete Sticky Aim Aimbot System for Educational and Research Purposes",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/educational/sticky-aim-aimbot",
    
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Education",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Education",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    
    python_requires=">=3.9",
    install_requires=CORE_REQUIREMENTS,
    extras_require={
        "dev": DEV_REQUIREMENTS,
        **OPTIONAL_REQUIREMENTS,
        "all": DEV_REQUIREMENTS + 
               OPTIONAL_REQUIREMENTS["gpu"] + 
               OPTIONAL_REQUIREMENTS["advanced_ai"] + 
               OPTIONAL_REQUIREMENTS["profiling"] + 
               OPTIONAL_REQUIREMENTS["docs"]
    },
    
    entry_points={
        "console_scripts": [
            "sticky-aimbot=sticky_aimbot.main:main",
            "aimbot-config=sticky_aimbot.gui.config_tool:main",
            "aimbot-test=sticky_aimbot.tests.test_runner:main",
        ],
    },
    
    package_data={
        "sticky_aimbot": [
            "config/presets/*.json",
            "detection/ai_models/*.pt",
            "gui/assets/*.png",
            "gui/assets/*.ico",
            "docs/*.md"
        ]
    },
    
    include_package_data=True,
    zip_safe=False,
    
    project_urls={
        "Documentation": "https://sticky-aimbot.readthedocs.io/",
        "Source": "https://github.com/educational/sticky-aim-aimbot",
        "Tracker": "https://github.com/educational/sticky-aim-aimbot/issues",
    },
    
    keywords=[
        "aimbot", "computer-vision", "game-ai", "educational", 
        "research", "machine-learning", "object-detection"
    ],
    
    # Custom commands
    cmdclass={},
    
    # Platform-specific requirements
    platforms=["win32"],
    
    # License
    license="MIT",
    
    # Additional metadata
    maintainer="Research Team",
    maintainer_email="<EMAIL>",
    
    # Minimum versions for critical dependencies
    python_requires=">=3.9",
)

# Post-installation setup
def post_install():
    """Run post-installation setup tasks"""
    print("🎯 Setting up Sticky Aim Aimbot...")
    
    # Create necessary directories
    directories = [
        "logs",
        "config/user",
        "detection/ai_models/custom",
        "data/screenshots",
        "data/recordings"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    # Download AI models if needed
    try:
        from sticky_aimbot.detection.model_downloader import download_models
        download_models()
        print("✅ AI models downloaded successfully")
    except ImportError:
        print("⚠️  AI model download will be handled at runtime")
    
    print("🎯 Setup complete! Run 'sticky-aimbot --help' to get started.")

if __name__ == "__main__":
    # Run setup
    setup()
    
    # Run post-installation if installing
    if "install" in sys.argv:
        post_install()
