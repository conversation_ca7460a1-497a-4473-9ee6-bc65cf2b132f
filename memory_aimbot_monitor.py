#!/usr/bin/env python3
"""
📊 MEMORY AIMBOT MONITOR
=======================
Real-time monitoring and status display for memory injection aimbot
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
import time
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from collections import deque

class MemoryAimbotMonitor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("📊 Memory Aimbot Monitor")
        self.root.geometry("1000x700")
        self.root.configure(bg='#0f0f0f')
        
        # Monitoring data
        self.monitoring_active = True
        self.data_history = {
            'fps': deque(maxlen=100),
            'targets': deque(maxlen=100),
            'memory_ops': deque(maxlen=100),
            'accuracy': deque(maxlen=100),
            'timestamps': deque(maxlen=100)
        }
        
        # Current status
        self.current_status = {
            'aimbot_active': False,
            'process_connected': False,
            'memory_reading': False,
            'target_count': 0,
            'fps': 0,
            'memory_operations': 0,
            'last_update': 'Never'
        }
        
        self.create_widgets()
        self.start_monitoring()
        
    def create_widgets(self):
        """Create monitor GUI widgets"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#0f0f0f')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Top status panel
        self.create_status_panel(main_frame)
        
        # Middle section with tabs
        self.create_tabbed_interface(main_frame)
        
        # Bottom control panel
        self.create_control_panel(main_frame)
        
    def create_status_panel(self, parent):
        """Create top status panel"""
        status_frame = tk.LabelFrame(parent, text="🎯 Aimbot Status", 
                                   bg='#0f0f0f', fg='white', font=('Arial', 12, 'bold'))
        status_frame.pack(fill='x', pady=5)
        
        # Status indicators in grid
        indicators_frame = tk.Frame(status_frame, bg='#0f0f0f')
        indicators_frame.pack(fill='x', padx=10, pady=10)
        
        # Row 1
        self.process_indicator = self.create_status_indicator(indicators_frame, "🔗 Process", "DISCONNECTED", 'red', 0, 0)
        self.memory_indicator = self.create_status_indicator(indicators_frame, "💾 Memory", "INACTIVE", 'red', 0, 1)
        self.aimbot_indicator = self.create_status_indicator(indicators_frame, "🎯 Aimbot", "INACTIVE", 'red', 0, 2)
        
        # Row 2
        self.fps_indicator = self.create_status_indicator(indicators_frame, "🚀 FPS", "0", 'gray', 1, 0)
        self.targets_indicator = self.create_status_indicator(indicators_frame, "👥 Targets", "0", 'gray', 1, 1)
        self.ops_indicator = self.create_status_indicator(indicators_frame, "⚡ Mem Ops", "0", 'gray', 1, 2)
        
    def create_status_indicator(self, parent, label, value, color, row, col):
        """Create a status indicator"""
        frame = tk.Frame(parent, bg='#1a1a1a', relief='raised', bd=1)
        frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')
        parent.grid_columnconfigure(col, weight=1)
        
        tk.Label(frame, text=label, bg='#1a1a1a', fg='white', 
                font=('Arial', 10, 'bold')).pack(pady=2)
        
        value_label = tk.Label(frame, text=value, bg='#1a1a1a', fg=color, 
                              font=('Arial', 12, 'bold'))
        value_label.pack(pady=2)
        
        return value_label
        
    def create_tabbed_interface(self, parent):
        """Create tabbed interface for different views"""
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True, pady=10)
        
        # Performance tab
        self.create_performance_tab(notebook)
        
        # Memory operations tab
        self.create_memory_tab(notebook)
        
        # Target tracking tab
        self.create_target_tab(notebook)
        
        # Debug log tab
        self.create_debug_tab(notebook)
        
    def create_performance_tab(self, notebook):
        """Create performance monitoring tab"""
        perf_frame = ttk.Frame(notebook, padding="10")
        notebook.add(perf_frame, text="📈 Performance")
        
        # Create matplotlib figure
        self.perf_fig, ((self.fps_ax, self.targets_ax), 
                       (self.memory_ax, self.accuracy_ax)) = plt.subplots(2, 2, figsize=(10, 6))
        self.perf_fig.patch.set_facecolor('#1a1a1a')
        
        # Configure axes
        axes_config = [
            (self.fps_ax, 'FPS', 'lime'),
            (self.targets_ax, 'Targets Detected', 'orange'),
            (self.memory_ax, 'Memory Operations/sec', 'cyan'),
            (self.accuracy_ax, 'Aim Accuracy %', 'yellow')
        ]
        
        for ax, title, color in axes_config:
            ax.set_title(title, color='white')
            ax.set_facecolor('#0a0a0a')
            ax.tick_params(colors='white')
            ax.grid(True, alpha=0.3)
        
        self.perf_canvas = FigureCanvasTkAgg(self.perf_fig, perf_frame)
        self.perf_canvas.get_tk_widget().pack(fill='both', expand=True)
        
    def create_memory_tab(self, notebook):
        """Create memory operations tab"""
        memory_frame = ttk.Frame(notebook, padding="10")
        notebook.add(memory_frame, text="💾 Memory Ops")
        
        # Memory statistics
        stats_frame = tk.LabelFrame(memory_frame, text="📊 Memory Statistics", 
                                  bg='#1a1a1a', fg='white')
        stats_frame.pack(fill='x', pady=5)
        
        stats_grid = tk.Frame(stats_frame, bg='#1a1a1a')
        stats_grid.pack(fill='x', padx=10, pady=10)
        
        self.mem_stats = {}
        stats_labels = ['Reads/sec', 'Writes/sec', 'Success Rate', 'Avg Latency', 'Errors', 'Data Transferred']
        
        for i, label in enumerate(stats_labels):
            row, col = i // 3, i % 3
            tk.Label(stats_grid, text=f"{label}:", bg='#1a1a1a', fg='white').grid(row=row*2, column=col, sticky='w', padx=5)
            value_label = tk.Label(stats_grid, text="0", bg='#1a1a1a', fg='cyan', font=('Arial', 10, 'bold'))
            value_label.grid(row=row*2+1, column=col, sticky='w', padx=5)
            self.mem_stats[label] = value_label
        
        # Memory operations log
        log_frame = tk.LabelFrame(memory_frame, text="📝 Recent Operations", 
                                bg='#1a1a1a', fg='white')
        log_frame.pack(fill='both', expand=True, pady=5)
        
        self.memory_log = scrolledtext.ScrolledText(log_frame, height=15, 
                                                  bg='#0a0a0a', fg='lime', 
                                                  font=('Consolas', 9))
        self.memory_log.pack(fill='both', expand=True, padx=10, pady=10)
        
    def create_target_tab(self, notebook):
        """Create target tracking tab"""
        target_frame = ttk.Frame(notebook, padding="10")
        notebook.add(target_frame, text="🎯 Targets")
        
        # Target list
        list_frame = tk.LabelFrame(target_frame, text="👥 Detected Targets", 
                                 bg='#1a1a1a', fg='white')
        list_frame.pack(fill='both', expand=True, pady=5)
        
        # Create treeview for targets
        columns = ('ID', 'Position', 'Health', 'Team', 'Distance', 'Status')
        self.target_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.target_tree.heading(col, text=col)
            self.target_tree.column(col, width=100)
        
        # Scrollbar for treeview
        target_scroll = ttk.Scrollbar(list_frame, orient='vertical', command=self.target_tree.yview)
        self.target_tree.configure(yscrollcommand=target_scroll.set)
        
        self.target_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        target_scroll.pack(side='right', fill='y', pady=10)
        
        # Target details
        details_frame = tk.LabelFrame(target_frame, text="🎯 Current Target Details", 
                                    bg='#1a1a1a', fg='white')
        details_frame.pack(fill='x', pady=5)
        
        self.target_details = scrolledtext.ScrolledText(details_frame, height=8, 
                                                      bg='#0a0a0a', fg='yellow', 
                                                      font=('Consolas', 10))
        self.target_details.pack(fill='x', padx=10, pady=10)
        
    def create_debug_tab(self, notebook):
        """Create debug log tab"""
        debug_frame = ttk.Frame(notebook, padding="10")
        notebook.add(debug_frame, text="🔍 Debug Log")
        
        # Log controls
        controls_frame = tk.Frame(debug_frame, bg='#1a1a1a')
        controls_frame.pack(fill='x', pady=5)
        
        tk.Button(controls_frame, text="🔄 Clear Log", bg='#4a4a4a', fg='white',
                 command=self.clear_debug_log).pack(side='left', padx=5)
        
        tk.Button(controls_frame, text="💾 Save Log", bg='#4a4a4a', fg='white',
                 command=self.save_debug_log).pack(side='left', padx=5)
        
        tk.Button(controls_frame, text="📊 Export Data", bg='#4a4a4a', fg='white',
                 command=self.export_monitoring_data).pack(side='left', padx=5)
        
        # Debug log display
        self.debug_log = scrolledtext.ScrolledText(debug_frame, height=25, 
                                                 bg='#0a0a0a', fg='white', 
                                                 font=('Consolas', 9))
        self.debug_log.pack(fill='both', expand=True, pady=10)
        
    def create_control_panel(self, parent):
        """Create bottom control panel"""
        control_frame = tk.Frame(parent, bg='#1a1a1a', relief='raised', bd=1)
        control_frame.pack(fill='x', pady=5)
        
        # Status and controls
        tk.Label(control_frame, text="📊 Monitor Status:", bg='#1a1a1a', fg='white',
                font=('Arial', 10, 'bold')).pack(side='left', padx=10, pady=5)
        
        self.monitor_status = tk.Label(control_frame, text="🟢 ACTIVE", bg='#1a1a1a', fg='lime',
                                     font=('Arial', 10, 'bold'))
        self.monitor_status.pack(side='left', padx=5, pady=5)
        
        # Last update time
        self.last_update_label = tk.Label(control_frame, text="Last Update: Never", 
                                        bg='#1a1a1a', fg='gray', font=('Arial', 9))
        self.last_update_label.pack(side='right', padx=10, pady=5)
        
    def update_status_indicators(self, status_data):
        """Update status indicators"""
        # Process connection
        if status_data.get('process_connected', False):
            self.process_indicator.config(text="CONNECTED", fg='lime')
        else:
            self.process_indicator.config(text="DISCONNECTED", fg='red')
        
        # Memory operations
        if status_data.get('memory_reading', False):
            self.memory_indicator.config(text="ACTIVE", fg='lime')
        else:
            self.memory_indicator.config(text="INACTIVE", fg='red')
        
        # Aimbot status
        if status_data.get('aimbot_active', False):
            self.aimbot_indicator.config(text="ACTIVE", fg='lime')
        else:
            self.aimbot_indicator.config(text="INACTIVE", fg='red')
        
        # Numeric indicators
        self.fps_indicator.config(text=f"{status_data.get('fps', 0):.1f}")
        self.targets_indicator.config(text=str(status_data.get('target_count', 0)))
        self.ops_indicator.config(text=str(status_data.get('memory_operations', 0)))
        
    def update_performance_graphs(self):
        """Update performance graphs"""
        if len(self.data_history['timestamps']) < 2:
            return
            
        # Get data
        times = list(self.data_history['timestamps'])
        fps_data = list(self.data_history['fps'])
        targets_data = list(self.data_history['targets'])
        memory_data = list(self.data_history['memory_ops'])
        accuracy_data = list(self.data_history['accuracy'])
        
        # Normalize time
        base_time = times[0]
        time_points = [(t - base_time) for t in times]
        
        # Clear and update graphs
        for ax in [self.fps_ax, self.targets_ax, self.memory_ax, self.accuracy_ax]:
            ax.clear()
            ax.set_facecolor('#0a0a0a')
            ax.tick_params(colors='white')
            ax.grid(True, alpha=0.3)
        
        # Plot data
        self.fps_ax.plot(time_points, fps_data, 'lime', linewidth=2)
        self.fps_ax.set_title('FPS', color='white')
        
        self.targets_ax.plot(time_points, targets_data, 'orange', linewidth=2)
        self.targets_ax.set_title('Targets Detected', color='white')
        
        self.memory_ax.plot(time_points, memory_data, 'cyan', linewidth=2)
        self.memory_ax.set_title('Memory Operations/sec', color='white')
        
        self.accuracy_ax.plot(time_points, accuracy_data, 'yellow', linewidth=2)
        self.accuracy_ax.set_title('Aim Accuracy %', color='white')
        
        self.perf_canvas.draw()
        
    def start_monitoring(self):
        """Start monitoring thread"""
        def monitor_loop():
            while self.monitoring_active:
                try:
                    # Load data from memory aimbot
                    self.load_aimbot_data()
                    
                    # Update GUI
                    self.root.after(0, self.update_gui)
                    
                    time.sleep(0.5)  # 2 Hz update rate
                    
                except Exception as e:
                    self.log_debug(f"Monitor error: {e}")
                    time.sleep(1)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        
    def load_aimbot_data(self):
        """Load data from memory aimbot"""
        try:
            # Try to load from shared data file
            if os.path.exists('memory_aimbot_status.json'):
                with open('memory_aimbot_status.json', 'r') as f:
                    data = json.load(f)
                    self.current_status.update(data)
                    
                    # Add to history
                    current_time = time.time()
                    self.data_history['timestamps'].append(current_time)
                    self.data_history['fps'].append(data.get('fps', 0))
                    self.data_history['targets'].append(data.get('target_count', 0))
                    self.data_history['memory_ops'].append(data.get('memory_operations', 0))
                    self.data_history['accuracy'].append(data.get('accuracy', 0))
                    
        except Exception as e:
            # Simulate data for testing
            self.simulate_monitoring_data()
            
    def simulate_monitoring_data(self):
        """Simulate monitoring data for testing"""
        current_time = time.time()
        
        # Simulate status
        self.current_status.update({
            'process_connected': True,
            'memory_reading': int(current_time) % 4 < 3,
            'aimbot_active': int(current_time) % 6 < 3,
            'target_count': int(3 + 2 * np.sin(current_time / 5)),
            'fps': 800 + 200 * np.sin(current_time / 10),
            'memory_operations': int(1000 + 500 * np.cos(current_time / 7)),
            'last_update': datetime.now().strftime("%H:%M:%S")
        })
        
        # Add to history
        self.data_history['timestamps'].append(current_time)
        self.data_history['fps'].append(self.current_status['fps'])
        self.data_history['targets'].append(self.current_status['target_count'])
        self.data_history['memory_ops'].append(self.current_status['memory_operations'])
        self.data_history['accuracy'].append(85 + 10 * np.sin(current_time / 8))
        
    def update_gui(self):
        """Update GUI elements"""
        # Update status indicators
        self.update_status_indicators(self.current_status)
        
        # Update performance graphs
        self.update_performance_graphs()
        
        # Update last update time
        self.last_update_label.config(text=f"Last Update: {self.current_status.get('last_update', 'Never')}")
        
    def log_debug(self, message):
        """Add message to debug log"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}\n"
        
        self.debug_log.insert(tk.END, log_entry)
        self.debug_log.see(tk.END)
        
        # Keep log size manageable
        if self.debug_log.index(tk.END).split('.')[0] > '1000':
            self.debug_log.delete(1.0, '100.0')
            
    def clear_debug_log(self):
        """Clear debug log"""
        self.debug_log.delete(1.0, tk.END)
        
    def save_debug_log(self):
        """Save debug log to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"memory_aimbot_monitor_log_{timestamp}.txt"
        
        with open(filename, 'w') as f:
            f.write(self.debug_log.get(1.0, tk.END))
        
        self.log_debug(f"Debug log saved to {filename}")
        
    def export_monitoring_data(self):
        """Export monitoring data to JSON"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"memory_aimbot_monitor_data_{timestamp}.json"
        
        export_data = {
            'current_status': self.current_status,
            'history': {key: list(value) for key, value in self.data_history.items()}
        }
        
        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        self.log_debug(f"Monitoring data exported to {filename}")
        
    def run(self):
        """Run the monitor"""
        print("📊 Memory Aimbot Monitor started")
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """Handle window closing"""
        self.monitoring_active = False
        self.root.destroy()

if __name__ == "__main__":
    monitor = MemoryAimbotMonitor()
    monitor.run()
