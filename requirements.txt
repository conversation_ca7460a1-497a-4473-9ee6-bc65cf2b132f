# 🎯 STICKY AIM AIMBOT - REQUIREMENTS
# ===================================
# Complete dependency list for the sticky aim aimbot system

# Core Computer Vision & AI
opencv-python>=4.8.0
ultralytics>=8.0.0
torch>=2.0.0
torchvision>=0.15.0
numpy>=1.24.0
pillow>=10.0.0
scipy>=1.10.0

# System & Process Management
psutil>=5.9.0
pywin32>=306; sys_platform=="win32"
keyboard>=0.13.5
mouse>=0.7.1

# GUI & Visualization
matplotlib>=3.7.0
tkinter-tooltip>=2.0.0

# Performance & Optimization
numba>=0.57.0
cupy-cuda11x>=12.0.0; extra == "gpu"

# Configuration & Data
pyyaml>=6.0
json5>=0.9.0

# Logging & Debugging
colorlog>=6.7.0
tqdm>=4.65.0

# Networking & Utils
requests>=2.31.0
urllib3>=2.0.0

# Development Dependencies (optional)
pytest>=7.4.0; extra == "dev"
pytest-cov>=4.1.0; extra == "dev"
black>=23.0.0; extra == "dev"
flake8>=6.0.0; extra == "dev"
mypy>=1.5.0; extra == "dev"

# Documentation (optional)
sphinx>=7.1.0; extra == "docs"
sphinx-rtd-theme>=1.3.0; extra == "docs"
myst-parser>=2.0.0; extra == "docs"

# Profiling Tools (optional)
py-spy>=0.3.14; extra == "profiling"
memory-profiler>=0.61.0; extra == "profiling"

# Advanced AI Models (optional)
transformers>=4.30.0; extra == "advanced_ai"
timm>=0.9.0; extra == "advanced_ai"

# Audio Processing (optional)
pyaudio>=0.2.11; extra == "audio"
sounddevice>=0.4.6; extra == "audio"

# Additional Computer Vision
scikit-image>=0.21.0
imutils>=0.5.4

# Mathematical Libraries
sympy>=1.12
pandas>=2.0.0

# Threading & Concurrency
concurrent-futures>=3.1.1
asyncio>=3.4.3

# System Monitoring
GPUtil>=1.4.0
wmi>=1.5.1; sys_platform=="win32"

# Cryptography & Security
cryptography>=41.0.0
hashlib>=20081119

# File I/O & Compression
h5py>=3.9.0
lz4>=4.3.0

# Image Processing Extensions
opencv-contrib-python>=4.8.0
imageio>=2.31.0

# Machine Learning Utilities
scikit-learn>=1.3.0
joblib>=1.3.0

# Network Communication
websockets>=11.0.0
aiohttp>=3.8.0

# Database (optional)
sqlite3>=2.6.0
sqlalchemy>=2.0.0; extra == "database"

# Configuration Management
configparser>=5.3.0
argparse>=1.4.0

# Time & Date Utilities
python-dateutil>=2.8.0
pytz>=2023.3

# String & Text Processing
regex>=2023.6.0
fuzzywuzzy>=0.18.0

# System Information
platform>=1.0.8
getpass>=1.0

# Error Handling & Validation
marshmallow>=3.20.0
cerberus>=1.3.4

# Caching
cachetools>=5.3.0
diskcache>=5.6.0

# Progress Bars & UI
rich>=13.4.0
click>=8.1.0

# Testing Utilities
hypothesis>=6.82.0; extra == "dev"
factory-boy>=3.3.0; extra == "dev"

# Code Quality
pre-commit>=3.3.0; extra == "dev"
bandit>=1.7.0; extra == "dev"

# Type Checking
types-requests>=2.31.0; extra == "dev"
types-PyYAML>=6.0.0; extra == "dev"

# Performance Monitoring
line-profiler>=4.1.0; extra == "profiling"
pympler>=0.9; extra == "profiling"

# Visualization Extensions
seaborn>=0.12.0
plotly>=5.15.0; extra == "advanced_viz"

# Game-Specific Libraries
pymem>=1.13.0
win32gui>=221.6; sys_platform=="win32"
win32process>=221.6; sys_platform=="win32"

# Hardware Monitoring
nvidia-ml-py>=12.535.0; extra == "gpu"
pynvml>=11.5.0; extra == "gpu"

# Additional Utilities
more-itertools>=10.1.0
toolz>=0.12.0
funcy>=2.0

# Serialization
msgpack>=1.0.0
pickle5>=0.0.12

# Compression & Archives
zipfile38>=0.0.3
tarfile>=0.0.1

# Environment Management
python-dotenv>=1.0.0
environs>=9.5.0

# Logging Extensions
structlog>=23.1.0
loguru>=0.7.0; extra == "advanced_logging"

# HTTP & Web
httpx>=0.24.0
beautifulsoup4>=4.12.0; extra == "web"

# Data Validation
pydantic>=2.1.0
voluptuous>=0.13.0

# Async Programming
trio>=0.22.0; extra == "async"
anyio>=3.7.0; extra == "async"

# Memory Management
pymalloc>=1.0.0
tracemalloc>=1.0

# System Calls
ctypes>=1.1.0
cffi>=1.15.0

# Binary Data
struct>=1.0
array>=1.0

# Regular Expressions
re>=2.2.1
fnmatch>=1.0

# File System
pathlib>=1.0.1
glob>=0.7

# Random & Statistics
random>=1.0
statistics>=1.0.3

# Collections & Data Structures
collections>=1.0
heapq>=1.0
bisect>=1.0

# Iterators & Functional
itertools>=1.0
functools>=1.0
operator>=1.0

# Context Management
contextlib>=1.0
weakref>=1.0

# Copying & Pickling
copy>=1.0
pickle>=1.0

# Warnings & Exceptions
warnings>=1.0
traceback>=1.0

# Inspection & Reflection
inspect>=1.0
types>=1.0

# Garbage Collection
gc>=1.0
sys>=1.0

# Operating System Interface
os>=1.0
shutil>=1.0
tempfile>=1.0

# I/O Operations
io>=1.0
codecs>=1.0

# String Services
string>=1.0
textwrap>=1.0

# Data Types
decimal>=1.70
fractions>=1.0
numbers>=1.0

# Mathematical Functions
math>=1.0
cmath>=1.0

# Cryptographic Services
hashlib>=1.0
hmac>=1.0
secrets>=1.0

# Generic Operating System Services
signal>=1.0
socket>=1.0
select>=1.0

# Internet Protocols
urllib>=1.0
http>=1.0
ftplib>=1.0

# Multimedia Services
wave>=1.0
colorsys>=1.0

# Internationalization
locale>=1.0
gettext>=1.0

# Program Frameworks
cmd>=1.0
shlex>=1.0

# Development Tools
pdb>=1.0
profile>=1.0
timeit>=1.0
trace>=1.0

# Python Runtime Services
atexit>=1.0
traceback>=1.0
__future__>=1.0

# Custom Modules
importlib>=1.0
pkgutil>=1.0
modulefinder>=1.0

# Python Language Services
ast>=1.0
symtable>=1.0
token>=1.0
tokenize>=1.0
keyword>=1.0

# Miscellaneous Services
formatter>=1.0
