#!/usr/bin/env python3
"""
🎯 MEMORY INJECTION AIMBOT
=========================
Direct memory access for maximum performance and accuracy
Reads game memory for player positions and injects aim data
"""

import ctypes
import ctypes.wintypes
import struct
import time
import threading
import keyboard
import math
import numpy as np
from dataclasses import dataclass
from typing import List, Optional, Tuple
from game_offsets import get_game_offsets, GameOffsets

# Windows API constants
PROCESS_ALL_ACCESS = 0x1F0FFF
PROCESS_VM_READ = 0x0010
PROCESS_VM_WRITE = 0x0020
PROCESS_VM_OPERATION = 0x0008

# Memory protection constants
PAGE_EXECUTE_READWRITE = 0x40
PAGE_READWRITE = 0x04

@dataclass
class Player:
    """Player data structure"""
    x: float
    y: float
    z: float
    health: float
    team: int
    valid: bool = True

class MemoryInjectionAimbot:
    def __init__(self):
        # Core settings
        self.running = False
        self.aimbot_active = False
        self.process_handle = None
        self.process_id = 0
        self.base_address = 0
        
        # Aimbot settings
        self.fov = 90.0
        self.smoothing = 2.0
        self.aim_key = "mouse_left"  # Left mouse button
        self.target_bone = "head"  # head, chest, etc.
        
        # Game offsets (will be set when process is found)
        self.offsets = None
        
        # Performance tracking
        self.fps = 0
        self.targets_found = 0
        
        print("🎯 Memory Injection Aimbot initializing...")
        
    def find_process(self, process_name: str) -> bool:
        """Find target game process"""
        try:
            import psutil
            
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'].lower() == process_name.lower():
                    self.process_id = proc.info['pid']

                    # Load game-specific offsets
                    self.offsets = get_game_offsets(process_name)
                    print(f"✅ Found process: {process_name} (PID: {self.process_id})")
                    print(f"📋 Loaded offsets for: {self.offsets.process_name}")
                    return True
                    
            print(f"❌ Process not found: {process_name}")
            return False
            
        except ImportError:
            print("❌ psutil not available. Install with: pip install psutil")
            return False
        except Exception as e:
            print(f"❌ Error finding process: {e}")
            return False
    
    def open_process(self) -> bool:
        """Open process handle with required permissions"""
        try:
            self.process_handle = ctypes.windll.kernel32.OpenProcess(
                PROCESS_ALL_ACCESS, False, self.process_id
            )
            
            if not self.process_handle:
                print("❌ Failed to open process handle")
                return False
                
            print("✅ Process handle opened successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error opening process: {e}")
            return False
    
    def read_memory(self, address: int, size: int) -> Optional[bytes]:
        """Read memory from target process"""
        try:
            buffer = ctypes.create_string_buffer(size)
            bytes_read = ctypes.c_size_t()
            
            success = ctypes.windll.kernel32.ReadProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                buffer,
                size,
                ctypes.byref(bytes_read)
            )
            
            if success and bytes_read.value == size:
                return buffer.raw
            return None
            
        except Exception as e:
            print(f"❌ Memory read error: {e}")
            return None
    
    def write_memory(self, address: int, data: bytes) -> bool:
        """Write memory to target process"""
        try:
            bytes_written = ctypes.c_size_t()
            
            success = ctypes.windll.kernel32.WriteProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                data,
                len(data),
                ctypes.byref(bytes_written)
            )
            
            return success and bytes_written.value == len(data)
            
        except Exception as e:
            print(f"❌ Memory write error: {e}")
            return False
    
    def read_float(self, address: int) -> Optional[float]:
        """Read float value from memory"""
        data = self.read_memory(address, 4)
        if data:
            return struct.unpack('<f', data)[0]
        return None
    
    def read_int(self, address: int) -> Optional[int]:
        """Read integer value from memory"""
        data = self.read_memory(address, 4)
        if data:
            return struct.unpack('<i', data)[0]
        return None
    
    def write_float(self, address: int, value: float) -> bool:
        """Write float value to memory"""
        data = struct.pack('<f', value)
        return self.write_memory(address, data)
    
    def get_module_base(self, module_name: str) -> int:
        """Get base address of a module"""
        try:
            import psutil
            
            process = psutil.Process(self.process_id)
            for module in process.memory_maps():
                if module_name.lower() in module.path.lower():
                    return int(module.addr, 16)
                    
            return 0
            
        except Exception as e:
            print(f"❌ Error getting module base: {e}")
            return 0
    
    def read_players(self) -> List[Player]:
        """Read all player data from memory"""
        players = []
        
        try:
            player_base = self.base_address + self.offsets.player_base
            
            for i in range(self.offsets.max_players):
                player_addr = player_base + (i * self.offsets.player_size)
                
                # Read player position
                x = self.read_float(player_addr + self.offsets.pos_x)
                y = self.read_float(player_addr + self.offsets.pos_y)
                z = self.read_float(player_addr + self.offsets.pos_z)
                
                # Read player health and team
                health = self.read_float(player_addr + self.offsets.health)
                team = self.read_int(player_addr + self.offsets.team)
                
                # Validate player data
                if (x is not None and y is not None and z is not None and 
                    health is not None and health > 0 and team is not None):
                    
                    players.append(Player(
                        x=x, y=y, z=z,
                        health=health,
                        team=team,
                        valid=True
                    ))
            
            self.targets_found = len(players)
            return players
            
        except Exception as e:
            print(f"❌ Error reading players: {e}")
            return []
    
    def get_local_player_data(self) -> Optional[Tuple[float, float, float, float, float]]:
        """Get local player position and view angles"""
        try:
            local_addr = self.base_address + self.offsets.local_player
            
            # Read position
            x = self.read_float(local_addr + self.offsets.pos_x)
            y = self.read_float(local_addr + self.offsets.pos_y)
            z = self.read_float(local_addr + self.offsets.pos_z)
            
            # Read view angles
            view_addr = local_addr + self.offsets.view_angles
            pitch = self.read_float(view_addr)
            yaw = self.read_float(view_addr + 4)
            
            if all(v is not None for v in [x, y, z, pitch, yaw]):
                return x, y, z, pitch, yaw
                
            return None
            
        except Exception as e:
            print(f"❌ Error reading local player: {e}")
            return None
    
    def calculate_angles(self, local_pos: Tuple[float, float, float], 
                        target_pos: Tuple[float, float, float]) -> Tuple[float, float]:
        """Calculate aim angles to target"""
        try:
            # Calculate vector to target
            dx = target_pos[0] - local_pos[0]
            dy = target_pos[1] - local_pos[1]
            dz = target_pos[2] - local_pos[2]
            
            # Calculate distance
            distance = math.sqrt(dx*dx + dy*dy + dz*dz)
            
            if distance == 0:
                return 0.0, 0.0
            
            # Calculate angles
            yaw = math.atan2(dy, dx) * 180.0 / math.pi
            pitch = -math.asin(dz / distance) * 180.0 / math.pi
            
            return pitch, yaw
            
        except Exception as e:
            print(f"❌ Angle calculation error: {e}")
            return 0.0, 0.0
    
    def is_in_fov(self, current_angles: Tuple[float, float], 
                  target_angles: Tuple[float, float]) -> bool:
        """Check if target is within FOV"""
        try:
            pitch_diff = abs(target_angles[0] - current_angles[0])
            yaw_diff = abs(target_angles[1] - current_angles[1])
            
            # Handle yaw wraparound
            if yaw_diff > 180:
                yaw_diff = 360 - yaw_diff
            
            total_diff = math.sqrt(pitch_diff*pitch_diff + yaw_diff*yaw_diff)
            return total_diff <= self.fov
            
        except Exception as e:
            print(f"❌ FOV check error: {e}")
            return False
    
    def find_best_target(self, players: List[Player], local_data: Tuple[float, float, float, float, float]) -> Optional[Player]:
        """Find the best target to aim at"""
        if not players or not local_data:
            return None
            
        local_pos = (local_data[0], local_data[1], local_data[2])
        current_angles = (local_data[3], local_data[4])
        
        best_target = None
        best_distance = float('inf')
        
        for player in players:
            if not player.valid or player.health <= 0:
                continue
                
            target_pos = (player.x, player.y, player.z)
            target_angles = self.calculate_angles(local_pos, target_pos)
            
            # Check if target is in FOV
            if not self.is_in_fov(current_angles, target_angles):
                continue
            
            # Calculate distance
            distance = math.sqrt(
                (player.x - local_pos[0])**2 + 
                (player.y - local_pos[1])**2 + 
                (player.z - local_pos[2])**2
            )
            
            if distance < best_distance:
                best_distance = distance
                best_target = player
        
        return best_target
    
    def inject_aim(self, target_angles: Tuple[float, float], current_angles: Tuple[float, float]) -> bool:
        """Inject aim angles into game memory"""
        try:
            # Calculate smooth aim adjustment
            pitch_diff = target_angles[0] - current_angles[0]
            yaw_diff = target_angles[1] - current_angles[1]
            
            # Handle yaw wraparound
            if yaw_diff > 180:
                yaw_diff -= 360
            elif yaw_diff < -180:
                yaw_diff += 360
            
            # Apply smoothing
            smooth_pitch = current_angles[0] + (pitch_diff / self.smoothing)
            smooth_yaw = current_angles[1] + (yaw_diff / self.smoothing)
            
            # Write new angles to memory
            view_addr = self.base_address + self.offsets.local_player + self.offsets.view_angles
            
            success1 = self.write_float(view_addr, smooth_pitch)
            success2 = self.write_float(view_addr + 4, smooth_yaw)
            
            return success1 and success2
            
        except Exception as e:
            print(f"❌ Aim injection error: {e}")
            return False
    
    def aimbot_loop(self):
        """Main aimbot loop"""
        print("🎯 Memory injection aimbot loop started")
        frame_count = 0
        start_time = time.time()
        
        while self.running:
            try:
                if not self.aimbot_active:
                    time.sleep(0.01)
                    continue
                
                # Read local player data
                local_data = self.get_local_player_data()
                if not local_data:
                    time.sleep(0.01)
                    continue
                
                # Read all players
                players = self.read_players()
                if not players:
                    time.sleep(0.01)
                    continue
                
                # Find best target
                target = self.find_best_target(players, local_data)
                if not target:
                    time.sleep(0.01)
                    continue
                
                # Calculate aim angles
                local_pos = (local_data[0], local_data[1], local_data[2])
                current_angles = (local_data[3], local_data[4])
                target_pos = (target.x, target.y, target.z)
                target_angles = self.calculate_angles(local_pos, target_pos)
                
                # Inject aim
                self.inject_aim(target_angles, current_angles)
                
                # Performance tracking
                frame_count += 1
                if frame_count % 100 == 0:
                    elapsed = time.time() - start_time
                    self.fps = 100 / elapsed if elapsed > 0 else 0
                    start_time = time.time()
                    print(f"🎯 FPS: {self.fps:.1f} | Targets: {self.targets_found} | Active: {self.aimbot_active}")
                
                time.sleep(0.001)  # High frequency updates
                
            except Exception as e:
                print(f"❌ Aimbot loop error: {e}")
                time.sleep(0.1)
    
    def toggle_aimbot(self):
        """Toggle aimbot activation"""
        self.aimbot_active = not self.aimbot_active
        status = "🔥 ACTIVE" if self.aimbot_active else "💤 INACTIVE"
        print(f"🎯 MEMORY AIMBOT {status}")
    
    def start(self, process_name: str = "game.exe"):
        """Start the memory injection aimbot"""
        print("🚀 Starting Memory Injection Aimbot...")
        print(f"🔍 Looking for process: {process_name}")
        
        # Find and attach to process
        if not self.find_process(process_name):
            print("❌ Cannot start - target process not found")
            return
        
        if not self.open_process():
            print("❌ Cannot start - failed to open process")
            return
        
        # Get base address for the correct module
        module_name = self.offsets.module_name if self.offsets else process_name
        self.base_address = self.get_module_base(module_name)
        if not self.base_address:
            print(f"❌ Cannot start - failed to get base address for {module_name}")
            return
        
        print(f"✅ Base address: 0x{self.base_address:X}")
        print("🎮 Controls:")
        print("   F1: Toggle aimbot ON/OFF")
        print("   F2: Quit")
        print("⚠️  AIMBOT STARTS INACTIVE - Press F1 to activate!")
        
        self.running = True
        
        # Start aimbot thread
        aimbot_thread = threading.Thread(target=self.aimbot_loop, daemon=True)
        aimbot_thread.start()
        
        # Hotkey handlers
        keyboard.add_hotkey('f1', self.toggle_aimbot)
        keyboard.add_hotkey('f2', self.stop)
        
        try:
            keyboard.wait('f2')  # Wait for F2 to quit
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self):
        """Stop the memory injection aimbot"""
        print("🛑 Stopping Memory Injection Aimbot...")
        self.running = False
        self.aimbot_active = False
        
        # Close process handle
        if self.process_handle:
            ctypes.windll.kernel32.CloseHandle(self.process_handle)
            self.process_handle = None

if __name__ == "__main__":
    print("🎯 MEMORY INJECTION AIMBOT")
    print("==========================")
    print("Direct memory access for maximum performance")
    print("Reads game memory and injects aim data")
    print()
    
    # Example usage - replace with actual game process name
    process_name = input("Enter game process name (e.g., 'csgo.exe'): ").strip()
    if not process_name:
        process_name = "notepad.exe"  # Default for testing
    
    aimbot = MemoryInjectionAimbot()
    aimbot.start(process_name)
