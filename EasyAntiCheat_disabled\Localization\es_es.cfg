#----------------------------------------------------------
# Easy Anti-Cheat Localization File
#
# This file must use UTF-8 encoding.
#
# Each localization file should be named ll_CC.cfg where
# ll = ISO-639 code for language (e.g. "en")
# CC = ISO-3166 code for country (e.g. "US")
#----------------------------------------------------------

bugreport:
{
	btn_continue = "Conéctate a Internet para buscar una solución y cerrar el juego.";
	btn_exit = "Cerrar el juego.";
	btn_hidedetails = "Ocultar detalles";
	btn_showdetails = "Mostrar detalles";
	chk_sendreport = "Enviar informe de errores";
	error_code = "Código de error:";
	lbl_body1 = "Lo sentimos, se ha producido un problema al iniciar la partida.";
	lbl_body2 = "Ayúdanos enviándonos un informe del problema.";
	lbl_body3 = "Easy Anti-Cheat puede buscar una solución al problema en Internet para intentar resolverlo.";
	lbl_header = "No se puede iniciar la partida";
	title = "Error al iniciar";
};
game_error:
{
	error_catalogue_corrupted = "Catálogo de hash de Easy Anti-Cheat dañado";
	error_catalogue_not_found = "No se ha encontrado el catálogo de EAC";
	error_certificate_revoked = "Se ha revocado el certificado del catálogo de EAC";
	error_corrupted_memory = "Memoria dañada";
	error_corrupted_network = "Flujo de paquetes dañado";
	error_file_forbidden = "Archivo de juego desconocido";
	error_file_not_found = "Falta un archivo necesario";
	error_file_version = "Versión de archivo desconocida";
	error_module_forbidden = "Módulo prohibido";
	error_system_configuration = "Configuración del sistema prohibida";
	error_system_version = "Archivo de sistema inseguro";
	error_tool_forbidden = "Herramienta prohibida";
	error_violation = "Error interno antitrampas";
	error_virtual = "No se puede ejecutar en máquinas virtuales.";
	peer_client_banned = "La validación antitrampas ha sido bloqueada.";
	peer_heartbeat_rejected = "La validación antitrampas ha sido rechazada.";
	peer_validated = "Se ha completado la validación antitrampas.";
	peer_validation_failed = "La validación antitrampas ha fallado.";
	executable_not_hashed = "No se ha podido localizar la entrada del ejecutable del juego en el catálogo.";
};
launcher:
{
	btn_cancel = "Cancelar";
	btn_exit = "Salir";
	error_cancel = "Se ha cancelado el inicio";
	error_filenotfound = "Archivo no encontrado";
	error_init = "Error al iniciar";
	error_install = "Error al instalar";
	error_launch = "Error al iniciar";
	error_nolib = "No se pudo cargar la biblioteca de Easy Anti-Cheat";
	loading = "CARGANDO";
	wait = "Espera";
	initializing = "INICIALIZANDO";
	success_waiting_for_game = "ESPERANDO AL JUEGO";
	success_closing = "Todo ha ido bien";
	network_error = "Error de red";
	error_no_settings_file = "{0} no se ha encontrado";
	error_invalid_settings_format = "{0} no tiene un formato JSON válido";
	error_missing_required_field = "A {0} le falta un campo obligatorio ({1})";
	error_invalid_eos_identifier = "{0} contiene un identificador de EOS inválido: ({1})";
	download_progress = "Progreso de la descarga: {0}";
};
launcher_error:
{
	error_already_running = "¡Ya está en ejecución una aplicación que usa Easy Anti-Cheat! {0}";
	error_application = "El cliente del juego ha encontrado un error en la aplicación. Código de error: {0}";
	error_bad_exe_format = "Se necesita sistema operativo de 64 bits";
	error_bitset_32 = "Tienes que usar la versión 32-bit del juego";
	error_bitset_64 = "Tienes que usar la versión 64-bit del juego";
	error_cancelled = "Operación cancelada por el usuario";
	error_certificate_validation = "Error al validar el certificado de firmas del código de Easy Anti-Cheat";
	error_connection = "¡Ha fallado la conexión a la red de distribución de contenido!";
	error_debugger = "Se ha detectado un programa de depuración. Desactívalo y vuelve a intentarlo.";
	error_disk_space = "Espacio insuficiente en el disco.";
	error_dns = "¡Ha fallado la resolución DNS para la red de distribución de contenidos!";
	error_dotlocal = "Se ha detectado una redirección DotLocal DLL.";
	error_dotlocal_instructions = "Borra el siguiente archivo";
	error_file_not_found = "Archivo no encontrado:";
	error_forbidden_tool = "Cierra {0} antes de iniciar el juego";
	error_forbidden_driver = "Descarga {0} antes de iniciar el juego";
	error_generic = "Error inesperado.";
	error_kernel_debug = "Easy Anti-Cheat no se puede ejecutar si la depuración del kernel está activa";
	error_kernel_dse = "Easy Anti-Cheat no se podrá ejecutar si se ha deshabilitado el uso obligatorio de controladores firmados";
	error_kernel_modified = "Se ha detectado una modificación prohibida del kernel de Windows";
	error_library_load = "No se pudo cargar la biblioteca de Easy Anti-Cheat";
	error_memory = "Memoria insuficiente para iniciar partida";
	error_module_load = "No se ha podido cargar el módulo antitrampas";
	error_patched = "Se ha detectado un iniciador de Windows parcheado";
	error_process = "No se puede crear el proceso";
	error_process_crash = "El proceso se ha detenido abruptamente";
	error_safe_mode = "Easy Anti-Cheat no se puede ejecutar con el modo seguro de Windows";
	error_socket = "¡Algo está impidiendo que la aplicación acceda a Internet!";
	error_ssl = "¡Se ha producido un error al establecer una conexión SSL con el servicio CDN!";
	error_start = "No se ha podido iniciar la partida";
	error_uncpath_forbidden = "Imposible ejecutar el juego a través de un recurso compartido de red. (ruta de acceso UNC).";
	error_connection_failed = "Error de conexión: ";
	error_missing_game_id = "ID del juego no encontrada";
	error_dns_resolve_failed = "Error en la resolución del DNS a través del proxy";
	error_dns_connection_failed = "¡Error en la conexión a la red de distribución de contenidos! Código Curl: {0}!";
	error_http_response = "Código de respuesta HTTP: {0} Código Curl: {1}";
	error_driver_handle = "Error inesperado. (Error al abrir el administrador de controladores)";
	error_incompatible_service = "Ya se está ejecutando un servicio incompatible de Easy Anti-Cheat. Cierra los otros juegos que estés ejecutando o reinicia el equipo.";
	error_incompatible_driver_version = "Ya se está ejecutando una versión del controlador Easy Anti-Cheat que resulta incompatible. Cierra los otros juegos que estés ejecutando o reinicia el equipo.";
	error_another_launcher = "Error inesperado. (Se está ejecutando otro iniciador)";
	error_game_running = "Error inesperado. (El juego ya se está ejecutando)";
	error_patched_boot_loader = "Se ha detectado un iniciador de Windows modificado. (Se ha desactivado la protección contra revisiones del núcleo)";
	error_unknown_process = "Cliente de juego no reconocido. No se puede continuar.";
	error_unknown_game = "Juego sin configurar. No se puede continuar.";
	error_win7_required = "Se necesita Windows 7 o posteriores.";
	success_initialized = "Se ha iniciado con éxito Easy Anti-Cheat";
	success_loaded = "Se ha cargado con éxito Easy Anti-Cheat en el juego";
	error_create_process = "No se ha podido crear el proceso del juego: {0}";
	error_create_thread = "No se ha podido crear el subproceso en segundo plano.";
	error_disallowed_cdn_path = "Error inesperado. (URL de CDN incorrecta).";
	error_empty_executable_field = "No se ha proporcionado la ruta a los archivos binarios del juego.";
	error_failed_path_query = "No se ha podido obtener la ruta del proceso.";
	error_failed_to_execute = "No se ha podido ejecutar el proceso del juego.";
	error_game_binary_is_directory = "El ejecutable objetivo es un directorio.";
	error_game_binary_is_dot_app = "El ejecutable objetivo es un directorio, especifica el archivo binario dentro de la .app en su lugar.";
	error_game_binary_not_found = "No se ha podido localizar el archivo binario del juego: '{0}'";
	error_game_binary_not_found_wine = "No se ha podido localizar el archivo binario del juego (Wine)";
	error_game_security_violation = "Infracción de seguridad de juego {0}";
	error_generic_ex = "Error inesperado. {0}";
	error_instance_count_limit = "Se ha alcanzado el número máximo de instancias simultáneas del juego.";
	error_internal = "Error interno.";
	error_invalid_executable_path = "La ruta del ejecutable del juego no es válida.";
	error_memory_ex = "No hay memoria suficiente para iniciar el juego {0}";
	error_missing_binary_path = "No se encuentra la ruta del ejecutable del juego.";
	error_missing_directory_path = "No se encuentra la ruta del directorio de trabajo del juego.";
	error_module_initialize = "Error de iniciación del módulo con {0}";
	error_module_loading = "No se ha podido cargar el módulo antitrampas.";
	error_set_environment_variables = "No se han podido establecer las variables de entorno para el proceso del juego.";
	error_unrecognized_blacklisted_driver = "Se ha detectado N/A. Descárgalo e inténtalo de nuevo.";
	error_unsupported_machine_arch = "Arquitectura de equipo anfitrión no compatible ({0}).";
	error_working_directory_not_found = "El directorio de trabajo no existe.";
	error_x8664_required = "Sistema operativo no compatible. Se requiere la versión de Windows de 64 bits (x86 64).";
	warn_module_download_size = "Tamaño de respuesta HTTP: {0}. Iniciando el modo 'null client'.";
	warn_vista_deprecation = "Easy Anti-Cheat tiene que dejar de ser compatible con Windows Vista en octubre de 2020 porque ya no se pueden crear firmas de código compatibles. Consulta https://support.microsoft.com/help/4472027/2019-sha-2-code-signing-support-requirement-for-windows-and-wsus";
	error_configuration = "No se ha podido validar la configuración antitrampas.";
	warn_win7_update_required = "Actualiza Windows, tu sistema no es compatible con las firmas de código SHA-2 cruciales que se requieren a partir de octubre de 2020. Consulta https://support.microsoft.com/help/4474419/sha-2-code-signing-support-update";
	error_service_update_timeout = "Se ha agotado el tiempo de actualización del servicio Easy Anti-Cheat."
	error_launch_ex = "Error al iniciar: {0}";
};
setup:
{
	btn_finish = "Acabar";
	btn_install = "Instalar ahora";
	btn_repair = "Servicio de reparaciones";
	btn_uninstall = "Desinstalar";
	epic_link = "© Epic Games, Inc";
	install_progress = "Instalando...";
	install_success = "Se ha instalado con éxito";
	licenses_link = "Licencias";
	privacy_link = "Privacidad";
	repair_progress = "Reparando...";
	title = "Instalación del servicio Easy Anti-Cheat";
	uninstall_progress = "Desinstalando...";
	uninstall_success = "Se ha desinstalado con éxito";
};
setup_error:
{
	error_cancelled = "Operación cancelada por el usuario";
	error_encrypted = "Se ha encriptado la carpeta de instalación de Easy Anti-Cheat";
	error_intro = "Ha fallado la configuración de Easy Anti-Cheat";
	error_not_installed = "Easy Anti-Cheat no está instalado.";
	error_registry = "Fallo al copiar el ejecutable del servicio";
	error_rights = "Privilegios insuficientes";
	error_service = "No se puede crear el servicio";
	error_service_ex = "No se puede crear el servicio {0}";
	error_system = "Se ha denegado el acceso a System32";
};