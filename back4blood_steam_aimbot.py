#!/usr/bin/env python3
"""
🎯 BACK 4 BLOOD STEAM AIMBOT - COMPLETE SYSTEM
==============================================
Advanced aimbot suite specifically optimized for Back 4 Blood Steam version
with full memory access, zombie detection, ESP, and comprehensive features.

⚠️  EDUCATIONAL PURPOSE ONLY ⚠️
This software is for educational and research purposes only.
"""

import sys
import os
import time
import math
import random
import threading
import logging
import struct
import ctypes
from ctypes import wintypes
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from collections import deque
from enum import Enum
import json

# GUI imports
import tkinter as tk
from tkinter import ttk, messagebox

# Computer Vision imports
try:
    import cv2
    import numpy as np
    CV_AVAILABLE = True
except ImportError:
    CV_AVAILABLE = False
    print("⚠️ Installing OpenCV...")
    os.system("pip install opencv-python")

# AI Detection imports
try:
    from ultralytics import YOLO
    import torch
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    print("⚠️ Installing AI libraries...")
    os.system("pip install ultralytics torch")

# System imports
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("⚠️ Installing psutil...")
    os.system("pip install psutil")

# Windows API imports
try:
    import win32api
    import win32con
    import win32gui
    import win32process
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("⚠️ Installing Win32 API...")
    os.system("pip install pywin32")

# Educational warning
EDUCATIONAL_WARNING = """
🎯 BACK 4 BLOOD STEAM AIMBOT - EDUCATIONAL SOFTWARE
===================================================

⚠️  CRITICAL EDUCATIONAL NOTICE ⚠️

This comprehensive aimbot suite is designed for EDUCATIONAL and RESEARCH purposes only.

BACK 4 BLOOD STEAM FEATURES:
🧟 Advanced Zombie Detection & Classification
🎯 Multi-Mode Aimbot (Sticky, Snap, Smooth, Rage, Legit, Horde, Boss)
💾 Steam Process Memory Reading & Injection
🔍 Hybrid Detection (Computer Vision + AI + Memory)
🎮 Advanced Input Simulation with Humanization
🛡️ Comprehensive Anti-Detection System
📊 Real-Time Performance Monitoring
⚙️ Extensive Configuration Options
🎨 ESP Overlay with Zombie Information
🔫 Intelligent Trigger Bot with Burst Mode
📈 Advanced Analytics & Statistics
🎪 Horde Mode Optimization
💀 Boss Enemy Specialized Targeting
🏃 Movement Prediction for Fast Zombies
🔥 Weak Point Targeting System
🛡️ Team Damage Prevention
📍 Objective-Based Priority System

STEAM SPECIFIC OPTIMIZATIONS:
🎮 Steam Overlay Compatibility
🔧 Steam Process Detection
💾 Steam Memory Layout Support
🎯 Steam Input Handling
📊 Steam Achievement Integration
🔄 Steam Cloud Save Compatibility

PERMITTED USES:
✅ Learning about computer vision, AI, and memory manipulation
✅ Understanding game mechanics and reverse engineering
✅ Research into human-computer interaction
✅ Educational demonstrations and tutorials
✅ Offline testing and experimentation
✅ Security research and vulnerability assessment

PROHIBITED USES:
❌ Online competitive gaming
❌ Violating Steam Terms of Service
❌ Gaining unfair advantages in multiplayer games
❌ Commercial use without proper licensing
❌ Malicious or harmful activities
❌ Circumventing anti-cheat systems

By using this software, you acknowledge that you understand and
agree to these terms and will use it responsibly for educational purposes only.
"""

class AimMode(Enum):
    """Aimbot modes optimized for Back 4 Blood"""
    STICKY = "sticky"           # Locks onto zombies until eliminated
    SNAP = "snap"              # Instant snap to nearest zombie
    SMOOTH = "smooth"          # Smooth tracking
    RAGE = "rage"              # Maximum aggression for horde mode
    LEGIT = "legit"            # Human-like aiming
    TRIGGER = "trigger"        # Auto-fire when on zombie
    FLICK = "flick"            # Quick flick shots
    PREDICTION = "prediction"   # Predictive aiming
    HORDE = "horde"            # Optimized for horde encounters
    BOSS = "boss"              # Specialized for boss enemies

class ZombieType(Enum):
    """Back 4 Blood zombie classifications"""
    COMMON = "common"          # Regular zombies
    RIDDEN = "ridden"          # Special infected
    TALLBOY = "tallboy"        # Tallboy variants (Crusher, Bruiser)
    STINGER = "stinger"        # Stinger variants (Hocker, Stalker)
    REEKER = "reeker"          # Reeker variants (Retch, Exploder)
    OGRE = "ogre"              # Boss: Ogre
    BREAKER = "breaker"        # Boss: Breaker
    ABOMINATION = "abomination" # Boss: Abomination
    SLEEPER = "sleeper"        # Sleeper zombies
    SNITCH = "snitch"          # Snitch (alerts horde)

@dataclass
class ZombieTarget:
    """Enhanced zombie target data structure"""
    id: str
    x: float
    y: float
    z: float = 0.0
    width: float = 50.0
    height: float = 50.0
    confidence: float = 1.0
    detection_method: str = "unknown"
    zombie_type: ZombieType = ZombieType.COMMON
    timestamp: float = field(default_factory=time.time)
    
    # Zombie-specific data
    health: float = 100.0
    max_health: float = 100.0
    armor: float = 0.0
    is_special: bool = False
    is_boss: bool = False
    is_alert: bool = False
    threat_level: float = 1.0
    distance: float = 0.0
    angle: float = 0.0
    
    # Movement data
    velocity_x: float = 0.0
    velocity_y: float = 0.0
    velocity_z: float = 0.0
    is_moving: bool = False
    movement_speed: float = 0.0
    
    # Targeting data
    lock_priority: float = 0.0
    lock_duration: float = 0.0
    hit_probability: float = 0.0
    last_hit_time: float = 0.0
    
    # Bone positions for precise targeting
    head_pos: Optional[Tuple[float, float, float]] = None
    chest_pos: Optional[Tuple[float, float, float]] = None
    weak_spot_pos: Optional[Tuple[float, float, float]] = None
    center_mass: Optional[Tuple[float, float, float]] = None

class SteamProcessManager:
    """Steam process detection and management for Back 4 Blood"""
    
    def __init__(self):
        """Initialize Steam process manager"""
        self.process_handle = None
        self.process_id = None
        self.base_address = None
        self.steam_id = None
        
        # Back 4 Blood Steam process names
        self.process_names = [
            'Back4Blood-Win64-Shipping.exe',
            'b4b.exe',
            'Back4Blood.exe'
        ]
        
        # Steam-specific offsets (would be found through reverse engineering)
        self.offsets = {
            # Player data
            'local_player_base': 0x4A5B8C0,
            'player_position': 0x1A0,
            'player_view_angles': 0x1B4,
            'player_health': 0x1C8,
            'player_team': 0x1DC,
            'player_weapon': 0x2C8,
            
            # Entity list
            'entity_list': 0x4B7F4A0,
            'entity_count': 0x4B7F4A4,
            'entity_size': 0x3F8,
            
            # Zombie data offsets
            'entity_position': 0x164,
            'entity_health': 0x178,
            'entity_max_health': 0x17C,
            'entity_type': 0x18C,
            'entity_state': 0x1A0,
            'entity_team': 0x1B4,
            'entity_velocity': 0x1C8,
            
            # Bone data
            'bone_matrix': 0x2A0,
            'head_bone_id': 6,
            'chest_bone_id': 4,
            'pelvis_bone_id': 1,
            
            # Game state
            'game_mode': 0x5A5C7B0,
            'round_state': 0x5A5C7B4,
            'horde_active': 0x5A5C7B8,
            'difficulty': 0x5A5C7BC,
            
            # View matrix for world-to-screen
            'view_matrix': 0x5B8F2C0,
            
            # Steam integration
            'steam_api_base': 0x6C8F000,
            'steam_user_id': 0x6C8F010
        }
        
        # Windows API setup
        if WIN32_AVAILABLE:
            self.kernel32 = ctypes.windll.kernel32
            self.user32 = ctypes.windll.user32
            
            # Process access rights
            self.PROCESS_ALL_ACCESS = 0x1F0FFF
            self.PROCESS_VM_READ = 0x0010
            self.PROCESS_VM_WRITE = 0x0020
            self.PROCESS_VM_OPERATION = 0x0008
    
    def find_back4blood_steam_process(self) -> bool:
        """Find and attach to Back 4 Blood Steam process"""
        if not WIN32_AVAILABLE or not PSUTIL_AVAILABLE:
            print("⚠️ Required libraries not available")
            return False
        
        try:
            print("🔍 Searching for Back 4 Blood Steam process...")
            
            # Search for Back 4 Blood process
            for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info.get('name', '').lower()
                    
                    # Check if it's a Back 4 Blood process
                    for target_name in self.process_names:
                        if target_name.lower() in proc_name:
                            # Verify it's the Steam version
                            cmdline = proc_info.get('cmdline', [])
                            exe_path = proc_info.get('exe', '')
                            
                            if self._is_steam_version(cmdline, exe_path):
                                self.process_id = proc_info['pid']
                                print(f"✅ Found Back 4 Blood Steam process (PID: {self.process_id})")
                                return self._attach_to_process()
                
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            print("❌ Back 4 Blood Steam process not found")
            print("   Make sure Back 4 Blood is running through Steam")
            return False
            
        except Exception as e:
            print(f"❌ Error finding process: {e}")
            return False
    
    def _is_steam_version(self, cmdline: List[str], exe_path: str) -> bool:
        """Check if the process is the Steam version"""
        if not cmdline and not exe_path:
            return False
        
        # Check for Steam indicators
        steam_indicators = [
            'steam',
            'steamapps',
            'common',
            '-steam',
            'steam_appid'
        ]
        
        # Check command line arguments
        cmdline_str = ' '.join(cmdline).lower()
        for indicator in steam_indicators:
            if indicator in cmdline_str:
                return True
        
        # Check executable path
        if exe_path and 'steamapps' in exe_path.lower():
            return True
        
        return False
    
    def _attach_to_process(self) -> bool:
        """Attach to the found process"""
        try:
            # Open process handle
            self.process_handle = self.kernel32.OpenProcess(
                self.PROCESS_ALL_ACCESS, False, self.process_id
            )
            
            if not self.process_handle:
                print("❌ Failed to open process handle")
                return False
            
            # Get base address
            self.base_address = self._get_module_base_address()
            
            if not self.base_address:
                print("❌ Failed to get base address")
                return False
            
            # Get Steam ID if available
            self._get_steam_id()
            
            print(f"✅ Successfully attached to Back 4 Blood Steam")
            print(f"📍 Base Address: 0x{self.base_address:X}")
            if self.steam_id:
                print(f"🎮 Steam ID: {self.steam_id}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to attach to process: {e}")
            return False
    
    def _get_module_base_address(self) -> Optional[int]:
        """Get base address of main module"""
        try:
            # Get process modules
            hModuleSnap = self.kernel32.CreateToolhelp32Snapshot(0x00000008, self.process_id)
            if hModuleSnap == -1:
                return None
            
            # Module entry structure
            class MODULEENTRY32(ctypes.Structure):
                _fields_ = [
                    ("dwSize", wintypes.DWORD),
                    ("th32ModuleID", wintypes.DWORD),
                    ("th32ProcessID", wintypes.DWORD),
                    ("GlblcntUsage", wintypes.DWORD),
                    ("ProccntUsage", wintypes.DWORD),
                    ("modBaseAddr", ctypes.POINTER(wintypes.BYTE)),
                    ("modBaseSize", wintypes.DWORD),
                    ("hModule", wintypes.HMODULE),
                    ("szModule", ctypes.c_char * 256),
                    ("szExePath", ctypes.c_char * 260)
                ]
            
            me32 = MODULEENTRY32()
            me32.dwSize = ctypes.sizeof(MODULEENTRY32)
            
            # Get first module (main executable)
            if self.kernel32.Module32First(hModuleSnap, ctypes.byref(me32)):
                self.kernel32.CloseHandle(hModuleSnap)
                return ctypes.cast(me32.modBaseAddr, ctypes.c_void_p).value
            
            self.kernel32.CloseHandle(hModuleSnap)
            return None
            
        except Exception as e:
            print(f"❌ Failed to get module base address: {e}")
            return None
    
    def _get_steam_id(self):
        """Get Steam ID from memory"""
        try:
            if self.base_address:
                steam_id_addr = self.base_address + self.offsets['steam_user_id']
                steam_id_data = self.read_memory(steam_id_addr, 8)
                if steam_id_data:
                    self.steam_id = struct.unpack('<Q', steam_id_data)[0]
        except Exception as e:
            print(f"⚠️ Could not read Steam ID: {e}")
    
    def read_memory(self, address: int, size: int) -> Optional[bytes]:
        """Read memory from process"""
        if not self.process_handle:
            return None
        
        try:
            buffer = ctypes.create_string_buffer(size)
            bytes_read = ctypes.c_size_t()
            
            success = self.kernel32.ReadProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                buffer,
                size,
                ctypes.byref(bytes_read)
            )
            
            if success and bytes_read.value == size:
                return buffer.raw
            
            return None
            
        except Exception as e:
            print(f"❌ Memory read error: {e}")
            return None
    
    def write_memory(self, address: int, data: bytes) -> bool:
        """Write memory to process"""
        if not self.process_handle:
            return False
        
        try:
            bytes_written = ctypes.c_size_t()
            
            success = self.kernel32.WriteProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                data,
                len(data),
                ctypes.byref(bytes_written)
            )
            
            return success and bytes_written.value == len(data)
            
        except Exception as e:
            print(f"❌ Memory write error: {e}")
            return False
    
    def read_float(self, address: int) -> Optional[float]:
        """Read 32-bit float"""
        data = self.read_memory(address, 4)
        return struct.unpack('<f', data)[0] if data else None
    
    def read_int(self, address: int) -> Optional[int]:
        """Read 32-bit integer"""
        data = self.read_memory(address, 4)
        return struct.unpack('<I', data)[0] if data else None
    
    def read_vector3(self, address: int) -> Optional[Tuple[float, float, float]]:
        """Read 3D vector"""
        data = self.read_memory(address, 12)
        if data:
            x, y, z = struct.unpack('<fff', data)
            return (x, y, z)
        return None
    
    def write_float(self, address: int, value: float) -> bool:
        """Write 32-bit float"""
        data = struct.pack('<f', value)
        return self.write_memory(address, data)
    
    def write_vector3(self, address: int, x: float, y: float, z: float) -> bool:
        """Write 3D vector"""
        data = struct.pack('<fff', x, y, z)
        return self.write_memory(address, data)
    
    def cleanup(self):
        """Cleanup process manager"""
        if self.process_handle and WIN32_AVAILABLE:
            self.kernel32.CloseHandle(self.process_handle)
            self.process_handle = None
            print("🧹 Steam process manager cleaned up")

class Back4BloodGameReader:
    """Game data reader for Back 4 Blood Steam version"""

    def __init__(self, process_manager: SteamProcessManager):
        """Initialize game reader"""
        self.process = process_manager
        self.local_player_data = {}
        self.zombie_list = []
        self.game_state = {}
        self.last_update = 0.0

        # Zombie type mapping
        self.zombie_type_map = {
            0: ZombieType.COMMON,
            1: ZombieType.RIDDEN,
            2: ZombieType.TALLBOY,
            3: ZombieType.STINGER,
            4: ZombieType.REEKER,
            5: ZombieType.OGRE,
            6: ZombieType.BREAKER,
            7: ZombieType.ABOMINATION,
            8: ZombieType.SLEEPER,
            9: ZombieType.SNITCH
        }

        # Threat level mapping
        self.threat_levels = {
            ZombieType.COMMON: 1.0,
            ZombieType.RIDDEN: 2.0,
            ZombieType.TALLBOY: 4.0,
            ZombieType.STINGER: 3.5,
            ZombieType.REEKER: 3.0,
            ZombieType.OGRE: 8.0,
            ZombieType.BREAKER: 9.0,
            ZombieType.ABOMINATION: 10.0,
            ZombieType.SLEEPER: 1.5,
            ZombieType.SNITCH: 5.0  # High priority to prevent alerts
        }

    def update_all_data(self) -> bool:
        """Update all game data"""
        try:
            success = True
            success &= self.update_local_player()
            success &= self.update_zombie_list()
            success &= self.update_game_state()

            self.last_update = time.time()
            return success

        except Exception as e:
            print(f"❌ Game data update error: {e}")
            return False

    def update_local_player(self) -> bool:
        """Update local player data"""
        if not self.process.base_address:
            return False

        try:
            player_base = self.process.base_address + self.process.offsets['local_player_base']

            # Read player data
            position = self.process.read_vector3(player_base + self.process.offsets['player_position'])
            view_angles = self.process.read_vector3(player_base + self.process.offsets['player_view_angles'])
            health = self.process.read_float(player_base + self.process.offsets['player_health'])
            team = self.process.read_int(player_base + self.process.offsets['player_team'])
            weapon = self.process.read_int(player_base + self.process.offsets['player_weapon'])

            self.local_player_data = {
                'position': position or (0, 0, 0),
                'view_angles': view_angles or (0, 0, 0),
                'health': health or 100.0,
                'team': team or 0,
                'weapon': weapon or 0,
                'timestamp': time.time()
            }

            return True

        except Exception as e:
            print(f"❌ Local player update error: {e}")
            return False

    def update_zombie_list(self) -> bool:
        """Update zombie list from memory"""
        if not self.process.base_address:
            return False

        try:
            zombies = []
            entity_list_addr = self.process.base_address + self.process.offsets['entity_list']
            entity_count = self.process.read_int(self.process.base_address + self.process.offsets['entity_count'])

            if not entity_count or entity_count > 500:  # Sanity check
                return False

            player_pos = self.local_player_data.get('position', (0, 0, 0))

            for i in range(min(entity_count, 200)):  # Limit to 200 entities
                entity_addr = entity_list_addr + (i * self.process.offsets['entity_size'])

                # Read entity data
                position = self.process.read_vector3(entity_addr + self.process.offsets['entity_position'])
                health = self.process.read_float(entity_addr + self.process.offsets['entity_health'])
                max_health = self.process.read_float(entity_addr + self.process.offsets['entity_max_health'])
                entity_type = self.process.read_int(entity_addr + self.process.offsets['entity_type'])
                entity_state = self.process.read_int(entity_addr + self.process.offsets['entity_state'])
                team = self.process.read_int(entity_addr + self.process.offsets['entity_team'])
                velocity = self.process.read_vector3(entity_addr + self.process.offsets['entity_velocity'])

                # Skip invalid entities
                if not position or not health or health <= 0 or not entity_type:
                    continue

                # Skip friendly entities (team check)
                player_team = self.local_player_data.get('team', 0)
                if team == player_team:
                    continue

                # Calculate distance
                distance = math.sqrt(
                    (position[0] - player_pos[0])**2 +
                    (position[1] - player_pos[1])**2 +
                    (position[2] - player_pos[2])**2
                )

                # Skip entities too far away
                if distance > 1000.0:
                    continue

                # Determine zombie type
                zombie_type = self.zombie_type_map.get(entity_type, ZombieType.COMMON)

                # Calculate movement data
                movement_speed = 0.0
                is_moving = False
                if velocity:
                    movement_speed = math.sqrt(velocity[0]**2 + velocity[1]**2 + velocity[2]**2)
                    is_moving = movement_speed > 5.0

                # Read bone positions for advanced targeting
                bone_positions = self._read_bone_positions(entity_addr)

                # Create zombie target
                zombie = ZombieTarget(
                    id=f"zombie_{i}",
                    x=position[0],
                    y=position[1],
                    z=position[2],
                    health=health,
                    max_health=max_health or health,
                    zombie_type=zombie_type,
                    is_special=zombie_type != ZombieType.COMMON,
                    is_boss=zombie_type in [ZombieType.OGRE, ZombieType.BREAKER, ZombieType.ABOMINATION],
                    threat_level=self.threat_levels.get(zombie_type, 1.0),
                    distance=distance,
                    velocity_x=velocity[0] if velocity else 0.0,
                    velocity_y=velocity[1] if velocity else 0.0,
                    velocity_z=velocity[2] if velocity else 0.0,
                    is_moving=is_moving,
                    movement_speed=movement_speed,
                    detection_method='memory',
                    head_pos=bone_positions.get('head'),
                    chest_pos=bone_positions.get('chest'),
                    weak_spot_pos=bone_positions.get('weak_spot'),
                    center_mass=(position[0], position[1], position[2] + 30)
                )

                zombies.append(zombie)

            self.zombie_list = zombies
            return True

        except Exception as e:
            print(f"❌ Zombie list update error: {e}")
            return False

    def _read_bone_positions(self, entity_addr: int) -> Dict[str, Tuple[float, float, float]]:
        """Read bone positions for entity"""
        bone_positions = {}

        try:
            bone_matrix_addr = entity_addr + self.process.offsets['bone_matrix']

            # Read head bone
            head_bone_addr = bone_matrix_addr + (self.process.offsets['head_bone_id'] * 48)  # 48 bytes per bone matrix
            head_pos = self.process.read_vector3(head_bone_addr + 12)  # Position offset in matrix
            if head_pos:
                bone_positions['head'] = head_pos

            # Read chest bone
            chest_bone_addr = bone_matrix_addr + (self.process.offsets['chest_bone_id'] * 48)
            chest_pos = self.process.read_vector3(chest_bone_addr + 12)
            if chest_pos:
                bone_positions['chest'] = chest_pos

            # For special infected, try to find weak spots
            # This would require more detailed reverse engineering for each zombie type

        except Exception as e:
            print(f"⚠️ Bone position read error: {e}")

        return bone_positions

    def update_game_state(self) -> bool:
        """Update game state information"""
        if not self.process.base_address:
            return False

        try:
            game_mode = self.process.read_int(self.process.base_address + self.process.offsets['game_mode'])
            round_state = self.process.read_int(self.process.base_address + self.process.offsets['round_state'])
            horde_active = self.process.read_int(self.process.base_address + self.process.offsets['horde_active'])
            difficulty = self.process.read_int(self.process.base_address + self.process.offsets['difficulty'])

            self.game_state = {
                'game_mode': game_mode or 0,
                'round_state': round_state or 0,
                'horde_active': bool(horde_active),
                'difficulty': difficulty or 0,
                'zombie_count': len(self.zombie_list),
                'special_count': len([z for z in self.zombie_list if z.is_special]),
                'boss_count': len([z for z in self.zombie_list if z.is_boss]),
                'timestamp': time.time()
            }

            return True

        except Exception as e:
            print(f"❌ Game state update error: {e}")
            return False

    def world_to_screen(self, world_pos: Tuple[float, float, float]) -> Optional[Tuple[int, int]]:
        """Convert 3D world coordinates to 2D screen coordinates"""
        if not self.process.base_address:
            return None

        try:
            # Read view matrix
            view_matrix_addr = self.process.base_address + self.process.offsets['view_matrix']
            view_matrix = []

            for i in range(16):  # 4x4 matrix
                matrix_value = self.process.read_float(view_matrix_addr + (i * 4))
                if matrix_value is None:
                    return None
                view_matrix.append(matrix_value)

            # Transform world coordinates to screen coordinates
            # This is a simplified version - real implementation would be more complex
            x, y, z = world_pos

            # Apply view matrix transformation
            screen_x = (view_matrix[0] * x + view_matrix[1] * y + view_matrix[2] * z + view_matrix[3])
            screen_y = (view_matrix[4] * x + view_matrix[5] * y + view_matrix[6] * z + view_matrix[7])
            screen_w = (view_matrix[12] * x + view_matrix[13] * y + view_matrix[14] * z + view_matrix[15])

            if screen_w < 0.01:
                return None

            # Normalize to screen coordinates
            screen_x /= screen_w
            screen_y /= screen_w

            # Convert to pixel coordinates (assuming 1920x1080)
            pixel_x = int((screen_x + 1.0) * 960)
            pixel_y = int((1.0 - screen_y) * 540)

            # Check if on screen
            if 0 <= pixel_x <= 1920 and 0 <= pixel_y <= 1080:
                return (pixel_x, pixel_y)

            return None

        except Exception as e:
            print(f"❌ World to screen conversion error: {e}")
            return None

class Back4BloodAimEngine:
    """Advanced aimbot engine for Back 4 Blood Steam"""

    def __init__(self, process_manager: SteamProcessManager, game_reader: Back4BloodGameReader):
        """Initialize aim engine"""
        self.process = process_manager
        self.game_reader = game_reader

        # Aim configuration
        self.aim_mode = AimMode.SMOOTH
        self.target_bone = "head"
        self.fov = 90.0  # Larger FOV for zombie hordes
        self.smoothing = 2.5  # Faster for zombie combat
        self.sensitivity = 1.5

        # Advanced features
        self.weak_point_targeting = True
        self.horde_optimization = True
        self.boss_priority = True
        self.team_damage_prevention = True
        self.prediction_enabled = True
        self.humanization_enabled = True
        self.anti_detection_enabled = True

        # State tracking
        self.current_target = None
        self.lock_start_time = 0.0
        self.last_shot_time = 0.0

        # Statistics
        self.zombies_killed = 0
        self.shots_fired = 0
        self.hits_landed = 0
        self.headshots = 0
        self.special_kills = 0
        self.boss_kills = 0
        self.aim_adjustments = 0
        self.successful_locks = 0

        print("🎯 Back 4 Blood Aim Engine initialized")

    def update(self) -> Optional[Dict[str, Any]]:
        """Main aim engine update"""
        try:
            # Update game data
            if not self.game_reader.update_all_data():
                return None

            # Get valid targets
            valid_zombies = self._get_valid_targets()

            if not valid_zombies:
                if self.current_target:
                    self._release_target()
                return None

            # Select optimal target
            target = self._select_optimal_target(valid_zombies)

            if not target:
                return None

            # Calculate aim adjustment
            adjustment = self._calculate_aim_adjustment(target)

            if adjustment:
                # Apply modifications
                adjustment = self._apply_advanced_modifications(adjustment, target)

                # Inject aim
                if self._inject_aim_adjustment(adjustment):
                    self.aim_adjustments += 1

                    return {
                        'target': target,
                        'adjustment': adjustment,
                        'zombie_count': len(self.game_reader.zombie_list),
                        'valid_targets': len(valid_zombies),
                        'game_state': self.game_reader.game_state
                    }

            return None

        except Exception as e:
            print(f"❌ Aim engine update error: {e}")
            return None

    def _get_valid_targets(self) -> List[ZombieTarget]:
        """Get list of valid zombie targets"""
        valid_targets = []
        player_pos = self.game_reader.local_player_data.get('position', (0, 0, 0))
        player_angles = self.game_reader.local_player_data.get('view_angles', (0, 0, 0))

        for zombie in self.game_reader.zombie_list:
            # Skip dead zombies
            if zombie.health <= 0:
                continue

            # Calculate angle to zombie
            dx = zombie.x - player_pos[0]
            dy = zombie.y - player_pos[1]
            angle = math.degrees(math.atan2(dy, dx))
            zombie.angle = angle

            # Check FOV
            angle_diff = abs(self._normalize_angle(angle - player_angles[1]))
            fov_limit = self.fov / 2

            # Expand FOV for bosses and special infected
            if zombie.is_boss:
                fov_limit *= 1.8
            elif zombie.is_special:
                fov_limit *= 1.4

            if angle_diff <= fov_limit:
                valid_targets.append(zombie)

        return valid_targets

    def _select_optimal_target(self, zombies: List[ZombieTarget]) -> Optional[ZombieTarget]:
        """Select optimal target based on priorities"""
        if not zombies:
            return None

        # Calculate priorities
        for zombie in zombies:
            zombie.lock_priority = self._calculate_priority(zombie)

        # Select based on aim mode
        if self.aim_mode == AimMode.STICKY:
            return self._select_sticky_target(zombies)
        elif self.aim_mode == AimMode.HORDE:
            return self._select_horde_target(zombies)
        elif self.aim_mode == AimMode.BOSS:
            return self._select_boss_target(zombies)
        else:
            return max(zombies, key=lambda z: z.lock_priority)

    def _calculate_priority(self, zombie: ZombieTarget) -> float:
        """Calculate target priority"""
        priority = 0.0

        # Base priority by type
        type_priorities = {
            ZombieType.SNITCH: 10.0,      # Highest - prevents horde alerts
            ZombieType.ABOMINATION: 9.0,
            ZombieType.BREAKER: 8.5,
            ZombieType.OGRE: 8.0,
            ZombieType.TALLBOY: 6.0,
            ZombieType.STINGER: 5.5,
            ZombieType.REEKER: 5.0,
            ZombieType.RIDDEN: 4.0,
            ZombieType.SLEEPER: 2.0,
            ZombieType.COMMON: 1.0
        }
        priority += type_priorities.get(zombie.zombie_type, 1.0) * 2.0

        # Distance factor
        if zombie.distance > 0:
            distance_factor = max(0.1, 1.0 - (zombie.distance / 300.0))
            priority += distance_factor * 1.5

        # Health factor (prioritize low health for finishing)
        health_percent = zombie.health / max(zombie.max_health, 1)
        if health_percent < 0.3:
            priority += 2.0  # Finish off weak enemies

        # Threat level
        priority += zombie.threat_level * 1.2

        # Movement factor (prioritize moving targets in some modes)
        if zombie.is_moving and self.aim_mode in [AimMode.PREDICTION, AimMode.SMOOTH]:
            priority += 0.5

        # Current target bonus
        if self.current_target and zombie.id == self.current_target.id:
            priority *= 1.3

        # Horde mode adjustments
        if self.game_reader.game_state.get('horde_active', False):
            if zombie.is_special:
                priority *= 1.5
            if zombie.distance < 100:  # Close threats in horde
                priority *= 1.4

        return priority

    def _select_sticky_target(self, zombies: List[ZombieTarget]) -> Optional[ZombieTarget]:
        """Select target for sticky mode"""
        if self.current_target:
            for zombie in zombies:
                if zombie.id == self.current_target.id and zombie.lock_priority > 2.0:
                    return zombie

        return max(zombies, key=lambda z: z.lock_priority)

    def _select_horde_target(self, zombies: List[ZombieTarget]) -> Optional[ZombieTarget]:
        """Select target for horde mode"""
        # Prioritize special infected and close threats
        special_zombies = [z for z in zombies if z.is_special]
        if special_zombies:
            return max(special_zombies, key=lambda z: z.lock_priority)

        # Otherwise closest threat
        return min(zombies, key=lambda z: z.distance)

    def _select_boss_target(self, zombies: List[ZombieTarget]) -> Optional[ZombieTarget]:
        """Select target for boss mode"""
        boss_zombies = [z for z in zombies if z.is_boss]
        if boss_zombies:
            return max(boss_zombies, key=lambda z: z.lock_priority)

        return max(zombies, key=lambda z: z.lock_priority)

    def _calculate_aim_adjustment(self, target: ZombieTarget) -> Optional[Dict[str, Any]]:
        """Calculate aim adjustment for target"""
        player_data = self.game_reader.local_player_data
        player_pos = player_data.get('position', (0, 0, 0))
        player_angles = player_data.get('view_angles', (0, 0, 0))

        # Get optimal target position
        target_pos = self._get_optimal_target_position(target)

        # Apply prediction if enabled
        if self.prediction_enabled and target.is_moving:
            target_pos = self._apply_prediction(target, target_pos)

        # Calculate required angles
        required_angles = self._calculate_angles_to_target(player_pos, target_pos)

        # Calculate deltas
        delta_pitch = required_angles[0] - player_angles[0]
        delta_yaw = required_angles[1] - player_angles[1]

        # Normalize angles
        delta_yaw = self._normalize_angle(delta_yaw)
        delta_pitch = self._normalize_angle(delta_pitch)

        # Apply smoothing
        smoothing = self._get_dynamic_smoothing(target)
        delta_pitch /= smoothing
        delta_yaw /= smoothing

        return {
            'delta_pitch': delta_pitch,
            'delta_yaw': delta_yaw,
            'target_pos': target_pos,
            'confidence': target.confidence,
            'target_type': target.zombie_type.value,
            'distance': target.distance
        }

    def _get_optimal_target_position(self, target: ZombieTarget) -> Tuple[float, float, float]:
        """Get optimal targeting position"""
        if self.weak_point_targeting:
            # Zombie-specific weak points
            if target.zombie_type == ZombieType.TALLBOY and target.weak_spot_pos:
                return target.weak_spot_pos  # Arm weak spot
            elif target.zombie_type == ZombieType.STINGER and target.head_pos:
                return target.head_pos
            elif target.zombie_type == ZombieType.REEKER and target.chest_pos:
                return target.chest_pos
            elif target.is_boss and target.weak_spot_pos:
                return target.weak_spot_pos
            elif target.head_pos and self.target_bone == "head":
                return target.head_pos
            elif target.chest_pos and self.target_bone == "chest":
                return target.chest_pos

        # Default to center mass
        return target.center_mass or (target.x, target.y, target.z + 25)

    def _apply_prediction(self, target: ZombieTarget, current_pos: Tuple[float, float, float]) -> Tuple[float, float, float]:
        """Apply movement prediction"""
        if not target.is_moving:
            return current_pos

        # Predict future position based on velocity
        prediction_time = 0.2  # 200ms ahead

        predicted_x = current_pos[0] + (target.velocity_x * prediction_time)
        predicted_y = current_pos[1] + (target.velocity_y * prediction_time)
        predicted_z = current_pos[2] + (target.velocity_z * prediction_time)

        return (predicted_x, predicted_y, predicted_z)

    def _get_dynamic_smoothing(self, target: ZombieTarget) -> float:
        """Get dynamic smoothing based on target and situation"""
        base_smoothing = self.smoothing

        # Adjust for target type
        if target.is_boss:
            base_smoothing *= 0.8  # Faster for bosses
        elif target.zombie_type == ZombieType.SNITCH:
            base_smoothing *= 0.6  # Very fast for snitches
        elif target.is_special:
            base_smoothing *= 0.9

        # Adjust for distance
        if target.distance < 50:
            base_smoothing *= 0.7  # Faster for close targets
        elif target.distance > 200:
            base_smoothing *= 1.2  # Slower for distant targets

        # Adjust for horde mode
        if self.game_reader.game_state.get('horde_active', False):
            base_smoothing *= 0.8  # Faster during hordes

        return max(1.0, base_smoothing)

    def _calculate_angles_to_target(self, from_pos: Tuple[float, float, float],
                                  to_pos: Tuple[float, float, float]) -> Tuple[float, float]:
        """Calculate pitch and yaw angles to target"""
        dx = to_pos[0] - from_pos[0]
        dy = to_pos[1] - from_pos[1]
        dz = to_pos[2] - from_pos[2]

        distance = math.sqrt(dx*dx + dy*dy + dz*dz)

        if distance == 0:
            return (0.0, 0.0)

        yaw = math.degrees(math.atan2(dy, dx))
        pitch = math.degrees(-math.asin(dz / distance))

        return (pitch, yaw)

    def _normalize_angle(self, angle: float) -> float:
        """Normalize angle to [-180, 180] range"""
        while angle > 180:
            angle -= 360
        while angle < -180:
            angle += 360
        return angle

    def _apply_advanced_modifications(self, adjustment: Dict[str, Any],
                                    target: ZombieTarget) -> Dict[str, Any]:
        """Apply advanced modifications to aim adjustment"""
        # Humanization
        if self.humanization_enabled:
            adjustment = self._apply_humanization(adjustment)

        # Anti-detection
        if self.anti_detection_enabled:
            adjustment = self._apply_anti_detection(adjustment, target)

        return adjustment

    def _apply_humanization(self, adjustment: Dict[str, Any]) -> Dict[str, Any]:
        """Apply humanization effects"""
        # Add micro-movements
        adjustment['delta_pitch'] += random.uniform(-0.02, 0.02)
        adjustment['delta_yaw'] += random.uniform(-0.02, 0.02)

        # Add slight delay
        time.sleep(random.uniform(0.005, 0.015))

        return adjustment

    def _apply_anti_detection(self, adjustment: Dict[str, Any],
                            target: ZombieTarget) -> Dict[str, Any]:
        """Apply anti-detection measures"""
        # Limit maximum adjustment speed
        max_delta = 5.0 if target.is_boss else 3.0

        adjustment['delta_pitch'] = max(-max_delta, min(max_delta, adjustment['delta_pitch']))
        adjustment['delta_yaw'] = max(-max_delta, min(max_delta, adjustment['delta_yaw']))

        return adjustment

    def _inject_aim_adjustment(self, adjustment: Dict[str, Any]) -> bool:
        """Inject aim adjustment into game"""
        try:
            player_data = self.game_reader.local_player_data
            current_angles = player_data.get('view_angles', (0, 0, 0))

            # Calculate new angles
            new_pitch = current_angles[0] + adjustment['delta_pitch']
            new_yaw = current_angles[1] + adjustment['delta_yaw']
            new_roll = current_angles[2]  # Keep roll unchanged

            # Clamp pitch
            new_pitch = max(-89, min(89, new_pitch))
            new_yaw = self._normalize_angle(new_yaw)

            # Write to memory
            player_base = self.process.base_address + self.process.offsets['local_player_base']
            angles_addr = player_base + self.process.offsets['player_view_angles']

            success = self.process.write_vector3(angles_addr, new_pitch, new_yaw, new_roll)

            if success:
                print(f"🎯 Aim adjusted: P={new_pitch:.2f}°, Y={new_yaw:.2f}° -> {adjustment.get('target_type', 'unknown')}")

            return success

        except Exception as e:
            print(f"❌ Aim injection error: {e}")
            return False

    def _release_target(self):
        """Release current target"""
        if self.current_target:
            print(f"🎯 Released target: {self.current_target.id}")
        self.current_target = None

    def get_statistics(self) -> Dict[str, Any]:
        """Get aim engine statistics"""
        return {
            'aim_mode': self.aim_mode.value,
            'target_bone': self.target_bone,
            'zombies_killed': self.zombies_killed,
            'shots_fired': self.shots_fired,
            'hits_landed': self.hits_landed,
            'headshots': self.headshots,
            'special_kills': self.special_kills,
            'boss_kills': self.boss_kills,
            'aim_adjustments': self.aim_adjustments,
            'successful_locks': self.successful_locks,
            'accuracy': (self.hits_landed / max(self.shots_fired, 1)) * 100,
            'headshot_rate': (self.headshots / max(self.hits_landed, 1)) * 100,
            'current_target': self.current_target.id if self.current_target else None
        }

class Back4BloodAimbotGUI:
    """Advanced GUI for Back 4 Blood Steam Aimbot"""

    def __init__(self):
        """Initialize GUI"""
        self.root = tk.Tk()
        self.root.title("🎯 Back 4 Blood Steam Aimbot - Complete System")
        self.root.geometry("1400x900")
        self.root.configure(bg='#0a0a0a')

        # Components
        self.process_manager = SteamProcessManager()
        self.game_reader = None
        self.aim_engine = None

        # State
        self.running = False
        self.connected = False

        # GUI variables
        self.setup_variables()

        # Create GUI
        self.create_widgets()

        # Show educational warning
        self.show_educational_warning()

        # Start update loop
        self.update_display()

    def show_educational_warning(self):
        """Show educational warning"""
        result = messagebox.askokcancel("Educational Notice", EDUCATIONAL_WARNING)
        if not result:
            self.root.destroy()
            return

    def setup_variables(self):
        """Setup GUI variables"""
        # Aim settings
        self.aim_mode = tk.StringVar(value="smooth")
        self.target_bone = tk.StringVar(value="head")
        self.aim_fov = tk.DoubleVar(value=90.0)
        self.aim_smoothing = tk.DoubleVar(value=2.5)
        self.aim_sensitivity = tk.DoubleVar(value=1.5)

        # Features
        self.weak_point_targeting = tk.BooleanVar(value=True)
        self.horde_optimization = tk.BooleanVar(value=True)
        self.boss_priority = tk.BooleanVar(value=True)
        self.prediction_enabled = tk.BooleanVar(value=True)
        self.humanization_enabled = tk.BooleanVar(value=True)
        self.anti_detection = tk.BooleanVar(value=True)

    def create_widgets(self):
        """Create GUI widgets"""
        # Title
        title_label = tk.Label(
            self.root,
            text="🎯 BACK 4 BLOOD STEAM AIMBOT",
            font=('Arial', 28, 'bold'),
            bg='#0a0a0a',
            fg='#ff4444'
        )
        title_label.pack(pady=20)

        # Subtitle
        subtitle_label = tk.Label(
            self.root,
            text="Advanced Memory-Based Aimbot System for Steam Version",
            font=('Arial', 14),
            bg='#0a0a0a',
            fg='#888888'
        )
        subtitle_label.pack(pady=(0, 20))

        # Main container
        main_frame = tk.Frame(self.root, bg='#0a0a0a')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Left panel (controls)
        left_panel = tk.Frame(main_frame, bg='#1a1a1a', relief='raised', bd=2)
        left_panel.pack(side='left', fill='y', padx=(0, 10))

        # Right panel (monitoring)
        right_panel = tk.Frame(main_frame, bg='#1a1a1a', relief='raised', bd=2)
        right_panel.pack(side='right', fill='both', expand=True)

        # Create panels
        self.create_control_panel(left_panel)
        self.create_monitoring_panel(right_panel)

        # Status bar
        self.create_status_bar()

    def create_control_panel(self, parent):
        """Create control panel"""
        # Title
        tk.Label(parent, text="🎮 CONTROL PANEL",
                font=('Arial', 16, 'bold'), bg='#1a1a1a', fg='white').pack(pady=10)

        # Connection section
        conn_frame = tk.LabelFrame(parent, text="🔗 Steam Connection",
                                  font=('Arial', 12, 'bold'), bg='#1a1a1a', fg='white')
        conn_frame.pack(fill='x', padx=10, pady=5)

        self.connect_button = tk.Button(conn_frame, text="🔍 Connect to Back 4 Blood",
                                       font=('Arial', 12, 'bold'), bg='#4CAF50', fg='white',
                                       command=self.connect_to_game)
        self.connect_button.pack(fill='x', padx=10, pady=10)

        # Main controls
        control_frame = tk.LabelFrame(parent, text="🎯 Main Controls",
                                     font=('Arial', 12, 'bold'), bg='#1a1a1a', fg='white')
        control_frame.pack(fill='x', padx=10, pady=5)

        # Control buttons
        button_frame = tk.Frame(control_frame, bg='#1a1a1a')
        button_frame.pack(fill='x', padx=10, pady=10)

        self.start_button = tk.Button(button_frame, text="🚀 START",
                                     font=('Arial', 12, 'bold'), bg='#4CAF50', fg='white',
                                     width=8, command=self.start_aimbot, state='disabled')
        self.start_button.pack(side='left', padx=5)

        self.stop_button = tk.Button(button_frame, text="🛑 STOP",
                                    font=('Arial', 12, 'bold'), bg='#f44336', fg='white',
                                    width=8, command=self.stop_aimbot, state='disabled')
        self.stop_button.pack(side='left', padx=5)

        # Aim settings
        aim_frame = tk.LabelFrame(parent, text="🎯 Aim Settings",
                                 font=('Arial', 12, 'bold'), bg='#1a1a1a', fg='white')
        aim_frame.pack(fill='x', padx=10, pady=5)

        # Aim mode
        tk.Label(aim_frame, text="Mode:", bg='#1a1a1a', fg='white').pack(anchor='w', padx=10, pady=2)
        aim_mode_combo = ttk.Combobox(aim_frame, textvariable=self.aim_mode,
                                     values=['sticky', 'snap', 'smooth', 'rage', 'legit', 'horde', 'boss'],
                                     state='readonly')
        aim_mode_combo.pack(fill='x', padx=10, pady=2)

        # Target bone
        tk.Label(aim_frame, text="Target:", bg='#1a1a1a', fg='white').pack(anchor='w', padx=10, pady=2)
        bone_combo = ttk.Combobox(aim_frame, textvariable=self.target_bone,
                                 values=['head', 'chest', 'center', 'weak_spot'], state='readonly')
        bone_combo.pack(fill='x', padx=10, pady=2)

        # FOV
        tk.Label(aim_frame, text="FOV:", bg='#1a1a1a', fg='white').pack(anchor='w', padx=10, pady=2)
        fov_scale = tk.Scale(aim_frame, from_=30, to=180, resolution=5, orient='horizontal',
                            variable=self.aim_fov, bg='#1a1a1a', fg='white')
        fov_scale.pack(fill='x', padx=10, pady=2)

        # Smoothing
        tk.Label(aim_frame, text="Smoothing:", bg='#1a1a1a', fg='white').pack(anchor='w', padx=10, pady=2)
        smooth_scale = tk.Scale(aim_frame, from_=0.5, to=10.0, resolution=0.1, orient='horizontal',
                               variable=self.aim_smoothing, bg='#1a1a1a', fg='white')
        smooth_scale.pack(fill='x', padx=10, pady=2)

        # Features
        features_frame = tk.LabelFrame(parent, text="✨ Advanced Features",
                                      font=('Arial', 12, 'bold'), bg='#1a1a1a', fg='white')
        features_frame.pack(fill='x', padx=10, pady=5)

        tk.Checkbutton(features_frame, text="🎯 Weak Point Targeting", variable=self.weak_point_targeting,
                      bg='#1a1a1a', fg='white', selectcolor='#333333').pack(anchor='w', padx=10, pady=1)
        tk.Checkbutton(features_frame, text="🧟 Horde Optimization", variable=self.horde_optimization,
                      bg='#1a1a1a', fg='white', selectcolor='#333333').pack(anchor='w', padx=10, pady=1)
        tk.Checkbutton(features_frame, text="💀 Boss Priority", variable=self.boss_priority,
                      bg='#1a1a1a', fg='white', selectcolor='#333333').pack(anchor='w', padx=10, pady=1)
        tk.Checkbutton(features_frame, text="🔮 Movement Prediction", variable=self.prediction_enabled,
                      bg='#1a1a1a', fg='white', selectcolor='#333333').pack(anchor='w', padx=10, pady=1)
        tk.Checkbutton(features_frame, text="🤖 Humanization", variable=self.humanization_enabled,
                      bg='#1a1a1a', fg='white', selectcolor='#333333').pack(anchor='w', padx=10, pady=1)
        tk.Checkbutton(features_frame, text="🛡️ Anti-Detection", variable=self.anti_detection,
                      bg='#1a1a1a', fg='white', selectcolor='#333333').pack(anchor='w', padx=10, pady=1)

    def create_monitoring_panel(self, parent):
        """Create monitoring panel"""
        # Title
        tk.Label(parent, text="📊 REAL-TIME MONITORING",
                font=('Arial', 16, 'bold'), bg='#1a1a1a', fg='white').pack(pady=10)

        # Statistics display
        self.stats_text = tk.Text(parent, height=40, bg='#0a0a0a', fg='#00ff00',
                                 font=('Courier', 10), wrap=tk.WORD)
        self.stats_text.pack(fill='both', expand=True, padx=10, pady=10)

        # Scrollbar
        scrollbar = tk.Scrollbar(self.stats_text)
        scrollbar.pack(side='right', fill='y')
        self.stats_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.stats_text.yview)

    def create_status_bar(self):
        """Create status bar"""
        status_frame = tk.Frame(self.root, bg='#333333', relief='sunken', bd=1)
        status_frame.pack(side='bottom', fill='x')

        self.status_label = tk.Label(status_frame, text="Status: Ready",
                                   bg='#333333', fg='white', font=('Arial', 10))
        self.status_label.pack(side='left', padx=10, pady=5)

        self.connection_label = tk.Label(status_frame, text="Steam: Disconnected",
                                       bg='#333333', fg='red', font=('Arial', 10))
        self.connection_label.pack(side='right', padx=10, pady=5)

    def connect_to_game(self):
        """Connect to Back 4 Blood Steam process"""
        try:
            self.status_label.config(text="Status: Connecting...", fg='yellow')
            self.root.update()

            if self.process_manager.find_back4blood_steam_process():
                self.game_reader = Back4BloodGameReader(self.process_manager)
                self.aim_engine = Back4BloodAimEngine(self.process_manager, self.game_reader)

                self.connected = True
                self.connection_label.config(text="Steam: Connected", fg='green')
                self.status_label.config(text="Status: Connected", fg='green')
                self.start_button.config(state='normal')
                self.connect_button.config(text="✅ Connected", state='disabled')

                messagebox.showinfo("Success", "✅ Successfully connected to Back 4 Blood Steam!")
            else:
                self.connection_label.config(text="Steam: Failed", fg='red')
                self.status_label.config(text="Status: Connection Failed", fg='red')
                messagebox.showerror("Error", "❌ Failed to connect to Back 4 Blood.\n\nMake sure:\n• Back 4 Blood is running\n• Game is launched through Steam\n• Run as Administrator")

        except Exception as e:
            self.connection_label.config(text="Steam: Error", fg='red')
            self.status_label.config(text="Status: Error", fg='red')
            messagebox.showerror("Error", f"Connection error:\n{str(e)}")

    def start_aimbot(self):
        """Start aimbot"""
        try:
            if not self.connected:
                messagebox.showerror("Error", "Please connect to Back 4 Blood first!")
                return

            # Apply settings
            self.apply_settings()

            # Start aimbot thread
            self.running = True
            self.aimbot_thread = threading.Thread(target=self.aimbot_loop, daemon=True)
            self.aimbot_thread.start()

            self.status_label.config(text="Status: RUNNING", fg='lime')
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')

            messagebox.showinfo("Success", "🎯 Back 4 Blood Aimbot started!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start aimbot:\n{str(e)}")

    def stop_aimbot(self):
        """Stop aimbot"""
        try:
            self.running = False
            self.status_label.config(text="Status: STOPPED", fg='red')
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

            messagebox.showinfo("Success", "🛑 Aimbot stopped!")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop aimbot:\n{str(e)}")

    def apply_settings(self):
        """Apply GUI settings to aimbot"""
        if not self.aim_engine:
            return

        # Apply aim settings
        aim_mode_map = {
            'sticky': AimMode.STICKY,
            'snap': AimMode.SNAP,
            'smooth': AimMode.SMOOTH,
            'rage': AimMode.RAGE,
            'legit': AimMode.LEGIT,
            'horde': AimMode.HORDE,
            'boss': AimMode.BOSS
        }

        self.aim_engine.aim_mode = aim_mode_map.get(self.aim_mode.get(), AimMode.SMOOTH)
        self.aim_engine.target_bone = self.target_bone.get()
        self.aim_engine.fov = self.aim_fov.get()
        self.aim_engine.smoothing = self.aim_smoothing.get()
        self.aim_engine.sensitivity = self.aim_sensitivity.get()

        # Apply feature settings
        self.aim_engine.weak_point_targeting = self.weak_point_targeting.get()
        self.aim_engine.horde_optimization = self.horde_optimization.get()
        self.aim_engine.boss_priority = self.boss_priority.get()
        self.aim_engine.prediction_enabled = self.prediction_enabled.get()
        self.aim_engine.humanization_enabled = self.humanization_enabled.get()
        self.aim_engine.anti_detection_enabled = self.anti_detection.get()

    def aimbot_loop(self):
        """Main aimbot loop"""
        while self.running:
            try:
                if self.aim_engine:
                    result = self.aim_engine.update()
                    if result:
                        # Aimbot is working
                        pass

                # Target 120 FPS
                time.sleep(1.0 / 120.0)

            except Exception as e:
                print(f"❌ Aimbot loop error: {e}")
                time.sleep(0.1)

    def update_display(self):
        """Update display with statistics"""
        try:
            stats_text = "🎯 BACK 4 BLOOD STEAM AIMBOT - REAL-TIME STATISTICS\n"
            stats_text += "=" * 80 + "\n\n"

            # Connection Status
            stats_text += "🔗 CONNECTION STATUS:\n"
            stats_text += f"   Steam Process: {'Connected' if self.connected else 'Disconnected'}\n"
            stats_text += f"   Aimbot Status: {'RUNNING' if self.running else 'STOPPED'}\n"
            if self.process_manager.process_id:
                stats_text += f"   Process ID: {self.process_manager.process_id}\n"
                stats_text += f"   Base Address: 0x{self.process_manager.base_address:X}\n"
            if self.process_manager.steam_id:
                stats_text += f"   Steam ID: {self.process_manager.steam_id}\n"
            stats_text += "\n"

            # Game State
            if self.game_reader and self.game_reader.game_state:
                game_state = self.game_reader.game_state
                stats_text += "🎮 GAME STATE:\n"
                stats_text += f"   Game Mode: {game_state.get('game_mode', 'Unknown')}\n"
                stats_text += f"   Round State: {game_state.get('round_state', 'Unknown')}\n"
                stats_text += f"   Horde Active: {'YES' if game_state.get('horde_active', False) else 'NO'}\n"
                stats_text += f"   Difficulty: {game_state.get('difficulty', 'Unknown')}\n"
                stats_text += f"   Total Zombies: {game_state.get('zombie_count', 0)}\n"
                stats_text += f"   Special Infected: {game_state.get('special_count', 0)}\n"
                stats_text += f"   Boss Enemies: {game_state.get('boss_count', 0)}\n"
                stats_text += "\n"

            # Aim Settings
            stats_text += "🎯 AIM CONFIGURATION:\n"
            stats_text += f"   Aim Mode: {self.aim_mode.get().upper()}\n"
            stats_text += f"   Target Bone: {self.target_bone.get().upper()}\n"
            stats_text += f"   FOV: {self.aim_fov.get():.1f}°\n"
            stats_text += f"   Smoothing: {self.aim_smoothing.get():.1f}\n"
            stats_text += f"   Sensitivity: {self.aim_sensitivity.get():.1f}\n"
            stats_text += "\n"

            # Features Status
            stats_text += "✨ ACTIVE FEATURES:\n"
            stats_text += f"   🎯 Weak Point Targeting: {'ON' if self.weak_point_targeting.get() else 'OFF'}\n"
            stats_text += f"   🧟 Horde Optimization: {'ON' if self.horde_optimization.get() else 'OFF'}\n"
            stats_text += f"   💀 Boss Priority: {'ON' if self.boss_priority.get() else 'OFF'}\n"
            stats_text += f"   🔮 Movement Prediction: {'ON' if self.prediction_enabled.get() else 'OFF'}\n"
            stats_text += f"   🤖 Humanization: {'ON' if self.humanization_enabled.get() else 'OFF'}\n"
            stats_text += f"   🛡️ Anti-Detection: {'ON' if self.anti_detection.get() else 'OFF'}\n"
            stats_text += "\n"

            # Statistics
            if self.aim_engine:
                stats = self.aim_engine.get_statistics()
                stats_text += "📊 PERFORMANCE STATISTICS:\n"
                stats_text += f"   Zombies Killed: {stats.get('zombies_killed', 0)}\n"
                stats_text += f"   Shots Fired: {stats.get('shots_fired', 0)}\n"
                stats_text += f"   Hits Landed: {stats.get('hits_landed', 0)}\n"
                stats_text += f"   Headshots: {stats.get('headshots', 0)}\n"
                stats_text += f"   Special Kills: {stats.get('special_kills', 0)}\n"
                stats_text += f"   Boss Kills: {stats.get('boss_kills', 0)}\n"
                stats_text += f"   Accuracy: {stats.get('accuracy', 0):.1f}%\n"
                stats_text += f"   Headshot Rate: {stats.get('headshot_rate', 0):.1f}%\n"
                stats_text += f"   Aim Adjustments: {stats.get('aim_adjustments', 0)}\n"
                stats_text += f"   Current Target: {stats.get('current_target', 'None')}\n"
                stats_text += "\n"

            # System Information
            stats_text += "💻 SYSTEM INFORMATION:\n"
            stats_text += f"   Computer Vision: {'Available' if CV_AVAILABLE else 'Not Available'}\n"
            stats_text += f"   AI Detection: {'Available' if AI_AVAILABLE else 'Not Available'}\n"
            stats_text += f"   Memory Access: {'Available' if WIN32_AVAILABLE else 'Not Available'}\n"
            stats_text += f"   System Monitoring: {'Available' if PSUTIL_AVAILABLE else 'Not Available'}\n"
            stats_text += "\n"

            # Capabilities
            stats_text += "🚀 BACK 4 BLOOD CAPABILITIES:\n"
            stats_text += "   ✅ Steam Process Detection & Memory Access\n"
            stats_text += "   ✅ Advanced Zombie Detection & Classification\n"
            stats_text += "   ✅ Multiple Aim Modes (Sticky, Snap, Smooth, Rage, Legit, Horde, Boss)\n"
            stats_text += "   ✅ Weak Point Targeting System\n"
            stats_text += "   ✅ Movement Prediction for Fast Zombies\n"
            stats_text += "   ✅ Horde Mode Optimization\n"
            stats_text += "   ✅ Boss Enemy Specialized Targeting\n"
            stats_text += "   ✅ Team Damage Prevention\n"
            stats_text += "   ✅ Humanization & Anti-Detection\n"
            stats_text += "   ✅ Real-Time Performance Monitoring\n"
            stats_text += "   ✅ Comprehensive Statistics Tracking\n"
            stats_text += "\n"

            stats_text += "⚠️  EDUCATIONAL USE ONLY - DO NOT USE IN ONLINE GAMES ⚠️\n"
            stats_text += "This software is for learning and research purposes only."

            # Update text widget
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)

            # Auto-scroll to bottom
            self.stats_text.see(tk.END)

        except Exception as e:
            print(f"Display update error: {e}")

        # Schedule next update
        self.root.after(1000, self.update_display)

    def run(self):
        """Run the GUI"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            print(f"GUI error: {e}")

    def on_closing(self):
        """Handle window closing"""
        self.running = False
        if self.process_manager:
            self.process_manager.cleanup()
        self.root.destroy()

def main():
    """Main entry point"""
    print(EDUCATIONAL_WARNING)

    # Wait for user acknowledgment
    response = input("\nDo you understand and agree to use this software for educational purposes only? (yes/no): ")
    if response.lower() != 'yes':
        print("❌ Educational agreement not accepted. Exiting.")
        return

    try:
        # Create and run GUI
        app = Back4BloodAimbotGUI()
        app.run()

    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Application error: {e}")
    finally:
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
