#!/usr/bin/env python3
"""
🎮 REMOTE GAMEPAD AIMBOT GUI
===========================
Control panel for the virtual gamepad aimbot
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
import threading
import time

class GamepadAimbotGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎮 Remote Gamepad Aimbot")
        self.root.geometry("550x650")
        self.root.configure(bg='#0d1117')
        
        # Process tracking
        self.aimbot_process = None
        self.aimbot_running = False
        
        # Settings
        self.fov_var = tk.IntVar(value=400)
        self.sens_var = tk.DoubleVar(value=0.8)
        self.conf_var = tk.DoubleVar(value=0.6)
        self.smooth_var = tk.DoubleVar(value=0.8)
        
        self.create_widgets()
        self.update_status()
        
    def create_widgets(self):
        """Create GUI widgets"""
        # Title
        title_label = tk.Label(self.root, text="🎮 REMOTE GAMEPAD AIMBOT", 
                              font=('Arial', 18, 'bold'), 
                              bg='#0d1117', fg='white')
        title_label.pack(pady=20)
        
        # Description
        desc_label = tk.Label(self.root, 
                            text="Virtual Xbox controller for console-style gaming\nPerfect for games with gamepad support!", 
                            font=('Arial', 10), 
                            bg='#0d1117', fg='lightgray')
        desc_label.pack(pady=10)
        
        # Status frame
        status_frame = tk.LabelFrame(self.root, text="📊 Status", 
                                   bg='#0d1117', fg='white', font=('Arial', 12, 'bold'))
        status_frame.pack(pady=10, padx=20, fill='x')
        
        self.status_label = tk.Label(status_frame, text="💤 GAMEPAD AIMBOT: INACTIVE", 
                                   font=('Arial', 14, 'bold'), 
                                   bg='#0d1117', fg='red')
        self.status_label.pack(pady=10)
        
        # Control buttons
        control_frame = tk.LabelFrame(self.root, text="🎮 Control", 
                                    bg='#0d1117', fg='white', font=('Arial', 12, 'bold'))
        control_frame.pack(pady=10, padx=20, fill='x')
        
        button_frame = tk.Frame(control_frame, bg='#0d1117')
        button_frame.pack(pady=15)
        
        # Start button
        self.start_btn = tk.Button(button_frame, text="🚀 START GAMEPAD AIMBOT", 
                                  font=('Arial', 12, 'bold'),
                                  bg='#238636', fg='white', width=20, height=2,
                                  command=self.start_aimbot)
        self.start_btn.grid(row=0, column=0, padx=5)
        
        # Stop button
        self.stop_btn = tk.Button(button_frame, text="⏹️ STOP AIMBOT", 
                                 font=('Arial', 12, 'bold'),
                                 bg='#da3633', fg='white', width=20, height=2,
                                 command=self.stop_aimbot)
        self.stop_btn.grid(row=0, column=1, padx=5)
        
        # Test button
        self.test_btn = tk.Button(button_frame, text="🧪 TEST GAMEPAD", 
                                 font=('Arial', 12, 'bold'),
                                 bg='#1f6feb', fg='white', width=20, height=2,
                                 command=self.test_gamepad)
        self.test_btn.grid(row=1, column=0, columnspan=2, pady=10)
        
        # Settings frame
        settings_frame = tk.LabelFrame(self.root, text="⚙️ Gamepad Settings", 
                                     bg='#0d1117', fg='white', font=('Arial', 12, 'bold'))
        settings_frame.pack(pady=10, padx=20, fill='x')
        
        # FOV Size
        fov_frame = tk.Frame(settings_frame, bg='#0d1117')
        fov_frame.pack(pady=5, fill='x')
        
        tk.Label(fov_frame, text="🔵 FOV Size:", bg='#0d1117', fg='white', 
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.fov_label = tk.Label(fov_frame, text="400", bg='#0d1117', fg='#58a6ff',
                                 font=('Arial', 10, 'bold'))
        self.fov_label.pack(side='right', padx=10)
        
        fov_scale = tk.Scale(fov_frame, from_=200, to=800, orient='horizontal',
                           variable=self.fov_var, bg='#0d1117', fg='white',
                           highlightbackground='#0d1117', command=self.update_fov_label)
        fov_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Stick Sensitivity
        sens_frame = tk.Frame(settings_frame, bg='#0d1117')
        sens_frame.pack(pady=5, fill='x')
        
        tk.Label(sens_frame, text="🕹️ Stick Sensitivity:", bg='#0d1117', fg='white',
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.sens_label = tk.Label(sens_frame, text="0.8", bg='#0d1117', fg='#58a6ff',
                                  font=('Arial', 10, 'bold'))
        self.sens_label.pack(side='right', padx=10)
        
        sens_scale = tk.Scale(sens_frame, from_=0.1, to=2.0, resolution=0.1,
                            orient='horizontal', variable=self.sens_var,
                            bg='#0d1117', fg='white', highlightbackground='#0d1117',
                            command=self.update_sens_label)
        sens_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Confidence
        conf_frame = tk.Frame(settings_frame, bg='#0d1117')
        conf_frame.pack(pady=5, fill='x')
        
        tk.Label(conf_frame, text="🎯 Confidence:", bg='#0d1117', fg='white',
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.conf_label = tk.Label(conf_frame, text="0.6", bg='#0d1117', fg='#58a6ff',
                                  font=('Arial', 10, 'bold'))
        self.conf_label.pack(side='right', padx=10)
        
        conf_scale = tk.Scale(conf_frame, from_=0.1, to=1.0, resolution=0.1,
                            orient='horizontal', variable=self.conf_var,
                            bg='#0d1117', fg='white', highlightbackground='#0d1117',
                            command=self.update_conf_label)
        conf_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Smoothing
        smooth_frame = tk.Frame(settings_frame, bg='#0d1117')
        smooth_frame.pack(pady=5, fill='x')
        
        tk.Label(smooth_frame, text="🌊 Stick Smoothing:", bg='#0d1117', fg='white',
                font=('Arial', 10)).pack(side='left', padx=10)
        
        self.smooth_label = tk.Label(smooth_frame, text="0.8", bg='#0d1117', fg='#58a6ff',
                                    font=('Arial', 10, 'bold'))
        self.smooth_label.pack(side='right', padx=10)
        
        smooth_scale = tk.Scale(smooth_frame, from_=0.1, to=1.0, resolution=0.1,
                              orient='horizontal', variable=self.smooth_var,
                              bg='#0d1117', fg='white', highlightbackground='#0d1117',
                              command=self.update_smooth_label)
        smooth_scale.pack(side='right', padx=10, fill='x', expand=True)
        
        # Instructions frame
        info_frame = tk.LabelFrame(self.root, text="ℹ️ Instructions", 
                                 bg='#0d1117', fg='white', font=('Arial', 12, 'bold'))
        info_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        info_text = """
🎮 Remote Gamepad Features:
• Creates virtual Xbox 360 controller
• Uses right analog stick for aiming
• Perfect for console-style games
• No mouse movement interference

🎮 Controls (when aimbot is running):
• F1: Toggle aimbot ON/OFF
• F2: Quit aimbot
• F3: Test right trigger

⚠️ Requirements:
• ViGEmBus driver must be installed
• Game must support Xbox controllers
• Aimbot starts INACTIVE for safety

🎯 How it works:
• Virtual controller appears in Windows
• Game detects it as real Xbox controller
• Right stick moves to aim at targets
• Triggers can be used for shooting
        """
        
        info_label = tk.Label(info_frame, text=info_text, bg='#0d1117', fg='lightgray',
                            font=('Arial', 9), justify='left')
        info_label.pack(pady=10, padx=10)
        
    def update_fov_label(self, value):
        self.fov_label.config(text=str(int(float(value))))
        
    def update_sens_label(self, value):
        self.sens_label.config(text=f"{float(value):.1f}")
        
    def update_conf_label(self, value):
        self.conf_label.config(text=f"{float(value):.1f}")
        
    def update_smooth_label(self, value):
        self.smooth_label.config(text=f"{float(value):.1f}")
    
    def start_aimbot(self):
        """Start the gamepad aimbot"""
        if self.aimbot_process and self.aimbot_process.poll() is None:
            messagebox.showinfo("Already Running", "Gamepad Aimbot is already running!")
            return
            
        try:
            # Check if gamepad aimbot file exists
            if not os.path.exists('gamepad_aimbot.py'):
                messagebox.showerror("File Not Found", 
                                   "gamepad_aimbot.py not found!\n\n" +
                                   "Make sure the gamepad aimbot file exists.")
                return
            
            # Start the gamepad aimbot process
            self.aimbot_process = subprocess.Popen([sys.executable, 'gamepad_aimbot.py'])
            self.aimbot_running = True
            
            messagebox.showinfo("Gamepad Aimbot Started", 
                              "🚀 Remote Gamepad Aimbot launched!\n\n" +
                              "🎮 Virtual Xbox controller created\n" +
                              "🎮 Your game should detect the controller\n\n" +
                              "🎮 Controls:\n" +
                              "• F1: Toggle aimbot ON/OFF\n" +
                              "• F2: Quit aimbot\n" +
                              "• F3: Test right trigger\n\n" +
                              "⚠️ Aimbot starts INACTIVE - Press F1 to activate!")
                              
        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to start gamepad aimbot:\n{str(e)}")
    
    def stop_aimbot(self):
        """Stop the gamepad aimbot"""
        if self.aimbot_process:
            try:
                self.aimbot_process.terminate()
                self.aimbot_process = None
                self.aimbot_running = False
                messagebox.showinfo("Aimbot Stopped", "⏹️ Gamepad Aimbot has been stopped\n🎮 Virtual controller removed")
            except Exception as e:
                messagebox.showerror("Stop Error", f"Error stopping aimbot:\n{str(e)}")
        else:
            messagebox.showwarning("Not Running", "No gamepad aimbot process to stop")
    
    def test_gamepad(self):
        """Test if vgamepad is working"""
        try:
            import vgamepad as vg
            
            # Create temporary gamepad
            test_gamepad = vg.VX360Gamepad()
            
            # Test button press
            test_gamepad.press_button(button=vg.XUSB_BUTTON.XUSB_GAMEPAD_A)
            test_gamepad.update()
            time.sleep(0.1)
            test_gamepad.release_button(button=vg.XUSB_BUTTON.XUSB_GAMEPAD_A)
            test_gamepad.update()
            
            # Test stick movement
            test_gamepad.right_joystick_float(x_value_float=0.5, y_value_float=0.5)
            test_gamepad.update()
            time.sleep(0.1)
            test_gamepad.right_joystick_float(x_value_float=0.0, y_value_float=0.0)
            test_gamepad.update()
            
            # Reset and cleanup
            test_gamepad.reset()
            test_gamepad.update()
            
            messagebox.showinfo("Test Successful", 
                              "✅ Virtual gamepad test successful!\n\n" +
                              "🎮 ViGEmBus driver is working\n" +
                              "🎮 Virtual controller can be created\n" +
                              "🎮 Ready to use gamepad aimbot!")
            
        except ImportError:
            messagebox.showerror("Missing Library", 
                               "❌ vgamepad library not installed!\n\n" +
                               "Install with: pip install vgamepad")
        except Exception as e:
            messagebox.showerror("Test Failed", 
                               f"❌ Virtual gamepad test failed!\n\n" +
                               f"Error: {str(e)}\n\n" +
                               "💡 Make sure ViGEmBus driver is installed")
    
    def update_status(self):
        """Update status display"""
        if self.aimbot_process and self.aimbot_process.poll() is None:
            self.status_label.config(text="🔥 GAMEPAD AIMBOT: RUNNING", fg='#238636')
            self.aimbot_running = True
        else:
            self.status_label.config(text="💤 GAMEPAD AIMBOT: INACTIVE", fg='#da3633')
            self.aimbot_running = False
            
        # Schedule next update
        self.root.after(1000, self.update_status)
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🎮 Launching Remote Gamepad Aimbot GUI...")
    app = GamepadAimbotGUI()
    app.run()
