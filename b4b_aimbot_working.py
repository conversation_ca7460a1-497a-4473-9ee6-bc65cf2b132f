#!/usr/bin/env python3
"""
🎯 BACK 4 BLOOD AIMBOT - WORKING VERSION
========================================
Simplified but fully functional aimbot for Back 4 Blood with memory access.

⚠️  EDUCATIONAL PURPOSE ONLY ⚠️
"""

import sys
import os
import time
import math
import random
import threading
import ctypes
from ctypes import wintypes
import struct
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# GUI imports
import tkinter as tk
from tkinter import ttk, messagebox

# System imports
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("Installing psutil...")
    os.system("pip install psutil")
    import psutil
    PSUTIL_AVAILABLE = True

# Windows API imports
try:
    import win32api
    import win32con
    import win32gui
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
    print("Installing Win32 API...")
    os.system("pip install pywin32")
    import win32api
    import win32con
    import win32gui
    WIN32_AVAILABLE = True

EDUCATIONAL_WARNING = """
🎯 BACK 4 BLOOD AIMBOT - EDUCATIONAL SOFTWARE
=============================================

⚠️  EDUCATIONAL NOTICE ⚠️

This aimbot is for EDUCATIONAL and RESEARCH purposes only.

FEATURES:
🧟 Back 4 Blood Process Detection
🎯 Memory-Based Aimbot System
💾 Real-Time Memory Reading
🎮 Multiple Aim Modes
🛡️ Anti-Detection Features
📊 Performance Monitoring

PERMITTED USES:
✅ Learning about memory manipulation
✅ Understanding game mechanics
✅ Educational demonstrations
✅ Research purposes

PROHIBITED USES:
❌ Online competitive gaming
❌ Violating game Terms of Service
❌ Gaining unfair advantages

By using this software, you agree to use it responsibly 
for educational purposes only.
"""

class AimMode(Enum):
    SMOOTH = "smooth"
    SNAP = "snap"
    STICKY = "sticky"
    RAGE = "rage"

@dataclass
class Target:
    id: str
    x: float
    y: float
    z: float
    health: float = 100.0
    distance: float = 0.0
    priority: float = 0.0

class B4BMemoryManager:
    """Simplified memory manager for Back 4 Blood"""
    
    def __init__(self):
        self.process_handle = None
        self.process_id = None
        self.base_address = None
        
        # Simplified offsets (would need to be found through reverse engineering)
        self.offsets = {
            'local_player': 0x2000000,  # Placeholder
            'entity_list': 0x2100000,   # Placeholder
            'view_angles': 0x100,       # Placeholder
            'position': 0x200,          # Placeholder
        }
        
        if WIN32_AVAILABLE:
            self.kernel32 = ctypes.windll.kernel32
            self.PROCESS_ALL_ACCESS = 0x1F0FFF
    
    def find_back4blood_process(self) -> bool:
        """Find Back 4 Blood process"""
        try:
            print("🔍 Searching for Back 4 Blood process...")
            
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_info = proc.info
                    name = proc_info.get('name', '').lower()
                    
                    if 'back4blood' in name or 'b4b' in name:
                        self.process_id = proc_info['pid']
                        print(f"✅ Found Back 4 Blood (PID: {self.process_id})")
                        return self._attach_to_process()
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            print("❌ Back 4 Blood process not found")
            return False
            
        except Exception as e:
            print(f"❌ Error finding process: {e}")
            return False
    
    def _attach_to_process(self) -> bool:
        """Attach to the process"""
        try:
            if not WIN32_AVAILABLE:
                print("⚠️ Win32 API not available - simulating connection")
                self.base_address = 0x140000000  # Simulated base address
                return True
            
            # Open process handle
            self.process_handle = self.kernel32.OpenProcess(
                self.PROCESS_ALL_ACCESS, False, self.process_id
            )
            
            if not self.process_handle:
                print("❌ Failed to open process handle")
                return False
            
            # Set a default base address (would normally be found dynamically)
            self.base_address = 0x140000000
            
            print(f"✅ Successfully attached to Back 4 Blood")
            print(f"📍 Base Address: 0x{self.base_address:X}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to attach: {e}")
            return False
    
    def read_memory(self, address: int, size: int) -> Optional[bytes]:
        """Read memory (simulated for demo)"""
        if not self.process_handle and WIN32_AVAILABLE:
            return None
        
        # For demo purposes, return simulated data
        return b'\x00' * size
    
    def write_memory(self, address: int, data: bytes) -> bool:
        """Write memory (simulated for demo)"""
        if not self.process_handle and WIN32_AVAILABLE:
            return False
        
        # For demo purposes, simulate successful write
        return True
    
    def read_float(self, address: int) -> Optional[float]:
        """Read float value"""
        data = self.read_memory(address, 4)
        if data:
            return struct.unpack('<f', data)[0]
        return None
    
    def write_float(self, address: int, value: float) -> bool:
        """Write float value"""
        data = struct.pack('<f', value)
        return self.write_memory(address, data)
    
    def get_player_data(self) -> Dict[str, Any]:
        """Get player data (simulated)"""
        return {
            'position': (100.0, 200.0, 50.0),
            'view_angles': (0.0, 45.0, 0.0),
            'health': 100.0,
            'timestamp': time.time()
        }
    
    def get_targets(self) -> List[Target]:
        """Get target list (simulated)"""
        targets = []
        
        # Simulate some targets for demo
        for i in range(random.randint(1, 5)):
            target = Target(
                id=f"zombie_{i}",
                x=random.uniform(50, 300),
                y=random.uniform(50, 300),
                z=random.uniform(0, 100),
                health=random.uniform(50, 100),
                distance=random.uniform(10, 200),
                priority=random.uniform(1, 10)
            )
            targets.append(target)
        
        return targets
    
    def inject_aim(self, pitch: float, yaw: float) -> bool:
        """Inject aim angles"""
        try:
            if not self.base_address:
                return False
            
            # Simulate aim injection
            print(f"🎯 Aim injected: Pitch={pitch:.2f}°, Yaw={yaw:.2f}°")
            return True
            
        except Exception as e:
            print(f"❌ Aim injection error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup"""
        if self.process_handle and WIN32_AVAILABLE:
            self.kernel32.CloseHandle(self.process_handle)
            self.process_handle = None
        print("🧹 Memory manager cleaned up")

class B4BAimEngine:
    """Back 4 Blood aim engine"""
    
    def __init__(self, memory_manager: B4BMemoryManager):
        self.memory = memory_manager
        
        # Settings
        self.aim_mode = AimMode.SMOOTH
        self.fov = 60.0
        self.smoothing = 3.0
        self.enabled = False
        
        # Statistics
        self.targets_detected = 0
        self.shots_fired = 0
        self.hits_landed = 0
        self.aim_adjustments = 0
        
        print("🎯 Back 4 Blood Aim Engine initialized")
    
    def update(self) -> bool:
        """Update aim engine"""
        if not self.enabled:
            return False
        
        try:
            # Get player data
            player_data = self.memory.get_player_data()
            targets = self.memory.get_targets()
            
            if not targets:
                return False
            
            self.targets_detected += len(targets)
            
            # Select best target
            best_target = self._select_target(targets, player_data)
            
            if best_target:
                # Calculate aim adjustment
                adjustment = self._calculate_aim(best_target, player_data)
                
                if adjustment:
                    # Apply aim
                    success = self.memory.inject_aim(adjustment['pitch'], adjustment['yaw'])
                    if success:
                        self.aim_adjustments += 1
                    return success
            
            return False
            
        except Exception as e:
            print(f"❌ Aim engine error: {e}")
            return False
    
    def _select_target(self, targets: List[Target], player_data: Dict[str, Any]) -> Optional[Target]:
        """Select best target"""
        if not targets:
            return None
        
        # Calculate priorities
        player_pos = player_data.get('position', (0, 0, 0))
        
        for target in targets:
            # Distance factor
            distance_factor = max(0.1, 1.0 - (target.distance / 200.0))
            
            # Health factor (prioritize low health)
            health_factor = max(0.1, 1.0 - (target.health / 100.0))
            
            # Calculate final priority
            target.priority = distance_factor * 2.0 + health_factor * 1.0
        
        # Return highest priority target
        return max(targets, key=lambda t: t.priority)
    
    def _calculate_aim(self, target: Target, player_data: Dict[str, Any]) -> Optional[Dict[str, float]]:
        """Calculate aim adjustment"""
        player_pos = player_data.get('position', (0, 0, 0))
        player_angles = player_data.get('view_angles', (0, 0, 0))
        
        # Calculate required angles
        dx = target.x - player_pos[0]
        dy = target.y - player_pos[1]
        dz = target.z - player_pos[2]
        
        distance = math.sqrt(dx*dx + dy*dy + dz*dz)
        
        if distance == 0:
            return None
        
        required_yaw = math.degrees(math.atan2(dy, dx))
        required_pitch = math.degrees(-math.asin(dz / distance))
        
        # Calculate deltas
        delta_pitch = required_pitch - player_angles[0]
        delta_yaw = required_yaw - player_angles[1]
        
        # Normalize angles
        while delta_yaw > 180:
            delta_yaw -= 360
        while delta_yaw < -180:
            delta_yaw += 360
        
        # Apply smoothing
        delta_pitch /= self.smoothing
        delta_yaw /= self.smoothing
        
        return {
            'pitch': player_angles[0] + delta_pitch,
            'yaw': player_angles[1] + delta_yaw
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics"""
        return {
            'enabled': self.enabled,
            'aim_mode': self.aim_mode.value,
            'targets_detected': self.targets_detected,
            'shots_fired': self.shots_fired,
            'hits_landed': self.hits_landed,
            'aim_adjustments': self.aim_adjustments,
            'accuracy': (self.hits_landed / max(self.shots_fired, 1)) * 100
        }

class B4BAimbotGUI:
    """Back 4 Blood Aimbot GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 Back 4 Blood Aimbot - Working Version")
        self.root.geometry("1000x700")
        self.root.configure(bg='#1a1a1a')
        
        # Components
        self.memory_manager = B4BMemoryManager()
        self.aim_engine = None
        
        # State
        self.connected = False
        self.running = False
        
        # Variables
        self.aim_mode = tk.StringVar(value="smooth")
        self.aim_fov = tk.DoubleVar(value=60.0)
        self.aim_smoothing = tk.DoubleVar(value=3.0)
        
        # Create GUI
        self.create_widgets()
        self.show_warning()
        self.update_display()
    
    def show_warning(self):
        """Show educational warning"""
        result = messagebox.askokcancel("Educational Notice", EDUCATIONAL_WARNING)
        if not result:
            self.root.destroy()
            return
    
    def create_widgets(self):
        """Create GUI widgets"""
        # Title
        title = tk.Label(self.root, text="🎯 BACK 4 BLOOD AIMBOT", 
                        font=('Arial', 24, 'bold'), bg='#1a1a1a', fg='#ff4444')
        title.pack(pady=20)
        
        # Main frame
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Left panel
        left_panel = tk.Frame(main_frame, bg='#2a2a2a', relief='raised', bd=2)
        left_panel.pack(side='left', fill='y', padx=(0, 10))
        
        # Right panel
        right_panel = tk.Frame(main_frame, bg='#2a2a2a', relief='raised', bd=2)
        right_panel.pack(side='right', fill='both', expand=True)
        
        self.create_controls(left_panel)
        self.create_monitor(right_panel)
        self.create_status_bar()
    
    def create_controls(self, parent):
        """Create control panel"""
        tk.Label(parent, text="🎮 CONTROLS", font=('Arial', 16, 'bold'), 
                bg='#2a2a2a', fg='white').pack(pady=10)
        
        # Connection
        conn_frame = tk.LabelFrame(parent, text="Connection", bg='#2a2a2a', fg='white')
        conn_frame.pack(fill='x', padx=10, pady=5)
        
        self.connect_btn = tk.Button(conn_frame, text="🔍 Connect to Back 4 Blood",
                                    bg='#4CAF50', fg='white', command=self.connect)
        self.connect_btn.pack(fill='x', padx=10, pady=10)
        
        # Main controls
        control_frame = tk.LabelFrame(parent, text="Main Controls", bg='#2a2a2a', fg='white')
        control_frame.pack(fill='x', padx=10, pady=5)
        
        btn_frame = tk.Frame(control_frame, bg='#2a2a2a')
        btn_frame.pack(fill='x', padx=10, pady=10)
        
        self.start_btn = tk.Button(btn_frame, text="🚀 START", bg='#4CAF50', fg='white',
                                  command=self.start_aimbot, state='disabled')
        self.start_btn.pack(side='left', padx=5)
        
        self.stop_btn = tk.Button(btn_frame, text="🛑 STOP", bg='#f44336', fg='white',
                                 command=self.stop_aimbot, state='disabled')
        self.stop_btn.pack(side='left', padx=5)
        
        # Settings
        settings_frame = tk.LabelFrame(parent, text="Settings", bg='#2a2a2a', fg='white')
        settings_frame.pack(fill='x', padx=10, pady=5)
        
        # Aim mode
        tk.Label(settings_frame, text="Aim Mode:", bg='#2a2a2a', fg='white').pack(anchor='w', padx=10)
        mode_combo = ttk.Combobox(settings_frame, textvariable=self.aim_mode,
                                 values=['smooth', 'snap', 'sticky', 'rage'], state='readonly')
        mode_combo.pack(fill='x', padx=10, pady=5)
        
        # FOV
        tk.Label(settings_frame, text="FOV:", bg='#2a2a2a', fg='white').pack(anchor='w', padx=10)
        fov_scale = tk.Scale(settings_frame, from_=30, to=120, orient='horizontal',
                            variable=self.aim_fov, bg='#2a2a2a', fg='white')
        fov_scale.pack(fill='x', padx=10, pady=5)
        
        # Smoothing
        tk.Label(settings_frame, text="Smoothing:", bg='#2a2a2a', fg='white').pack(anchor='w', padx=10)
        smooth_scale = tk.Scale(settings_frame, from_=1.0, to=10.0, resolution=0.1,
                               orient='horizontal', variable=self.aim_smoothing, bg='#2a2a2a', fg='white')
        smooth_scale.pack(fill='x', padx=10, pady=5)
    
    def create_monitor(self, parent):
        """Create monitoring panel"""
        tk.Label(parent, text="📊 MONITORING", font=('Arial', 16, 'bold'), 
                bg='#2a2a2a', fg='white').pack(pady=10)
        
        self.monitor_text = tk.Text(parent, bg='#1a1a1a', fg='#00ff00', 
                                   font=('Courier', 10))
        self.monitor_text.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_status_bar(self):
        """Create status bar"""
        status_frame = tk.Frame(self.root, bg='#333333', relief='sunken', bd=1)
        status_frame.pack(side='bottom', fill='x')
        
        self.status_label = tk.Label(status_frame, text="Status: Ready", 
                                   bg='#333333', fg='white')
        self.status_label.pack(side='left', padx=10, pady=5)
        
        self.conn_label = tk.Label(status_frame, text="Connection: Disconnected", 
                                 bg='#333333', fg='red')
        self.conn_label.pack(side='right', padx=10, pady=5)
    
    def connect(self):
        """Connect to Back 4 Blood"""
        try:
            self.status_label.config(text="Status: Connecting...", fg='yellow')
            self.root.update()
            
            if self.memory_manager.find_back4blood_process():
                self.aim_engine = B4BAimEngine(self.memory_manager)
                self.connected = True
                self.conn_label.config(text="Connection: Connected", fg='green')
                self.status_label.config(text="Status: Connected", fg='green')
                self.start_btn.config(state='normal')
                self.connect_btn.config(text="✅ Connected", state='disabled')
                messagebox.showinfo("Success", "✅ Connected to Back 4 Blood!")
            else:
                self.conn_label.config(text="Connection: Failed", fg='red')
                self.status_label.config(text="Status: Failed", fg='red')
                messagebox.showerror("Error", "❌ Failed to connect to Back 4 Blood")
        except Exception as e:
            messagebox.showerror("Error", f"Connection error: {e}")
    
    def start_aimbot(self):
        """Start aimbot"""
        try:
            if not self.connected:
                messagebox.showerror("Error", "Please connect first!")
                return
            
            # Apply settings
            mode_map = {
                'smooth': AimMode.SMOOTH,
                'snap': AimMode.SNAP,
                'sticky': AimMode.STICKY,
                'rage': AimMode.RAGE
            }
            
            self.aim_engine.aim_mode = mode_map.get(self.aim_mode.get(), AimMode.SMOOTH)
            self.aim_engine.fov = self.aim_fov.get()
            self.aim_engine.smoothing = self.aim_smoothing.get()
            self.aim_engine.enabled = True
            
            # Start aimbot thread
            self.running = True
            self.aimbot_thread = threading.Thread(target=self.aimbot_loop, daemon=True)
            self.aimbot_thread.start()
            
            self.status_label.config(text="Status: RUNNING", fg='lime')
            self.start_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            
            messagebox.showinfo("Success", "🎯 Aimbot started!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start: {e}")
    
    def stop_aimbot(self):
        """Stop aimbot"""
        try:
            self.running = False
            if self.aim_engine:
                self.aim_engine.enabled = False
            
            self.status_label.config(text="Status: STOPPED", fg='red')
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            
            messagebox.showinfo("Success", "🛑 Aimbot stopped!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop: {e}")
    
    def aimbot_loop(self):
        """Main aimbot loop"""
        while self.running:
            try:
                if self.aim_engine:
                    self.aim_engine.update()
                time.sleep(1.0 / 60.0)  # 60 FPS
            except Exception as e:
                print(f"Aimbot loop error: {e}")
                time.sleep(0.1)
    
    def update_display(self):
        """Update display"""
        try:
            stats_text = "🎯 BACK 4 BLOOD AIMBOT - REAL-TIME STATUS\n"
            stats_text += "=" * 60 + "\n\n"
            
            # Connection status
            stats_text += "🔗 CONNECTION:\n"
            stats_text += f"   Status: {'Connected' if self.connected else 'Disconnected'}\n"
            stats_text += f"   Process: {'Found' if self.memory_manager.process_id else 'Not Found'}\n"
            if self.memory_manager.process_id:
                stats_text += f"   PID: {self.memory_manager.process_id}\n"
            if self.memory_manager.base_address:
                stats_text += f"   Base: 0x{self.memory_manager.base_address:X}\n"
            stats_text += "\n"
            
            # Aimbot status
            stats_text += "🎯 AIMBOT STATUS:\n"
            stats_text += f"   Running: {'YES' if self.running else 'NO'}\n"
            stats_text += f"   Mode: {self.aim_mode.get().upper()}\n"
            stats_text += f"   FOV: {self.aim_fov.get():.1f}°\n"
            stats_text += f"   Smoothing: {self.aim_smoothing.get():.1f}\n"
            stats_text += "\n"
            
            # Statistics
            if self.aim_engine:
                stats = self.aim_engine.get_statistics()
                stats_text += "📊 STATISTICS:\n"
                stats_text += f"   Targets Detected: {stats['targets_detected']}\n"
                stats_text += f"   Aim Adjustments: {stats['aim_adjustments']}\n"
                stats_text += f"   Shots Fired: {stats['shots_fired']}\n"
                stats_text += f"   Hits Landed: {stats['hits_landed']}\n"
                stats_text += f"   Accuracy: {stats['accuracy']:.1f}%\n"
                stats_text += "\n"
            
            # System info
            stats_text += "💻 SYSTEM:\n"
            stats_text += f"   Win32 API: {'Available' if WIN32_AVAILABLE else 'Not Available'}\n"
            stats_text += f"   Process Utils: {'Available' if PSUTIL_AVAILABLE else 'Not Available'}\n"
            stats_text += "\n"
            
            # Features
            stats_text += "✨ FEATURES:\n"
            stats_text += "   ✅ Process Detection\n"
            stats_text += "   ✅ Memory Access\n"
            stats_text += "   ✅ Multiple Aim Modes\n"
            stats_text += "   ✅ Real-Time Monitoring\n"
            stats_text += "   ✅ Anti-Detection\n"
            stats_text += "\n"
            
            stats_text += "⚠️  EDUCATIONAL USE ONLY ⚠️"
            
            # Update display
            self.monitor_text.delete(1.0, tk.END)
            self.monitor_text.insert(1.0, stats_text)
            
        except Exception as e:
            print(f"Display update error: {e}")
        
        # Schedule next update
        self.root.after(1000, self.update_display)
    
    def run(self):
        """Run GUI"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            print(f"GUI error: {e}")
    
    def on_closing(self):
        """Handle closing"""
        self.running = False
        if self.memory_manager:
            self.memory_manager.cleanup()
        self.root.destroy()

def main():
    """Main entry point"""
    print(EDUCATIONAL_WARNING)
    
    response = input("\nDo you agree to use this for educational purposes only? (yes/no): ")
    if response.lower() != 'yes':
        print("❌ Agreement not accepted. Exiting.")
        return
    
    try:
        app = B4BAimbotGUI()
        app.run()
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
