#!/usr/bin/env python3
"""
🎯 STICKY TARGET TRACKER
========================
Advanced sticky aim implementation with target persistence, prediction, and humanization.
"""

import time
import math
import logging
from typing import Optional, List, Dict, Tuple, Any
from dataclasses import dataclass, field
from collections import deque
import numpy as np

from ..detection.base_detector import Target
from ..utils.math_utils import (
    calculate_distance, calculate_angle, normalize_angle,
    interpolate_smooth, predict_position
)

@dataclass
class TargetHistory:
    """Historical data for a target"""
    positions: deque = field(default_factory=lambda: deque(maxlen=10))
    timestamps: deque = field(default_factory=lambda: deque(maxlen=10))
    velocities: deque = field(default_factory=lambda: deque(maxlen=5))
    lock_duration: float = 0.0
    lock_start_time: float = 0.0
    prediction_accuracy: float = 1.0
    miss_count: int = 0

@dataclass
class AimAdjustment:
    """Aim adjustment data"""
    delta_x: float
    delta_y: float
    confidence: float
    prediction_used: bool
    smoothing_applied: bool
    timestamp: float = field(default_factory=time.time)

class StickyTargetTracker:
    """
    Advanced sticky aim tracker with persistence, prediction, and humanization.
    
    Features:
    - Target persistence (sticky behavior)
    - Movement prediction
    - Smooth interpolation
    - Priority-based target selection
    - Anti-detection measures
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the sticky target tracker"""
        self.logger = logging.getLogger(__name__)
        self.config = config or {}
        
        # Core tracking state
        self.current_target: Optional[Target] = None
        self.target_locked = False
        self.lock_start_time = 0.0
        self.last_target_switch = 0.0
        
        # Target management
        self.available_targets: List[Target] = []
        self.target_history: Dict[str, TargetHistory] = {}
        self.target_priorities: Dict[str, float] = {}
        
        # Configuration parameters
        self.max_lock_distance = self.config.get('max_lock_distance', 500.0)
        self.max_lock_fov = self.config.get('max_lock_fov', 45.0)
        self.target_switch_delay = self.config.get('target_switch_delay', 0.3)
        self.lock_strength = self.config.get('lock_strength', 0.8)
        self.smoothing_factor = self.config.get('smoothing_factor', 4.0)
        self.prediction_enabled = self.config.get('prediction_enabled', True)
        self.prediction_time = self.config.get('prediction_time', 0.15)
        self.target_lost_timeout = self.config.get('target_lost_timeout', 1.0)
        self.reacquisition_bonus = self.config.get('reacquisition_bonus', 1.5)
        
        # Smoothing and interpolation
        self.aim_accumulator = np.array([0.0, 0.0])
        self.last_aim_time = 0.0
        self.velocity_smoothing = 0.7
        
        # Performance tracking
        self.total_locks = 0
        self.successful_predictions = 0
        self.total_predictions = 0
        self.average_lock_time = 0.0
        
        # Anti-detection features
        self.micro_adjustment_timer = 0.0
        self.natural_drift_phase = 0.0
        self.fatigue_factor = 1.0
        
        self.logger.info("🎯 Sticky Target Tracker initialized")
    
    def update_targets(self, targets: List[Target]):
        """Update available targets and their tracking data"""
        current_time = time.time()
        self.available_targets = targets
        
        # Update target histories
        for target in targets:
            target_id = target.id
            
            if target_id not in self.target_history:
                self.target_history[target_id] = TargetHistory()
            
            history = self.target_history[target_id]
            
            # Update position history
            history.positions.append((target.x, target.y, target.z))
            history.timestamps.append(current_time)
            
            # Calculate velocity if we have enough history
            if len(history.positions) >= 2 and len(history.timestamps) >= 2:
                dt = history.timestamps[-1] - history.timestamps[-2]
                if dt > 0:
                    pos_curr = np.array(history.positions[-1])
                    pos_prev = np.array(history.positions[-2])
                    velocity = (pos_curr - pos_prev) / dt
                    
                    # Smooth velocity
                    if history.velocities:
                        prev_velocity = history.velocities[-1]
                        velocity = (self.velocity_smoothing * prev_velocity + 
                                  (1 - self.velocity_smoothing) * velocity)
                    
                    history.velocities.append(velocity)
        
        # Calculate target priorities
        self._calculate_target_priorities()
        
        # Clean up old target histories
        self._cleanup_old_histories(current_time)
    
    def _calculate_target_priorities(self):
        """Calculate priority scores for all targets"""
        if not self.available_targets:
            return
        
        current_time = time.time()
        
        for target in self.available_targets:
            priority = 0.0
            
            # Distance factor (closer = higher priority)
            distance = math.sqrt(target.x**2 + target.y**2 + target.z**2)
            if distance > 0:
                distance_factor = max(0, 1.0 - (distance / self.max_lock_distance))
                priority += distance_factor * 0.3
            
            # FOV factor (closer to center = higher priority)
            angle_to_target = math.atan2(target.y, target.x) * 180 / math.pi
            fov_factor = max(0, 1.0 - (abs(angle_to_target) / self.max_lock_fov))
            priority += fov_factor * 0.4
            
            # Health factor (lower health = higher priority for finishing)
            if hasattr(target, 'health') and target.health > 0:
                health_factor = max(0.1, 1.0 - (target.health / 100.0))
                priority += health_factor * 0.1
            
            # Velocity factor (slower targets easier to track)
            history = self.target_history.get(target.id)
            if history and history.velocities:
                velocity_mag = np.linalg.norm(history.velocities[-1])
                velocity_factor = max(0.1, 1.0 - min(velocity_mag / 500.0, 1.0))
                priority += velocity_factor * 0.1
            
            # Current target bonus (sticky behavior)
            if (self.current_target and target.id == self.current_target.id and
                current_time - self.last_target_switch > self.target_switch_delay):
                priority *= self.reacquisition_bonus
            
            # Prediction accuracy bonus
            if history and history.prediction_accuracy > 0.8:
                priority *= 1.2
            
            self.target_priorities[target.id] = priority
    
    def get_current_target(self) -> Optional[Target]:
        """Get the current target using sticky aim logic"""
        current_time = time.time()
        
        # Check if current target is still valid
        if self.current_target:
            current_target_valid = any(
                t.id == self.current_target.id for t in self.available_targets
            )
            
            # Check timeout
            if (not current_target_valid or 
                current_time - self.lock_start_time > self.target_lost_timeout):
                self._release_target()
        
        # Select new target if needed
        if not self.current_target and self.available_targets:
            self._select_best_target()
        
        return self.current_target
    
    def _select_best_target(self):
        """Select the best target based on priorities"""
        if not self.available_targets:
            return
        
        current_time = time.time()
        
        # Respect target switch delay
        if current_time - self.last_target_switch < self.target_switch_delay:
            return
        
        # Find highest priority target
        best_target = None
        best_priority = 0.0
        
        for target in self.available_targets:
            priority = self.target_priorities.get(target.id, 0.0)
            if priority > best_priority:
                best_priority = priority
                best_target = target
        
        # Only switch if significantly better or no current target
        if best_target and (not self.current_target or 
                           best_priority > self.target_priorities.get(self.current_target.id, 0.0) * 1.2):
            self._lock_target(best_target)
    
    def _lock_target(self, target: Target):
        """Lock onto a new target"""
        current_time = time.time()
        
        # Release previous target
        if self.current_target:
            self._release_target()
        
        # Lock new target
        self.current_target = target
        self.target_locked = True
        self.lock_start_time = current_time
        self.last_target_switch = current_time
        self.total_locks += 1
        
        # Initialize target history if needed
        if target.id not in self.target_history:
            self.target_history[target.id] = TargetHistory()
        
        self.target_history[target.id].lock_start_time = current_time
        
        # Reset aim accumulator for smooth transition
        self.aim_accumulator = np.array([0.0, 0.0])
        
        self.logger.debug(f"🎯 Locked onto target {target.id}")
    
    def _release_target(self):
        """Release current target"""
        if not self.current_target:
            return
        
        current_time = time.time()
        
        # Update statistics
        if self.current_target.id in self.target_history:
            history = self.target_history[self.current_target.id]
            lock_duration = current_time - history.lock_start_time
            history.lock_duration += lock_duration
            
            # Update average lock time
            self.average_lock_time = (
                (self.average_lock_time * (self.total_locks - 1) + lock_duration) / 
                self.total_locks if self.total_locks > 0 else lock_duration
            )
        
        self.logger.debug(f"🎯 Released target {self.current_target.id}")
        
        self.current_target = None
        self.target_locked = False
        self.lock_start_time = 0.0
    
    def calculate_aim_adjustment(self) -> Optional[AimAdjustment]:
        """Calculate aim adjustment for current target"""
        if not self.current_target or not self.target_locked:
            return None
        
        current_time = time.time()
        target_id = self.current_target.id
        
        # Get target position (with prediction if enabled)
        target_pos = self._get_target_position_with_prediction()
        if target_pos is None:
            return None
        
        # Calculate required aim adjustment
        delta_x = target_pos[0]  # Horizontal adjustment
        delta_y = target_pos[1]  # Vertical adjustment
        
        # Apply lock strength
        delta_x *= self.lock_strength
        delta_y *= self.lock_strength
        
        # Apply smoothing
        dt = current_time - self.last_aim_time if self.last_aim_time > 0 else 0.016
        
        # Accumulate smooth movement
        self.aim_accumulator += np.array([delta_x, delta_y]) / self.smoothing_factor
        
        # Get smoothed adjustment
        smooth_delta = self.aim_accumulator / self.smoothing_factor
        
        # Apply decay to accumulator
        self.aim_accumulator *= 0.9
        
        # Apply fatigue and micro-adjustments
        smooth_delta = self._apply_humanization(smooth_delta, current_time)
        
        self.last_aim_time = current_time
        
        return AimAdjustment(
            delta_x=smooth_delta[0],
            delta_y=smooth_delta[1],
            confidence=self._calculate_confidence(),
            prediction_used=self.prediction_enabled,
            smoothing_applied=True
        )
    
    def _get_target_position_with_prediction(self) -> Optional[Tuple[float, float]]:
        """Get target position with movement prediction"""
        if not self.current_target:
            return None
        
        target_id = self.current_target.id
        history = self.target_history.get(target_id)
        
        # Base position
        base_pos = (self.current_target.x, self.current_target.y)
        
        # Apply prediction if enabled and we have velocity data
        if (self.prediction_enabled and history and history.velocities and 
            len(history.velocities) > 0):
            
            velocity = history.velocities[-1]
            predicted_pos = predict_position(
                base_pos, velocity[:2], self.prediction_time
            )
            
            # Update prediction accuracy
            self.total_predictions += 1
            # Note: Actual accuracy would be measured against real outcomes
            
            return predicted_pos
        
        return base_pos
    
    def _apply_humanization(self, delta: np.ndarray, current_time: float) -> np.ndarray:
        """Apply humanization effects to aim adjustment"""
        # Fatigue simulation
        lock_duration = current_time - self.lock_start_time
        self.fatigue_factor = max(0.7, 1.0 - (lock_duration / 60.0))  # Fatigue over 1 minute
        delta *= self.fatigue_factor
        
        # Micro adjustments
        if current_time - self.micro_adjustment_timer > np.random.uniform(0.1, 0.4):
            micro_x = np.random.uniform(-0.02, 0.02)
            micro_y = np.random.uniform(-0.02, 0.02)
            delta += np.array([micro_x, micro_y])
            self.micro_adjustment_timer = current_time
        
        # Natural drift
        self.natural_drift_phase += 0.1
        drift_x = math.sin(self.natural_drift_phase) * 0.01
        drift_y = math.cos(self.natural_drift_phase * 0.7) * 0.01
        delta += np.array([drift_x, drift_y])
        
        return delta
    
    def _calculate_confidence(self) -> float:
        """Calculate confidence in current aim adjustment"""
        if not self.current_target:
            return 0.0
        
        confidence = 1.0
        
        # Reduce confidence based on target distance
        distance = math.sqrt(
            self.current_target.x**2 + self.current_target.y**2 + self.current_target.z**2
        )
        if distance > self.max_lock_distance * 0.8:
            confidence *= 0.8
        
        # Reduce confidence based on target velocity
        history = self.target_history.get(self.current_target.id)
        if history and history.velocities:
            velocity_mag = np.linalg.norm(history.velocities[-1])
            if velocity_mag > 300:  # Fast moving target
                confidence *= 0.7
        
        # Apply fatigue factor
        confidence *= self.fatigue_factor
        
        return max(0.1, confidence)
    
    def _cleanup_old_histories(self, current_time: float):
        """Clean up old target histories"""
        cleanup_threshold = 30.0  # 30 seconds
        
        to_remove = []
        for target_id, history in self.target_history.items():
            if (history.timestamps and 
                current_time - history.timestamps[-1] > cleanup_threshold):
                to_remove.append(target_id)
        
        for target_id in to_remove:
            del self.target_history[target_id]
            if target_id in self.target_priorities:
                del self.target_priorities[target_id]
    
    def has_active_target(self) -> bool:
        """Check if there's an active target"""
        return self.target_locked and self.current_target is not None
    
    def is_new_target_lock(self) -> bool:
        """Check if this is a new target lock (for callbacks)"""
        return (self.target_locked and 
                time.time() - self.lock_start_time < 0.1)
    
    def release_all_targets(self):
        """Release all targets (emergency stop)"""
        self._release_target()
        self.available_targets.clear()
        self.target_history.clear()
        self.target_priorities.clear()
    
    def update_config(self, new_config: Dict[str, Any]):
        """Update configuration"""
        self.config.update(new_config)
        
        # Update parameters
        self.max_lock_distance = self.config.get('max_lock_distance', self.max_lock_distance)
        self.max_lock_fov = self.config.get('max_lock_fov', self.max_lock_fov)
        self.target_switch_delay = self.config.get('target_switch_delay', self.target_switch_delay)
        self.lock_strength = self.config.get('lock_strength', self.lock_strength)
        self.smoothing_factor = self.config.get('smoothing_factor', self.smoothing_factor)
        self.prediction_enabled = self.config.get('prediction_enabled', self.prediction_enabled)
        self.prediction_time = self.config.get('prediction_time', self.prediction_time)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get tracking statistics"""
        return {
            'total_locks': self.total_locks,
            'average_lock_time': self.average_lock_time,
            'prediction_accuracy': (self.successful_predictions / max(self.total_predictions, 1)),
            'current_target_id': self.current_target.id if self.current_target else None,
            'target_locked': self.target_locked,
            'fatigue_factor': self.fatigue_factor,
            'available_targets': len(self.available_targets)
        }
    
    def cleanup(self):
        """Cleanup tracker resources"""
        self.release_all_targets()
        self.logger.info("🎯 Sticky Target Tracker cleaned up")
