#!/usr/bin/env python3
"""
🖱️ MOUSE CONTROLLER
===================
Advanced mouse movement simulation with humanization and anti-detection features.
"""

import time
import math
import random
import logging
from typing import Tuple, Optional, Dict, Any, List
import numpy as np
import win32api
import win32con
import ctypes
from ctypes import wintypes

from .base_controller import BaseController, AimAdjustment
from ..utils.math_utils import interpolate_bezier, calculate_distance

class MouseController(BaseController):
    """
    Advanced mouse controller with humanized movement patterns.
    
    Features:
    - Bezier curve interpolation
    - Human-like acceleration/deceleration
    - Micro-movements and jitter
    - Anti-detection timing
    - Multiple movement algorithms
    - Performance optimization
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the mouse controller"""
        super().__init__(config)
        self.logger = logging.getLogger(__name__)
        
        # Movement configuration
        self.sensitivity = self.config.get('sensitivity', 1.0)
        self.smoothing_enabled = self.config.get('smoothing_enabled', True)
        self.bezier_enabled = self.config.get('bezier_enabled', True)
        self.humanization_enabled = self.config.get('humanization_enabled', True)
        
        # Timing configuration
        self.min_move_time = self.config.get('min_move_time', 0.01)
        self.max_move_time = self.config.get('max_move_time', 0.2)
        self.movement_steps = self.config.get('movement_steps', 10)
        
        # Humanization parameters
        self.jitter_amount = self.config.get('jitter_amount', 0.5)
        self.overshoot_chance = self.config.get('overshoot_chance', 0.1)
        self.pause_chance = self.config.get('pause_chance', 0.05)
        self.micro_correction_chance = self.config.get('micro_correction_chance', 0.3)
        
        # Anti-detection features
        self.randomize_timing = self.config.get('randomize_timing', True)
        self.vary_acceleration = self.config.get('vary_acceleration', True)
        self.natural_arcs = self.config.get('natural_arcs', True)
        
        # Performance tracking
        self.movement_history = []
        self.last_movement_time = 0.0
        self.total_movements = 0
        self.average_movement_time = 0.0
        
        # Windows API setup
        self._setup_windows_api()
        
        self.logger.info("🖱️ Mouse Controller initialized")
    
    def _setup_windows_api(self):
        """Setup Windows API for mouse control"""
        try:
            # Get user32.dll
            self.user32 = ctypes.windll.user32
            
            # Define INPUT structure
            class POINT(ctypes.Structure):
                _fields_ = [("x", ctypes.c_long), ("y", ctypes.c_long)]
            
            class MOUSEINPUT(ctypes.Structure):
                _fields_ = [("dx", wintypes.LONG),
                           ("dy", wintypes.LONG),
                           ("mouseData", wintypes.DWORD),
                           ("dwFlags", wintypes.DWORD),
                           ("time", wintypes.DWORD),
                           ("dwExtraInfo", ctypes.POINTER(wintypes.ULONG))]
            
            class INPUT(ctypes.Structure):
                class _INPUT(ctypes.Union):
                    _fields_ = [("mi", MOUSEINPUT)]
                _anonymous_ = ("_input",)
                _fields_ = [("type", wintypes.DWORD),
                           ("_input", _INPUT)]
            
            self.INPUT = INPUT
            self.MOUSEINPUT = MOUSEINPUT
            
            # Constants
            self.INPUT_MOUSE = 0
            self.MOUSEEVENTF_MOVE = 0x0001
            self.MOUSEEVENTF_ABSOLUTE = 0x8000
            
            self.logger.debug("✅ Windows API setup complete")
            
        except Exception as e:
            self.logger.error(f"❌ Windows API setup failed: {e}")
    
    def apply_aim_adjustment(self, adjustment: AimAdjustment) -> bool:
        """Apply aim adjustment using mouse movement"""
        try:
            start_time = time.time()
            
            # Convert adjustment to pixel movement
            pixel_delta = self._adjustment_to_pixels(adjustment)
            
            if abs(pixel_delta[0]) < 0.5 and abs(pixel_delta[1]) < 0.5:
                return True  # Movement too small, skip
            
            # Choose movement algorithm
            if self.bezier_enabled and self._should_use_bezier(pixel_delta):
                success = self._move_with_bezier(pixel_delta)
            else:
                success = self._move_linear(pixel_delta)
            
            # Record performance
            movement_time = time.time() - start_time
            self._record_movement(pixel_delta, movement_time, success)
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Mouse movement error: {e}")
            return False
    
    def _adjustment_to_pixels(self, adjustment: AimAdjustment) -> Tuple[float, float]:
        """Convert aim adjustment to pixel coordinates"""
        # Apply sensitivity scaling
        pixel_x = adjustment.delta_x * self.sensitivity
        pixel_y = adjustment.delta_y * self.sensitivity
        
        # Apply DPI scaling if configured
        dpi_scale = self.config.get('dpi_scale', 1.0)
        pixel_x *= dpi_scale
        pixel_y *= dpi_scale
        
        return (pixel_x, pixel_y)
    
    def _should_use_bezier(self, pixel_delta: Tuple[float, float]) -> bool:
        """Determine if Bezier curve should be used for this movement"""
        distance = math.sqrt(pixel_delta[0]**2 + pixel_delta[1]**2)
        
        # Use Bezier for larger movements
        bezier_threshold = self.config.get('bezier_threshold', 10.0)
        return distance > bezier_threshold
    
    def _move_with_bezier(self, pixel_delta: Tuple[float, float]) -> bool:
        """Move mouse using Bezier curve interpolation"""
        try:
            # Get current mouse position
            current_pos = win32api.GetCursorPos()
            start_pos = np.array([current_pos[0], current_pos[1]], dtype=float)
            end_pos = start_pos + np.array(pixel_delta)
            
            # Generate control points for natural curve
            control_points = self._generate_bezier_control_points(start_pos, end_pos)
            
            # Calculate movement timing
            distance = np.linalg.norm(pixel_delta)
            move_time = self._calculate_movement_time(distance)
            steps = max(int(distance / 2), self.movement_steps)
            
            # Generate Bezier curve points
            curve_points = []
            for i in range(steps + 1):
                t = i / steps
                point = interpolate_bezier(control_points, t)
                curve_points.append(point)
            
            # Apply humanization to curve
            if self.humanization_enabled:
                curve_points = self._humanize_curve(curve_points)
            
            # Execute movement
            return self._execute_curve_movement(curve_points, move_time)
            
        except Exception as e:
            self.logger.error(f"❌ Bezier movement error: {e}")
            return False
    
    def _generate_bezier_control_points(self, start_pos: np.ndarray, 
                                      end_pos: np.ndarray) -> List[np.ndarray]:
        """Generate control points for Bezier curve"""
        # Calculate base control points
        direction = end_pos - start_pos
        distance = np.linalg.norm(direction)
        
        if distance == 0:
            return [start_pos, end_pos]
        
        # Normalize direction
        direction_norm = direction / distance
        
        # Create perpendicular vector for curve
        perpendicular = np.array([-direction_norm[1], direction_norm[0]])
        
        # Add some randomness for natural movement
        curve_strength = random.uniform(0.1, 0.3) * distance
        curve_direction = random.choice([-1, 1])
        
        # Generate control points
        control1 = start_pos + direction * 0.25 + perpendicular * curve_strength * curve_direction
        control2 = start_pos + direction * 0.75 + perpendicular * curve_strength * curve_direction * 0.5
        
        return [start_pos, control1, control2, end_pos]
    
    def _humanize_curve(self, curve_points: List[np.ndarray]) -> List[np.ndarray]:
        """Apply humanization effects to movement curve"""
        humanized_points = []
        
        for i, point in enumerate(curve_points):
            # Add jitter
            if self.jitter_amount > 0:
                jitter_x = random.uniform(-self.jitter_amount, self.jitter_amount)
                jitter_y = random.uniform(-self.jitter_amount, self.jitter_amount)
                point = point + np.array([jitter_x, jitter_y])
            
            # Add micro-pauses
            if i > 0 and random.random() < self.pause_chance:
                # Duplicate point to create pause
                humanized_points.append(curve_points[i-1].copy())
            
            humanized_points.append(point)
        
        # Add overshoot and correction
        if (len(humanized_points) > 1 and 
            random.random() < self.overshoot_chance):
            
            # Overshoot the target
            last_point = humanized_points[-1]
            direction = last_point - humanized_points[-2]
            overshoot = last_point + direction * random.uniform(0.1, 0.3)
            humanized_points.append(overshoot)
            
            # Correct back to target
            humanized_points.append(last_point)
        
        return humanized_points
    
    def _move_linear(self, pixel_delta: Tuple[float, float]) -> bool:
        """Move mouse using linear interpolation"""
        try:
            # Get current position
            current_pos = win32api.GetCursorPos()
            start_pos = np.array([current_pos[0], current_pos[1]], dtype=float)
            end_pos = start_pos + np.array(pixel_delta)
            
            # Calculate movement parameters
            distance = np.linalg.norm(pixel_delta)
            move_time = self._calculate_movement_time(distance)
            steps = max(int(distance / 3), 5)
            
            # Generate linear interpolation points
            points = []
            for i in range(steps + 1):
                t = i / steps
                # Apply easing for more natural movement
                t_eased = self._ease_in_out_cubic(t)
                point = start_pos + (end_pos - start_pos) * t_eased
                points.append(point)
            
            # Apply humanization
            if self.humanization_enabled:
                points = self._humanize_curve(points)
            
            # Execute movement
            return self._execute_curve_movement(points, move_time)
            
        except Exception as e:
            self.logger.error(f"❌ Linear movement error: {e}")
            return False
    
    def _ease_in_out_cubic(self, t: float) -> float:
        """Cubic easing function for natural acceleration/deceleration"""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2
    
    def _calculate_movement_time(self, distance: float) -> float:
        """Calculate appropriate movement time based on distance"""
        # Base time calculation
        base_time = distance / 1000.0  # Adjust based on desired speed
        
        # Apply bounds
        move_time = max(self.min_move_time, min(base_time, self.max_move_time))
        
        # Add randomization for anti-detection
        if self.randomize_timing:
            variation = move_time * 0.2  # 20% variation
            move_time += random.uniform(-variation, variation)
            move_time = max(self.min_move_time, move_time)
        
        return move_time
    
    def _execute_curve_movement(self, points: List[np.ndarray], total_time: float) -> bool:
        """Execute movement along curve points"""
        if len(points) < 2:
            return True
        
        try:
            step_time = total_time / (len(points) - 1)
            
            for i, point in enumerate(points[1:], 1):
                # Calculate timing variation
                if self.vary_acceleration:
                    # Vary timing based on position in curve
                    progress = i / (len(points) - 1)
                    timing_factor = self._get_timing_factor(progress)
                    current_step_time = step_time * timing_factor
                else:
                    current_step_time = step_time
                
                # Move to point
                self._move_to_point(point)
                
                # Wait before next movement
                if i < len(points) - 1:  # Don't wait after last point
                    time.sleep(current_step_time)
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Curve execution error: {e}")
            return False
    
    def _get_timing_factor(self, progress: float) -> float:
        """Get timing factor based on movement progress"""
        # Slower at start and end, faster in middle (natural acceleration)
        if progress < 0.3:
            return 1.5 - progress  # Slow start
        elif progress > 0.7:
            return 0.5 + (progress - 0.7) * 2  # Slow end
        else:
            return 0.8  # Faster middle
    
    def _move_to_point(self, point: np.ndarray):
        """Move mouse to specific point"""
        try:
            # Round to integer coordinates
            x = int(round(point[0]))
            y = int(round(point[1]))
            
            # Use Windows API for precise movement
            if hasattr(self, 'user32'):
                self.user32.SetCursorPos(x, y)
            else:
                # Fallback to win32api
                win32api.SetCursorPos((x, y))
                
        except Exception as e:
            self.logger.error(f"❌ Point movement error: {e}")
    
    def _record_movement(self, pixel_delta: Tuple[float, float], 
                        movement_time: float, success: bool):
        """Record movement for performance tracking"""
        self.total_movements += 1
        
        # Update average movement time
        self.average_movement_time = (
            (self.average_movement_time * (self.total_movements - 1) + movement_time) /
            self.total_movements
        )
        
        # Record in history
        movement_record = {
            'timestamp': time.time(),
            'delta': pixel_delta,
            'time': movement_time,
            'success': success,
            'distance': math.sqrt(pixel_delta[0]**2 + pixel_delta[1]**2)
        }
        
        self.movement_history.append(movement_record)
        
        # Keep history manageable
        if len(self.movement_history) > 100:
            self.movement_history = self.movement_history[-100:]
        
        self.last_movement_time = time.time()
    
    def simulate_click(self, button: str = 'left') -> bool:
        """Simulate mouse click"""
        try:
            if button == 'left':
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                time.sleep(random.uniform(0.01, 0.05))  # Human-like click duration
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
            elif button == 'right':
                win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, 0)
                time.sleep(random.uniform(0.01, 0.05))
                win32api.mouse_event(win32con.MOUSEEVENTF_RIGHTUP, 0, 0, 0, 0)
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Click simulation error: {e}")
            return False
    
    def get_current_position(self) -> Tuple[int, int]:
        """Get current mouse position"""
        try:
            return win32api.GetCursorPos()
        except Exception as e:
            self.logger.error(f"❌ Position query error: {e}")
            return (0, 0)
    
    def reset(self):
        """Reset controller state"""
        # Clear movement history
        self.movement_history.clear()
        self.total_movements = 0
        self.average_movement_time = 0.0
        self.last_movement_time = 0.0
        
        self.logger.debug("🖱️ Mouse controller reset")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        recent_movements = [m for m in self.movement_history 
                          if time.time() - m['timestamp'] < 60.0]  # Last minute
        
        return {
            'total_movements': self.total_movements,
            'average_movement_time': self.average_movement_time,
            'recent_movements': len(recent_movements),
            'last_movement_time': self.last_movement_time,
            'average_distance': (
                sum(m['distance'] for m in recent_movements) / len(recent_movements)
                if recent_movements else 0
            ),
            'success_rate': (
                sum(1 for m in recent_movements if m['success']) / len(recent_movements)
                if recent_movements else 1.0
            )
        }
    
    def update_config(self, new_config: Dict[str, Any]):
        """Update configuration"""
        self.config.update(new_config)
        
        # Update parameters
        self.sensitivity = self.config.get('sensitivity', self.sensitivity)
        self.smoothing_enabled = self.config.get('smoothing_enabled', self.smoothing_enabled)
        self.bezier_enabled = self.config.get('bezier_enabled', self.bezier_enabled)
        self.humanization_enabled = self.config.get('humanization_enabled', self.humanization_enabled)
        
        self.logger.debug("🖱️ Mouse controller configuration updated")
    
    def cleanup(self):
        """Cleanup controller resources"""
        self.reset()
        self.logger.info("🖱️ Mouse Controller cleaned up")
